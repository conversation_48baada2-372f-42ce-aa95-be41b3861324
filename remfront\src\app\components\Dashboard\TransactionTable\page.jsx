// components/TransactionTable.js
"use client";
import React from "react";
import styles from "./transactionTable.module.css";

const page = ({ date = "", currency, traderId, orderId, amount, status }) => {
  // Format date properly
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      return dateString;
    }
  };

  // Format amount with proper currency symbol
  const formatAmount = (amount, currency) => {
    if (!amount) return "0.00";
    const numAmount = parseFloat(amount);
    return numAmount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  // Get status styling
  const getStatusClass = (status) => {
    if (!status) return styles.statusDefault;
    const statusLower = status.toLowerCase();
    if (statusLower.includes('completed') || statusLower.includes('success')) {
      return styles.statusSuccess;
    } else if (statusLower.includes('pending') || statusLower.includes('processing')) {
      return styles.statusPending;
    } else if (statusLower.includes('failed') || statusLower.includes('cancelled')) {
      return styles.statusFailed;
    }
    return styles.statusDefault;
  };

  // Get transaction type icon
  const getTransactionIcon = (status) => {
    const statusLower = status?.toLowerCase() || '';
    if (statusLower.includes('completed') || statusLower.includes('success')) {
      return '✓';
    } else if (statusLower.includes('pending') || statusLower.includes('processing')) {
      return '⏳';
    } else if (statusLower.includes('failed') || statusLower.includes('cancelled')) {
      return '✗';
    }
    return '•';
  };

  return (
    <>
      {/* Desktop Table Row */}
      <tr className={styles.tableRow}>
        <td className={styles.tableCell}>
          <div className={styles.dateContainer}>
            <div className={styles.dateText}>{formatDate(date)}</div>
            <div className={styles.timeText}>
              {date ? new Date(date).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
              }) : ''}
            </div>
          </div>
        </td>
        <td className={styles.tableCell}>
          <div className={styles.idContainer}>
            <span className={styles.idLabel}>#{orderId || 'N/A'}</span>
          </div>
        </td>
        <td className={styles.tableCell}>
          <div className={styles.idContainer}>
            <span className={styles.traderIdText}>{traderId || 'N/A'}</span>
          </div>
        </td>
        <td className={styles.tableCell}>
          <div className={styles.amountContainer}>
            <span className={styles.amountText}>
              {formatAmount(amount, currency)}
            </span>
            <span className={styles.currencyText}>{currency || 'USD'}</span>
          </div>
        </td>
        <td className={styles.tableCell}>
          <div className={styles.currencyContainer}>
            <span className={styles.currencyBadge}>{currency || 'USD'}</span>
          </div>
        </td>
        <td className={styles.tableCell}>
          <div className={`${styles.statusBadge} ${getStatusClass(status)}`}>
            <span className={styles.statusIcon}>{getTransactionIcon(status)}</span>
            <span className={styles.statusText}>{status || 'Unknown'}</span>
          </div>
        </td>
      </tr>

      {/* Mobile Card View */}
      <div className={styles.mobileCard}>
        <div className={styles.mobileCardHeader}>
          <div className={styles.mobileTransactionInfo}>
            <div className={styles.mobileOrderId}>#{orderId || 'N/A'}</div>
            <div className={styles.mobileDateText}>{formatDate(date)}</div>
          </div>
          <div className={`${styles.mobileStatusBadge} ${getStatusClass(status)}`}>
            <span className={styles.statusIcon}>{getTransactionIcon(status)}</span>
            <span className={styles.statusText}>{status || 'Unknown'}</span>
          </div>
        </div>

        <div className={styles.mobileCardBody}>
          <div className={styles.mobileAmountSection}>
            <div className={styles.mobileAmountContainer}>
              <span className={styles.mobileAmountText}>
                {formatAmount(amount, currency)}
              </span>
              <span className={styles.mobileCurrencyText}>{currency || 'USD'}</span>
            </div>
            <div className={styles.mobileTraderInfo}>
              <span className={styles.mobileTraderLabel}>Trader ID:</span>
              <span className={styles.mobileTraderValue}>{traderId || 'N/A'}</span>
            </div>
          </div>
        </div>

        <div className={styles.mobileCardFooter}>
          <div className={styles.mobileMetadata}>
            <div className={styles.mobileMetaItem}>
              <span className={styles.mobileMetaLabel}>Time:</span>
              <span className={styles.mobileMetaValue}>
                {date ? new Date(date).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: '2-digit'
                }) : 'N/A'}
              </span>
            </div>
            <div className={styles.mobileMetaItem}>
              <span className={styles.mobileMetaLabel}>Currency:</span>
              <span className={`${styles.mobileMetaValue} ${styles.mobileCurrencyBadge}`}>
                {currency || 'USD'}
              </span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default page;
