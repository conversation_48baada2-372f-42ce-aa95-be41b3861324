import React, { useState, useEffect, useRef } from "react";
import { useLocalSearchParams } from "expo-router";
import {
  SafeAreaView,
  ScrollView,
  View,
  Text,
  TextInput,
  TouchableOpacity,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import "../global.css";
import customFetchWithToken from "./utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";
import { sources, businesses } from "../app/utils/constants";
import { useRouter } from "expo-router";
import * as SecureStore from "expo-secure-store";

type QueryParams = {
  firstname?: string;
  lastname?: string;
  email?: string;
};

interface Country {
  country_name: string;
  id: number;
}

interface PhoneArr {
  country_name: string; // Full name of the country
  country_short_name: string; // ISO short name or abbreviation
  flag: string; // Path or identifier for the flag
  phone_code: string; // International phone code as a string
}

const PersonalDetails = () => {
  const params = useLocalSearchParams<QueryParams>();
  // console.log("params", params);
  const router = useRouter();
  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green", width: "90%" }} // Adjust width to 100%
        contentContainerStyle={{ paddingHorizontal: 5 }}
        text1Style={{
          fontSize: 14,
          fontWeight: "bold",
          marginLeft: 20,
        }}
        text2Style={{
          fontSize: 29,
        }}
      />
    ),
    // You can add similar customizations for 'error' and 'info' types if needed
  };
  const showToastSuccess = (message: string) => {
    Toast.show({
      type: "success", // can also be 'error' or 'info'
      text1: message,
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };
  const showToastError = (message: string) => {
    Toast.show({
      type: "error", // can also be 'error' or 'info'
      text1: message,
      // text2: "This is a toast message 👋",
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };
  const [personalBtnActive, setPersonalBtnActive] = useState(true);
  const [empBtnActive, setEmpBtnActive] = useState(false);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [emailOTP, setEmailOTP] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [phoneOTP, setPhoneOTP] = useState("");
  const [dob, setDob] = useState("");
  const [citizenship, setCitizenship] = useState<Country[]>([]);
  const [gender, setGender] = useState("");
  const [address, setAddress] = useState("");
  const [nameOfBusiness, setNameOfBusiness] = useState("");
  const [natureOfBusiness, setNatureOfBusiness] = useState("");
  const [sourceOfFunds, setSourceOfFunds] = useState("");
  const [phoneCountryArray, setPhoneCountryArray] = useState<PhoneArr[]>([]);
  const [countryPhone, setCountryPhone] = useState<PhoneArr>();
  const [countryOfBusiness, setCountryOfBusiness] = useState<any>("");
  const [emailOtpVerified, setEmailOtpVerified] = useState(null);
  const [mobileOtpVerified, setMobileOtpVerified] = useState(null);
  console.log("sourceOfFunds", sourceOfFunds);
  // apiStates
  const [countriesFetched, setCountriesFetched] = useState<Country[]>([]);
  // apiStates
  const phnCountryRef = useRef(true);

  const getPhoneCountryDropDown = async () => {
    try {
      if (phnCountryRef.current == false) return;
      const res = await customFetchWithToken.get("/country-code/");
      setPhoneCountryArray(res.data.data);
      phnCountryRef.current = false;
    } catch (error: any) {
      console.error(error);
    }
  };

  const fetchData = async (): Promise<void> => {
    try {
      const res = await customFetchWithToken.get("/country-list/");
      setCountriesFetched(res.data.data);
    } catch (error: any) {
      console.log(error);
    }
  };

  // email and mobile OTP

  const validateEmail = (email: string): boolean => {
    // Regular expression for email validation
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  };

  const sendEmailReq = async () => {
    try {
      if (!validateEmail(email)) {
        showToastError("Enter a valid email address");

        return;
      }
      const res = await customFetchWithToken.post("/email-otp/", {
        email: email,
      });
      console.log(res);
      if (res.status === 200) {
        console.log(res);
        showToastSuccess(res.data.message);
      } else {
        showToastError("Registration failed.");
      }
    } catch (error: any) {
      console.error("Error:", error);
      console.log(error);
      showToastError(error.response.data.message);
    }
  };
  // email OTP

  //email otp verify
  const confirmEmailOtpPayload = {
    email: email,
    otp: Number(emailOTP),
  };
  const verifyEmail = async () => {
    try {
      const res = await customFetchWithToken.post(
        "/email-verify-otp/",
        confirmEmailOtpPayload
      );
      if (res.status === 200) {
        console.log(res.status);
        showToastSuccess(res.data.message);
      } else {
        showToastError("Incorrect Email OTP");
      }
    } catch (error: any) {
      console.error("Error:", error);
      console.log(error);
      showToastError(error.response.data.message);
    }
  };

  //email otp verify

  const sendPhnOTPReq = async () => {
    try {
      const res = await customFetchWithToken.post("/mobile-otp/", {
        mobile: `+${countryPhone}${phoneNumber}`,
      });
      console.log(res);
      if (res.status === 200) {
        console.log(res);
        showToastSuccess("OTP Sent to phone. Check Inbox");
      } else if (res.status === 429) {
        console.log(res.status);
        showToastSuccess("exceeded the request limit");
      } else {
        showToastError("Failed to send OTP");
      }
    } catch (error: any) {
      console.log(error);
      showToastError(error.response.data.message);
    }
  };

  const mobileOtpVerifyPayload = {
    mobile: `+${countryPhone}${phoneNumber}`,
    otp: String(phoneOTP),
  };
  const confirmPhnOTP = async () => {
    try {
      const res = await customFetchWithToken.post(
        "/mobile-verify-otp/",
        mobileOtpVerifyPayload
      );
      console.log(res);
      if (res.status === 200) {
        console.log(res.status);
        showToastSuccess(res.data.message);
      } else {
        showToastError("Failed to send OTP");
      }
    } catch (error: any) {
      console.error("Error:", error);
      console.log(error);
      showToastError(error.response.data.message);
    }
  };
  // email and mobile OTP

  //formSubmission

  const HandleSubmitUserForm = async () => {
    if (!sourceOfFunds) {
      showToastError("Select a source of fund");
      return;
    }
    if (sourceOfFunds === "Salaried" && !nameOfBusiness) {
      showToastError("Name of business is required.");
      return;
    }
    if (sourceOfFunds === "Salaried" && !natureOfBusiness) {
      showToastError("Nature of business is required.");
      return;
    }
    if (sourceOfFunds === "Salaried" && !countryOfBusiness) {
      showToastError("Country of business is required.");
      return;
    }

    // const requiredFields = {
    //   address: "Address",
    //   dob: "Date of birth",
    //   citizenship: "Citizenship",
    //   gender: "Gender",
    //   sourceOfFunds: "Source of Funds",
    // };

    // for (const [field, label] of Object.entries(requiredFields)) {
    //   if (!eval(field)) {
    //     showToastError(`${label} is required.`);
    //     return;
    //   }
    // }

    const Data = {
      email: email,
      firstName: firstName,
      lastName: lastName,
      emailOTP: String(emailOTP),
      phoneNumber: `${phoneNumber}`,
      phoneOTP: String(phoneOTP),
      dob: dob,
      citizenship: citizenship,
      gender: gender,
      address: address,
      natureOfBusiness: natureOfBusiness,
      nameOfBusiness: nameOfBusiness,
      sourceOfFunds: sourceOfFunds,
      countryOfBusiness: countryOfBusiness,
    };
    console.log("Data", Data);

    try {
      const res = await customFetchWithToken.post("/user-details/", Data);

      console.log("res", res);
      if (res.status === 200) {
        console.log(res.status);
        showToastSuccess(res.data.message);
        // setTimeout(() => router.push("/pages/searchads"), 1500);
        setTimeout(() => router.push("/status"), 1500);
      } else {
        showToastError("Registration failed.");
      }
    } catch (error: any) {
      console.error("USERError:", error);

      showToastError(error.response.data.message);
    }
  };

  async function getValueFor(key: any) {
    let result = await SecureStore.getItemAsync(key);
    if (result) {
      alert("🔐 Here's your value 🔐 \n" + result);
    } else {
      alert("No values stored under that key.");
    }
  }

  const getCode = () => {
    getValueFor("userEmail");
    getValueFor("user");
    // ToastAndroid.show("Request sent successfully!", ToastAndroid.TOP);
  };

  // useEffect(() => {
  //   getCode();
  // }, []);
  const getUserRegistration = async () => {
    try {
      const res = await customFetchWithToken.get("/view-user-details/");
      console.log("userData", res.data.data.mobile);
      if (res.status == 200) {
        setEmail(res.data.data.email);
        setFirstName(res.data.data.firstname);
        setLastName(res.data.data.lastname);
        setPhoneNumber(res.data.data.mobile);
        setDob(res.data.data.dob);
        setCitizenship(res.data.data.country);
        setGender(res.data.data.gender);
        setAddress(res.data.data.address);
        setSourceOfFunds(res.data.data.source_of_funds);
        setNatureOfBusiness(res.data.data.nature_of_business);
        setNameOfBusiness(res.data.data.name_of_business);
        setCountryOfBusiness(res.data.data.countryofbusiness);
        setEmailOtpVerified(res.data.data.email_verified);
        setMobileOtpVerified(res.data.data.mobile_verified);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updateParamQueries = (params: QueryParams) => {
    setFirstName(params.firstname || "");
    setLastName(params.lastname || "");
    setEmail(params.email || "");
  };

  useEffect(() => {
    getPhoneCountryDropDown();
    fetchData();
    updateParamQueries(params);
    getUserRegistration();
  }, []);

  const handlePersonalBtn = () => {
    setEmpBtnActive(false);
    setPersonalBtnActive(true);
  };

  const handleEmpBtn = () => {
    setPersonalBtnActive(false);
    setEmpBtnActive(true);
  };

  useEffect(() => {
    setCountryOfBusiness(citizenship);
  }, [citizenship]);

  return (
    <SafeAreaView className="flex-1">
      <ScrollView className="p-5">
        <View className="w-full">
          {/* Button container */}
          <View className="flex-row justify-start mb-5">
            <TouchableOpacity
              className={`${
                personalBtnActive ? "bg-blue-500" : "bg-black"
              } py-3 px-4 rounded mr-2`} // Added margin between buttons
              onPress={handlePersonalBtn}
            >
              <Text className="text-white">1. Personal</Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`${
                empBtnActive ? "bg-blue-500" : "bg-black"
              } py-3 px-4 rounded`}
              onPress={handleEmpBtn}
            >
              <Text className="text-white">2. Income</Text>
            </TouchableOpacity>
          </View>

          {personalBtnActive ? (
            <>
              <View className="mb-5">
                <Text className="text-lg mb-2">First Name*</Text>
                <TextInput
                  className="h-12 border border-gray-300 rounded-md px-4"
                  onChangeText={setFirstName}
                  value={firstName}
                />
              </View>
              <View className="mb-5">
                <Text className="text-lg mb-2">Last Name*</Text>
                <TextInput
                  className="h-12 border border-gray-300 rounded-md px-4"
                  onChangeText={setLastName}
                  value={lastName}
                />
              </View>
              <View className="mb-5">
                <Text className="text-lg mb-2">Email Address*</Text>
                <View className="flex-row items-center">
                  <TextInput
                    className="flex-1 h-12 border border-gray-300 rounded-md px-4 mr-2"
                    onChangeText={setEmail}
                    value={email}
                  />
                  {!emailOtpVerified ? (
                    <TouchableOpacity
                      className="bg-black py-3 px-4 rounded-md"
                      onPress={sendEmailReq}
                    >
                      <Text className="text-white">Send OTP</Text>
                    </TouchableOpacity>
                  ) : (
                    ""
                  )}
                </View>
              </View>
              {/* Email OTP Field */}
              <View className="mb-5">
                <Text className="text-lg mb-2">Email OTP*</Text>
                <View className="flex-row items-center">
                  <TextInput
                    className="flex-1 h-12 border border-gray-300 rounded-md px-4 mr-2"
                    onChangeText={setEmailOTP}
                    value={!emailOtpVerified ? emailOTP : "Verified"}
                  />
                  {!emailOtpVerified ? (
                    <TouchableOpacity
                      className="bg-black py-3 px-4 rounded-md"
                      onPress={verifyEmail}
                    >
                      <Text className="text-white">Confirm</Text>
                    </TouchableOpacity>
                  ) : (
                    ""
                  )}
                </View>
              </View>

              <View className="mb-5 relative">
                <Text className="text-lg mb-2">
                  Phone Number* (Don't enter "0" or any country codes)
                </Text>
                <View className="flex-row items-center">
                  {/* Dropdown */}
                  {!mobileOtpVerified ? (
                    <View
                      style={{
                        width: 140,
                        height: 48,
                        borderWidth: 1,
                        borderColor: "#ccc",
                        borderRadius: 8,
                        zIndex: 99,
                      }}
                    >
                      <Picker
                        selectedValue={countryPhone}
                        onValueChange={(itemValue) =>
                          setCountryPhone(itemValue)
                        }
                        style={{ height: 50 }} // Set appropriate height for the Picker
                      >
                        <Picker.Item label="Select Country Code" value="" />
                        {phoneCountryArray.map((el, index) => (
                          <Picker.Item
                            key={index}
                            label={`${el.country_name} +${el.phone_code}`}
                            value={`${el.phone_code}`}
                          />
                        ))}
                      </Picker>
                    </View>
                  ) : (
                    ""
                  )}
                  {/* Text Input */}
                  <TextInput
                    className="flex-1 h-12 border border-gray-300 rounded-md px-4 mr-2"
                    onChangeText={setPhoneNumber}
                    value={phoneNumber}
                  />
                  {/* Button */}
                  {!mobileOtpVerified ? (
                    <TouchableOpacity
                      className="bg-black py-3 px-4 rounded-md"
                      onPress={sendPhnOTPReq}
                    >
                      <Text className="text-white">Get OTP</Text>
                    </TouchableOpacity>
                  ) : (
                    ""
                  )}
                </View>
              </View>

              <View className="mb-5">
                <Text className="text-lg mb-2">OTP (Phone)*</Text>
                <View className="flex-row items-center">
                  <TextInput
                    className="flex-1 h-12 border border-gray-300 rounded-md px-4 mr-2"
                    onChangeText={setPhoneOTP}
                    value={!mobileOtpVerified ? phoneOTP : "Verified"}
                  />
                  {!mobileOtpVerified ? (
                    <TouchableOpacity
                      className="bg-black py-3 px-4 rounded-md"
                      onPress={confirmPhnOTP}
                    >
                      <Text className="text-white">Confirm</Text>
                    </TouchableOpacity>
                  ) : (
                    ""
                  )}
                </View>
              </View>

              <View className="mb-5">
                <Text className="text-lg mb-2">Date of Birth*</Text>
                <TextInput
                  className="h-12 border border-gray-300 rounded-md px-4"
                  onChangeText={setDob}
                  value={dob}
                  placeholder="YYYY-MM-DD"
                />
              </View>
              <View className="mb-5">
                <Text className="text-lg mb-2">Citizenship*</Text>
                <View className="border border-gray-300 rounded-md">
                  <Picker
                    selectedValue={citizenship}
                    onValueChange={(itemValue) => setCitizenship(itemValue)}
                    className="h-12 px-4"
                  >
                    <Picker.Item label="Select Citizenship" value="" />
                    {countriesFetched.map((el, index) => (
                      <Picker.Item
                        key={index}
                        label={el.country_name}
                        value={el.country_name}
                      />
                    ))}
                  </Picker>
                </View>
              </View>
              <View className="mb-5">
                <Text className="text-lg mb-2">Gender*</Text>
                <View className="border border-gray-300 rounded-md">
                  <Picker
                    selectedValue={gender}
                    onValueChange={(itemValue) => setGender(itemValue)}
                    className="h-12 px-4"
                  >
                    <Picker.Item label="Select Gender" value="" />
                    <Picker.Item label="Male" value="Male" />
                    <Picker.Item label="Female" value="Female" />
                  </Picker>
                </View>
              </View>
              <View className="mb-5">
                <Text className="text-lg mb-2">
                  Address (max 100 characters)
                </Text>
                <TextInput
                  className="h-12 border border-gray-300 rounded-md px-4"
                  onChangeText={setAddress}
                  value={address}
                />
              </View>
              <View className="flex items-center">
                <TouchableOpacity
                  className="bg-blue-500 py-3 px-5 rounded-md w-full mb-12"
                  onPress={handleEmpBtn}
                >
                  <Text className="text-white text-lg text-center">Next</Text>
                </TouchableOpacity>
              </View>
            </>
          ) : (
            <>
              <View className="mb-5">
                <Text className="text-lg mb-2">Source of Funds*</Text>
                <View className="border border-gray-300 rounded-md">
                  <Picker
                    selectedValue={sourceOfFunds}
                    onValueChange={(itemValue) => setSourceOfFunds(itemValue)}
                    className="h-12 px-4"
                  >
                    <Picker.Item label="Select Source" value="" />
                    {sources.map((el, index) => (
                      <Picker.Item
                        key={index}
                        label={el.name}
                        value={el.name}
                      />
                    ))}
                    <Picker.Item label="Investments" value="Investments" />
                  </Picker>
                </View>
              </View>

              {sourceOfFunds === "Salaried" ||
              sourceOfFunds === "Investments" ||
              sourceOfFunds === "Dividends or Profits from company" ? (
                <>
                  <View className="mb-5">
                    <Text className="text-lg mb-2">Nature of Business*</Text>
                    <View className="border border-gray-300 rounded-md">
                      <Picker
                        selectedValue={natureOfBusiness}
                        onValueChange={(itemValue) =>
                          setNatureOfBusiness(itemValue)
                        }
                        className="h-12 px-4"
                      >
                        <Picker.Item label="Select Nature" value="" />
                        {businesses.map((el, index) => (
                          <Picker.Item
                            key={index}
                            label={el.name}
                            value={el.name}
                          />
                        ))}
                      </Picker>
                    </View>
                  </View>

                  <View className="mb-5">
                    <Text className="text-lg mb-2">
                      Name of Business* (max 50 characters)
                    </Text>
                    <TextInput
                      className="h-12 border border-gray-300 rounded-md px-4"
                      onChangeText={setNameOfBusiness}
                      value={nameOfBusiness}
                    />
                  </View>
                </>
              ) : (
                ""
              )}

              <View className="flex-row justify-between">
                <TouchableOpacity
                  className="bg-blue-500 py-3 px-5 rounded-md"
                  onPress={handlePersonalBtn}
                >
                  <Text className="text-white text-lg">Previous</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  className="bg-blue-500 py-3 px-5 rounded-md"
                  onPress={HandleSubmitUserForm}
                >
                  {/* <Link href="/status"> */}
                  <Text className="text-white text-lg">Submit</Text>
                  {/* </Link> */}
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </ScrollView>
      <Toast config={toastConfig} />
    </SafeAreaView>
  );
};

export default PersonalDetails;
