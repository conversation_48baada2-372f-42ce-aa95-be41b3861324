"use client";
import React, { useEffect, useState } from "react";
import styles from "./historyCard.module.css";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = ({
  id,
  orderNo,
  dateCreated,
  status = "Notified",
  trade_amount,
  indicative_fx_rate,
  listing_id,
  payin_currency_id__currency_code,
  payin_option_id__payment_method,
  payout_currency_id__currency_code,
  payout_option_id__payment_method,
  peer_id = "23",
  max_liquidity,
  min_liquidity,
  availableLiquidity,
  terms,
  finalTradeFee,
}) => {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  // Enhanced date formatting with error handling
  const formatDateTime = (dateString) => {
    if (!dateString) return { date: "N/A", time: "N/A", relative: "Unknown" };

    try {
      const date = new Date(dateString);
      const formattedDate = date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
      const formattedTime = date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
      });

      // Calculate relative time
      const now = new Date();
      const diffInHours = Math.floor((now - date) / (1000 * 60 * 60));
      let relative;

      if (diffInHours < 1) {
        relative = "Just now";
      } else if (diffInHours < 24) {  qwe4r56 
        relative = `${diffInHours}h ago`;
      } else {
        const diffInDays = Math.floor(diffInHours / 24);
        relative = `${diffInDays}d ago`;
      }

      return { date: formattedDate, time: formattedTime, relative };
    } catch (error) {
      console.error("Date formatting error:", error);
      return {
        date: "Invalid date",
        time: "Invalid time",
        relative: "Unknown",
      };
    }
  };

  const {
    date: formattedDate,
    time: formattedTime,
    relative: relativeTime,
  } = formatDateTime(dateCreated);



  const handleTradeById = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      setTimeout(() => {
        router.push(`/pages/trade/${orderNo}`);
      }, 500);
    } catch (error) {
      console.error("Error navigating to trade:", error);
      toast.error("Failed to load trade details. Please try again.");
      setIsProcessing(false);
    }
  };

  const handlePeerDecisionToTradeAccept = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    const payload = {
      order_id: orderNo,
    };
    try {
      const response = await customFetchWithToken.post(
        "/trade/accept-request/",
        payload
      );

      // Show immediate feedback
      toast.info("Processing trade acceptance...");
      if (response.status === 200) {
        setTimeout(() => {
          router.push(`/pages/trade/${orderNo}`);
        }, 400);
      } else {
        toast.error(response.data.message);
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error accepting trade:", error);
      toast.error("Failed to accept trade. Please try again.");
      setIsProcessing(false);
    }
  };

  const handlePeerDecisionToTradeReject = async () => {
    if (isProcessing) return;

    setIsProcessing(true);
    try {
      const payload = {
        order_id: orderNo,
      };
      toast.info("Processing trade rejection...");
      const response = await customFetchWithToken.post(
        "/trade/reject-request/",
        payload
      );
      if (response.status === 200) {
        setTimeout(() => {
          router.push(`/pages/trade/${orderNo}`);
        }, 400);
      } else {
        toast.error(response.data.message);
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error rejecting trade:", error);
      toast.error("Failed to reject trade. Please try again.");
      setIsProcessing(false);
    }
  };

  const getStatusConfig = (status) => {
    const statusMap = {
      pending: {
        class: styles.pending,
        icon: "⏳",
        label: "Pending Approval",
        description: "Waiting for peer response",
      },
      expired: {
        class: styles.expired,
        icon: "⏰",
        label: "Expired",
        description: "Trade request has expired",
      },
      ongoing: {
        class: styles.ongoing,
        icon: "🔄",
        label: "In Progress",
        description: "Trade is currently active",
      },
      rejected: {
        class: styles.rejected,
        icon: "❌",
        label: "Rejected",
        description: "Trade request was declined",
      },
      completed: {
        class: styles.completed,
        icon: "✅",
        label: "Completed",
        description: "Trade successfully finished",
      },
      notified: {
        class: styles.notified,
        icon: "🔔",
        label: "Notified",
        description: "Notification sent",
      },
      cancelled: {
        class: styles.cancelled,
        icon: "🚫",
        label: "Cancelled",
        description: "Trade was cancelled",
      },
    };

    return (
      statusMap[status.toLowerCase()] || {
        class: "",
        icon: "❓",
        label: status || "Unknown",
        description: "Status unknown",
      }
    );
  };

  const statusConfig = getStatusConfig(status);
  const formatCurrency = (amount, currency) => {
    if (!amount) return "N/A";
    return `${Number(amount).toLocaleString()} ${currency || ""}`;
  };

  return (
    <div className={styles.history_container}>
      {/* Status Header */}
      <div className={styles.statusHeader}>
        <div className={styles.orderInfo}>
          <span className={styles.orderLabel}>Order #{orderNo}</span>
          <span className={styles.dateLabel}>{formattedDate}</span>
          <time className={styles.relativeTime} title={`${formattedDate} at ${formattedTime}`}>
            {relativeTime}
          </time>
        </div>
        <div
          className={`${styles.statusBadge} ${statusConfig.class}`}
          title={statusConfig.description}
        >
          <span className={styles.statusIcon}>{statusConfig.icon}</span>
          {statusConfig.label}
        </div>
      </div>

      {/* Main Content */}
      <div className={styles.mainContent}>
        {/* Trade Amount Highlight */}
        <div className={styles.tradeAmountHighlight}>
          {formatCurrency(trade_amount, payin_currency_id__currency_code)}
          <svg
            width={12}
            height={12}
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 448 512"
          >
            <path
              fill="#64748b"
              d="M438.6 278.6c12.5-12.5 12.5-32.8 0-45.3l-160-160c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L338.8 224 32 224c-17.7 0-32 14.3-32 32s14.3 32 32 32l306.7 0L233.4 393.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0l160-160z"
            />
          </svg>
          {payout_currency_id__currency_code}
        </div>

        {/* Exchange Rate */}
        <div className={styles.exchangeRate}>
          Rate: {indicative_fx_rate
            ? `1 ${payin_currency_id__currency_code} = ${Number(
                indicative_fx_rate
              ).toFixed(4)} ${payout_currency_id__currency_code}`
            : "Rate not available"}
        </div>

        {/* Trade Details */}
        <div className={styles.tradeDetails}>
          <div className={styles.detailRow}>
            <span className={styles.detailLabel}>Listing ID:</span>
            <span className={styles.detailValue}>#{listing_id}</span>
          </div>
          <div className={styles.detailRow}>
            <span className={styles.detailLabel}>Available:</span>
            <span className={styles.detailValue}>{formatCurrency(availableLiquidity, payin_currency_id__currency_code)}</span>
          </div>
          <div className={styles.detailRow}>
            <span className={styles.detailLabel}>Range:</span>
            <span className={styles.detailValue}>
              {formatCurrency(min_liquidity, payin_currency_id__currency_code)} - {formatCurrency(max_liquidity, payin_currency_id__currency_code)}
            </span>
          </div>
          {finalTradeFee && (
            <div className={styles.detailRow}>
              <span className={styles.detailLabel}>Trade Fee:</span>
              <span className={styles.detailValue}>{finalTradeFee}%</span>
            </div>
          )}
        </div>

        {/* Payment Methods */}
        <div className={styles.paymentMethods}>
          <div className={styles.paymentMethod}>
            <span className={styles.methodLabel}>Pay with:</span>
            <span className={styles.methodValue}>{payin_option_id__payment_method}</span>
          </div>
          <div className={styles.paymentMethod}>
            <span className={styles.methodLabel}>Receive via:</span>
            <span className={styles.methodValue}>{payout_option_id__payment_method}</span>
          </div>
        </div>

        {peer_id && (
          <div className={styles.peerInfo}>
            <span className={styles.peerLabel}>Peer ID:</span>
            <span className={styles.peerValue}>#{peer_id}</span>
          </div>
        )}
      </div>

      {/* Action Buttons - Now handles all statuses like HistoryCard */}
      <div className={styles.actionSection}>
        {status.toLowerCase() === "ongoing" && (
          <button 
            onClick={handleTradeById} 
            className={styles.actionButton}
            disabled={isProcessing}
            aria-label={`Continue trade for order ${orderNo}`}
          >
            <span className={styles.buttonIcon}>🔄</span>
            {isProcessing ? "Loading..." : "Continue Trade"}
          </button>
        )}

        {status.toLowerCase() === "pending" && (
          <div className={styles.pendingActions}>
            <div className={styles.pendingMessage}>
              <span className={styles.messageIcon}>⏳</span>
              Trade Awaiting Response
            </div>
            <div className={styles.buttonGroup}>
              <button
                className={styles.acceptButton}
                onClick={handlePeerDecisionToTradeAccept}
                disabled={isProcessing}
                aria-label={`Accept trade request for order ${orderNo}`}
              >
                <span className={styles.buttonIcon}>✅</span>
                {isProcessing ? "Processing..." : "Accept"}
              </button>
              <button
                className={styles.rejectButton}
                onClick={handlePeerDecisionToTradeReject}
                disabled={isProcessing}
                aria-label={`Reject trade request for order ${orderNo}`}
              >
                <span className={styles.buttonIcon}>❌</span>
                {isProcessing ? "Processing..." : "Reject"}
              </button>
            </div>
          </div>
        )}

        {status.toLowerCase() === "completed" && (
          <div className={styles.completedActions}>
            <div className={styles.completedMessage}>
              <span className={styles.messageIcon}>✅</span>
              Trade Completed Successfully
            </div>
            <button
              onClick={handleTradeById}
              className={styles.secondaryButton}
              disabled={isProcessing}
              aria-label={`View details for completed trade ${orderNo}`}
            >
              <span className={styles.buttonIcon}>👁️</span>
              View Details
            </button>
          </div>
        )}

        {(status.toLowerCase() === "rejected" ||
          status.toLowerCase() === "expired" ||
          status.toLowerCase() === "cancelled") && (
          <div className={styles.inactiveActions}>
            <div className={styles.inactiveMessage}>
              <span className={styles.messageIcon}>
                {status.toLowerCase() === "rejected"
                  ? "❌"
                  : status.toLowerCase() === "expired"
                  ? "⏰"
                  : "🚫"}
              </span>
              Trade {status.charAt(0).toUpperCase() + status.slice(1)}
            </div>
          </div>
        )}
      </div>

      <ToastContainer />
    </div>
  );
};

export default page;
