@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Page Container */
.pageContainer {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 32px;
}

/* Page Header */
.pageHeader {
    text-align: center;
    margin-bottom: 32px;
    padding: 32px 0;
    display: none;
}

@media (max-width: 576px) {
    .pageHeader {
        display: block;
        margin-bottom: 20px;
        padding: 0 16px;
        text-align: center;
    }
}

.headerContent {
    max-width: 600px;
    margin: 0 auto;
}

.pageTitle {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0 0 8px 0;
    letter-spacing: -0.5px;
}

.pageSubtitle {
    font-size: 14px;
    color: #64748B;
    margin: 0;
    font-weight: 400;
    line-height: 1.4;
}

/* Profile Card */
.profileCard {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 32px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.profileCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    border-radius: 24px 24px 0 0;
}

.profileHeader {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 32px;
}

.profileIcon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.profileInfo {
    flex: 1;
}

.profileTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.profileDescription {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

/* Profile Content */
.profileContent {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

/* Profile Picture Section */
.profilePictureSection {
    display: flex;
    align-items: center;
    gap: 24px;
    padding: 24px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 20px;
}

.profilePictureContainer {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: rgba(226, 232, 240, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.profilePicture {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.profilePictureInfo {
    flex: 1;
}

.profileName {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.profileEmail {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 500;
}

/* Profile Details Grid */
.profileDetailsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.detailCard {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.detailCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: rgba(79, 70, 229, 0.3);
}

.detailIcon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    flex-shrink: 0;
}

.detailContent {
    flex: 1;
}

.detailLabel {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 4px;
}

.detailValue {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    word-break: break-word;
}

/* File Input */
.fileInput {
    display: none;
}

/* Peer Registration Card */
.peerCard {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 32px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.peerCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #10b981 0%, #059669 50%, #047857 100%);
    border-radius: 24px 24px 0 0;
}

.peerHeader {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 32px;
}

.peerIcon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.peerInfo {
    flex: 1;
}

.peerTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.peerDescription {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

.peerContent {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.peerGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.peerField {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.peerField:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border-color: rgba(16, 185, 129, 0.3);
}

.fieldIcon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    flex-shrink: 0;
    margin-top: 4px;
}

.fieldContent {
    flex: 1;
}

.fieldLabel {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.fieldInput {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 14px;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.fieldInput:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    background: white;
}

.fieldInput::placeholder {
    color: #94a3b8;
}

.whatsappContainer {
    display: flex;
    gap: 12px;
    align-items: center;
}

.countrySelect {
    padding: 12px 16px;
    border: 2px solid rgba(226, 232, 240, 0.8);
    border-radius: 12px;
    font-size: 14px;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    min-width: 150px;
    transition: all 0.3s ease;
}

.countrySelect:focus {
    outline: none;
    border-color: #10b981;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.peerActions {
    display: flex;
    justify-content: center;
    padding-top: 16px;
}

.submitButton {
    padding: 16px 32px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.submitButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4);
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.buttonIcon {
    font-size: 18px;
}

/* Status Section */
.statusSection {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

/* Status Cards */
.statusCard, .goldCard {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 24px;
    padding: 32px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.statusCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #c0c0c0 0%, #a8a8a8 50%, #909090 100%);
    border-radius: 24px 24px 0 0;
}

.goldCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
    border-radius: 24px 24px 0 0;
}

.statusHeader, .goldHeader {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 32px;
}

.statusIcon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 8px 20px rgba(192, 192, 192, 0.3);
}

.goldIcon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 8px 20px rgba(251, 191, 36, 0.3);
}

.statusInfo, .goldInfo {
    flex: 1;
}

.statusTitle, .goldTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.statusDescription, .goldDescription {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

.statusContent, .goldContent {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

/* Documents Grid */
.documentsGrid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

.documentCard {
    padding: 24px;
    background: rgba(248, 250, 252, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    border-radius: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.documentCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: rgba(192, 192, 192, 0.4);
}

.documentHeader {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 20px;
}

.documentIcon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(226, 232, 240, 0.8);
    flex-shrink: 0;
}

.documentInfo {
    flex: 1;
}

.documentTitle {
    font-size: 16px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.documentDescription {
    font-size: 13px;
    color: #64748b;
    margin: 0;
    line-height: 1.4;
}

.uploadContainer {
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.uploadLabel {
    display: block;
    width: 100%;
    padding: 16px;
    border: 2px dashed rgba(192, 192, 192, 0.4);
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    box-sizing: border-box;
    flex: 1;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.uploadLabel:hover {
    border-color: rgba(192, 192, 192, 0.6);
    background: rgba(255, 255, 255, 0.8);
}

.uploadContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.uploadIcon {
    opacity: 0.7;
}

.uploadText {
    font-size: 13px;
    color: #64748b;
    font-weight: 500;
    text-align: center;
}

.statusActions {
    display: flex;
    justify-content: center;
    padding-top: 16px;
}

.submitDocumentsButton {
    padding: 16px 32px;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    border: none;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
}

.submitDocumentsButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(139, 92, 246, 0.4);
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
}

/* Gold Requirements */
.requirementsList {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.requirement {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: rgba(255, 248, 220, 0.8);
    border: 1px solid rgba(251, 191, 36, 0.2);
    border-radius: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.requirement:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
}

.requirementIcon {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(251, 191, 36, 0.2);
    flex-shrink: 0;
}

.requirementText {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    line-height: 1.4;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .pageContainer {
        padding: 16px;
        gap: 24px;
    }

    .profileCard, .peerCard, .statusCard, .goldCard {
        padding: 24px;
        border-radius: 20px;
    }

    .profileHeader, .peerHeader, .statusHeader, .goldHeader {
        margin-bottom: 24px;
    }

    .profileIcon, .peerIcon, .statusIcon, .goldIcon {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }

    .profileTitle, .peerTitle, .statusTitle, .goldTitle {
        font-size: 20px;
    }

    .profileDetailsGrid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .peerGrid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .documentsGrid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .profilePictureSection {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 20px;
    }

    .profilePicture {
        width: 100px;
        height: 100px;
    }

    .profileName {
        font-size: 20px;
    }

    .profileEmail {
        font-size: 14px;
    }

    .whatsappContainer {
        flex-direction: column;
        gap: 12px;
    }

    .countrySelect {
        width: 100%;
        min-width: unset;
    }

    .submitButton, .submitDocumentsButton {
        width: 100%;
        justify-content: center;
        padding: 14px 24px;
    }
}

@media (max-width: 576px) {
    .pageContainer {
        padding: 12px;
        gap: 20px;
    }

    .profileCard, .peerCard, .statusCard, .goldCard {
        padding: 20px;
        border-radius: 16px;
    }

    .detailCard, .peerField, .documentCard, .requirement {
        padding: 16px;
        border-radius: 12px;
    }

    .profilePictureSection {
        padding: 16px;
        border-radius: 16px;
    }

    .profilePicture {
        width: 80px;
        height: 80px;
    }

    .profileName {
        font-size: 18px;
    }

    .profileEmail {
        font-size: 13px;
    }

    .detailIcon, .fieldIcon, .documentIcon, .requirementIcon {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }

    .detailValue {
        font-size: 14px;
    }

    .fieldInput {
        padding: 10px 14px;
        font-size: 13px;
    }

    .countrySelect {
        padding: 10px 14px;
        font-size: 13px;
    }

    .uploadLabel {
        padding: 12px;
    }

    .uploadText {
        font-size: 12px;
    }

    .documentTitle {
        font-size: 15px;
    }

    .documentDescription {
        font-size: 12px;
    }

    .requirementText {
        font-size: 14px;
    }
}
