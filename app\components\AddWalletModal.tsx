import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Modal,
  ActivityIndicator,
  ScrollView,
} from "react-native";
import { styled } from "nativewind";
import { Picker } from "@react-native-picker/picker";
import Toast, { BaseToast } from "react-native-toast-message";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import { Entypo } from "@expo/vector-icons";

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledTextInput = styled(TextInput);
const StyledScrollView = styled(ScrollView);

interface NetworkList {
  id: number;
  network: string;
}

const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "#10B981", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{
        fontSize: 14,
        fontWeight: "600",
        marginLeft: 20,
      }}
    />
  ),
  error: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "#EF4444", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{
        fontSize: 14,
        fontWeight: "600",
        marginLeft: 20,
      }}
    />
  ),
};

const showToast = (type: "success" | "error", message: string) => {
  Toast.show({
    type,
    text1: message,
    position: "top",
    visibilityTime: 3000,
  });
};

const AddWalletModal = ({
  isVisible,
  onClose,
}: {
  isVisible: boolean;
  onClose: () => void;
}) => {
  const [selectedNetwork, setSelectedNetwork] = useState("");
  const [externalWallet, setExternalWallet] = useState("");
  const [networkList, setNetworkList] = useState<NetworkList[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const addExternalWalletAddress = async () => {
    if (!selectedNetwork || !externalWallet) {
      showToast("error", "Please select a network and enter a wallet address.");
      return;
    }

    const trxRegex = /^T[A-Za-z1-9]{33}$/;
    const ercRegex = /^(0x)[0-9a-fA-F]{40}$/;

    if (selectedNetwork === "TRX" && !trxRegex.test(externalWallet.trim())) {
      showToast("error", "Please enter a valid TRX wallet address");
      return;
    } else if (
      selectedNetwork === "MATIC" &&
      !ercRegex.test(externalWallet.trim())
    ) {
      showToast("error", "Please enter a valid MATIC wallet address");
      return;
    }

    setIsSubmitting(true);
    try {
      const res = await customFetchWithToken.post("/add-external-wallet/", [
        {
          networks: selectedNetwork,
          wallet_addresses: externalWallet.trim(),
        },
      ]);

      showToast("success", res.data.message);
      setSelectedNetwork("");
      setExternalWallet("");
      // onClose();
    } catch (error: any) {
      showToast(
        "error",
        error.response?.data?.message ||
          "Failed to add wallet. Please try again."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const getNetworkList = async () => {
    setIsLoading(true);
    try {
      const res = await customFetchWithToken.get("/get-network-list/");
      setNetworkList(res.data.data);
    } catch (error: any) {
      showToast("error", "Failed to fetch networks");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isVisible) {
      getNetworkList();
    }
  }, [isVisible]);

  return (
    <Modal visible={isVisible} transparent animationType="slide">
      <StyledView className="flex-1 bg-black/50 justify-center items-center px-4">
        <StyledView className="bg-white w-full max-w-md rounded-2xl shadow-xl">
          {/* Header */}
          <StyledView className="flex-row justify-between items-center p-5 border-b border-gray-100">
            <StyledText className="text-xl font-bold text-gray-800">
              Add New Wallet
            </StyledText>
            <TouchableOpacity
              onPress={onClose}
              className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center"
            >
              <Entypo name="cross" size={20} color="#4B5563" />
            </TouchableOpacity>
          </StyledView>

          {isLoading ? (
            <StyledView className="p-8 items-center">
              <ActivityIndicator size="large" color="#6366F1" />
              <StyledText className="mt-4 text-gray-600">
                Loading networks...
              </StyledText>
            </StyledView>
          ) : (
            <StyledScrollView className="p-5">
              {/* Network Selection */}
              <StyledView className="mb-5">
                <StyledText className="text-sm font-semibold text-gray-700 mb-2">
                  Network
                </StyledText>
                <StyledView className="border border-gray-200 rounded-xl overflow-hidden bg-gray-50">
                  <Picker
                    selectedValue={selectedNetwork}
                    onValueChange={setSelectedNetwork}
                    style={{ height: 50, backgroundColor: "transparent" }}
                  >
                    <Picker.Item
                      label="Select Network"
                      value=""
                      color="#9CA3AF"
                    />
                    {networkList.map((network) => (
                      <Picker.Item
                        key={network.id}
                        label={network.network}
                        value={network.network}
                        color="#1F2937"
                      />
                    ))}
                  </Picker>
                </StyledView>
              </StyledView>

              {/* Wallet Address Input */}
              <StyledView className="mb-6">
                <StyledText className="text-sm font-semibold text-gray-700 mb-2">
                  Wallet Address
                </StyledText>
                <StyledTextInput
                  placeholder="Enter wallet address"
                  value={externalWallet}
                  onChangeText={setExternalWallet}
                  maxLength={260}
                  className="border border-gray-200 rounded-xl px-4 py-3.5 text-base bg-gray-50"
                  placeholderTextColor="#9CA3AF"
                  selectionColor="#6366F1"
                />
                {selectedNetwork && (
                  <StyledText className="mt-2 text-xs text-gray-500">
                    Enter a valid {selectedNetwork} wallet address
                  </StyledText>
                )}
              </StyledView>

              {/* Add Button */}
              <StyledTouchableOpacity
                onPress={addExternalWalletAddress}
                disabled={isSubmitting}
                className={`rounded-xl py-4 ${
                  isSubmitting
                    ? "bg-indigo-400"
                    : "bg-indigo-600 active:bg-indigo-700"
                }`}
              >
                <StyledView className="flex-row justify-center items-center">
                  {isSubmitting ? (
                    <>
                      <ActivityIndicator size="small" color="white" />
                      <StyledText className="ml-2 text-white font-semibold">
                        Adding...
                      </StyledText>
                    </>
                  ) : (
                    <StyledText className="text-white font-semibold text-base">
                      Add Wallet
                    </StyledText>
                  )}
                </StyledView>
              </StyledTouchableOpacity>
            </StyledScrollView>
          )}
        </StyledView>
      </StyledView>
      <Toast config={toastConfig} />
    </Modal>
  );
};

export default AddWalletModal;
