import React, { useState, memo } from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { styled } from "nativewind";
import SearchExchModal from "./SearchExchModal";
import SearchRecipientModal from "./searchRecipientModal";

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

interface TradeCardProps {
  name: string;
  listingId: number;
  trades: number;
  amount: string;
  successRate: string;
  rateSpread: string;
  exchangeRate: string;
  quantity: number;
  payInCurrency: string;
  payOutCurrency: string;
  payInOption: string;
  payOutOption: string;
  onBuyPress: () => void;
  min_liquidity: number;
  max_liquidity: number;
  time_limit: number;
  terms: string;
  created_date: string;
}

const TradeCard = memo(
  ({
    name,
    amount,
    listingId,
    trades,
    successRate,
    rateSpread,
    exchangeRate,
    quantity,
    payInCurrency,
    payOutCurrency,
    payInOption,
    payOutOption,
    min_liquidity,
    max_liquidity,
    time_limit,
    created_date,
    terms,
    onBuyPress,
  }: TradeCardProps) => {
    const [showExchModal, setShowExchModal] = useState(false);
    const [showRecipientModal, setShowRecipientModal] = useState(false);
    // console.log("here", showRecipientModal);

    const handleExchModalOpen = () => {
      setShowExchModal(!showExchModal);
    };

    return (
      <StyledView className="border-t border-b border-dashed border-gray-300 p-4">
        <StyledView className="flex-row items-center mb-2">
          <Image
            source={{ uri: "/placeholder.svg" }}
            className="w-5 h-5 rounded-full bg-orange-200 mr-2"
          />
          <StyledText className="text-md font-pmedium">{name}</StyledText>
        </StyledView>

        <StyledText className="text-md text-gray-500 mb-1">
          Listing Id : {listingId}
        </StyledText>

        <StyledView className="flex flex-col items-start mb-2">
          <StyledText className="text-md text-gray-500 mr-2">
            Trades: {trades} Orders | {successRate}
          </StyledText>
          {/* <StyledView className="flex-row items-center">
          <StyledText className="text-md text-purple-600 mr-1">
            △{rateSpread}
          </StyledText>
          <StyledText className="text-md text-purple-600">
            ▽{rateSpread}
          </StyledText>
        </StyledView> */}
        </StyledView>

        <StyledView className="flex-row justify-between items-start mb-2">
          <StyledView>
            {/* <StyledText className="text-md font-pbold text-[#FFA824] mb-0">
            1 {payInCurrency} = {Number(exchangeRate).toFixed(2)}
            {payOutCurrency}
          </StyledText> */}
            <StyledText className="text-xl font-pbold text-[#FFA824] mb-0">
              1 {payInCurrency} = {Math.floor(Number(exchangeRate) * 100) / 100}{" "}
              {payOutCurrency}
            </StyledText>
            <StyledText className="text- text-gray-500 my-2">
              Quantity: {quantity}
            </StyledText>
            <StyledText className="text-md text-gray-500">
              Limit: {min_liquidity} - {max_liquidity}
            </StyledText>
          </StyledView>

          <StyledView className="items-end ">
            {/* <StyledText className=" text-xl font-pbold text-[#FFA824] mb-0">
            1 {payInCurrency} = {Math.floor(Number(exchangeRate) * 100) / 100}{" "}
            {payOutCurrency}
          </StyledText> */}
            <StyledView className="flex-row items-center mb-1">
              <StyledText className="text-md text-gray-500 mr-1">
                {payInCurrency} → {payOutCurrency}
              </StyledText>
              <View className="w-1 h-2.5 bg-blue-600 rounded-full" />
            </StyledView>
            <StyledView className="flex-row items-center mb-1">
              <StyledText className="text-md text-gray-500 mr-1">
                {payInOption}
              </StyledText>
              <View className="w-1 h-2.5 bg-green-800 rounded-full" />
            </StyledView>
            <StyledView className="flex-row items-center">
              <StyledText className="text-md text-gray-500 mr-1">
                {payOutOption}
              </StyledText>
              <View className="w-1 h-2.5 bg-yellow-500 rounded-full" />
            </StyledView>
          </StyledView>
        </StyledView>

        <StyledTouchableOpacity
          className="bg-[#74dba6] rounded-md py-2 items-center"
          onPress={handleExchModalOpen}
        >
          <StyledText className="text-black font-pbold">BUY</StyledText>
        </StyledTouchableOpacity>
        {showExchModal && (
          <SearchExchModal
            listingId={listingId}
            rate={Math.floor(Number(exchangeRate) * 100) / 100}
            available_liquidity={quantity}
            min_liquidity={min_liquidity}
            max_liquidity={max_liquidity}
            ListedOn={created_date}
            timeLimit={time_limit}
            payInCurrency={payInCurrency}
            payOutCurrency={payOutCurrency}
            payin_option={payInOption}
            payout_option={payOutOption}
            amount={amount}
            terms={terms}
            isVisible={showExchModal}
            showExchModal={showExchModal}
            onClose={() => setShowExchModal(false)}
            setShowRecipientModal={setShowRecipientModal} // Correct usage
          />
        )}
        {showRecipientModal && (
          <SearchRecipientModal
            isVisible={showRecipientModal}
            onClose={() => setShowRecipientModal(false)}
            payOutOption={payOutOption}
            payOutCurrency={payOutCurrency}
            listingId={listingId}
            enteredAmount={23}
          />
        )}
      </StyledView>
    );
  }
);

export default TradeCard;
