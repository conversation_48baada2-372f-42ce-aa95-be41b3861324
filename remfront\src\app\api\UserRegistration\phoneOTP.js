import axios from "axios";
require("dotenv").config();

const URL = process.env.NEXT_PUBLIC_PHN_OTP_URL;
const Verify_URL = process.env.NEXT_PUBLIC_PHN_OTP_VERIFY__URL;

export const Phone_OTP_Fetch_Api = async (number) => {
  const res = await axios({
    url: URL,
    method: "POST",
    data: {
      mobile: String(number),
    },
  });
  return res;
};

export const Phone_OTP_Verify_Api = async (number, otp) => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }
  const res = await axios({
    url: Verify_URL,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "POST",
    data: {
      mobile: String(number),
      otp: String(otp),
    },
  });
  return res;
};
