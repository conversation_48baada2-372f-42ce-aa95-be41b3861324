import axios from "axios";
require("dotenv").config();

if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

const baseURL = process.env.NEXT_PUBLIC_Base_URL;
export const userRegistrationApi = async (Data) => {
  // const baseURL = process.env.NEXT_PUBLIC_BASE_URL;
  const URL = process.env.NEXT_PUBLIC_USER_REGISTRATION_URL;
  //   const Api_key = process.env.NEXT_PUBLIC_API_KEY;

  const res = await axios({
    url: URL,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "POST",
    data: Data,
  });
  return res;
};

export const userVerificationStatusApi = async (Data) => {
  const Base_URL = process.env.NEXT_PUBLIC_Base_URL;
  const res = await axios({
    url: `${Base_URL}/upload-document/`,
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "multipart/form-data",
    },
    method: "POST",
    data: Data,
  });
  return res;
};
export const userVerificationPeerApi = async (Data) => {
  const res = await axios({
    url: `${baseURL}/peer-registration/`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "POST",
    data: Data,
  });
  return res;
};
export const getstatusDetailsApi = async (Data) => {
  const res = await axios({
    url: `${baseURL}/get-document/`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "GET",
  });
  return res;
};
export const getPeerDetailsApi = async (Data) => {
  const res = await axios({
    url: `${baseURL}/get-peer-details/`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "GET",
  });
  return res;
};
