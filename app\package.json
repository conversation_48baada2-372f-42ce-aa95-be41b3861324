{"name": "rem_expo", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo", "setupFilesAfterEnv": ["./jest.setup.ts"], "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@sentry/react-native|native-base|react-native-svg)"], "collectCoverage": true, "collectCoverageFrom": ["**/*.{ts,tsx,js,jsx}", "!**/coverage/**", "!**/node_modules/**", "!**/babel.config.js", "!**/expo-env.d.ts", "!**/.expo/**"]}, "dependencies": {"@babel/preset-env": "^7.26.0", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/native": "^7.0.0", "axios": "^1.7.9", "babel-preset-expo": "~13.0.0", "expo-blur": "~14.1.5", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-font": "~13.3.2", "expo-image-picker": "~16.1.4", "expo-linking": "~7.1.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lodash": "^4.17.21", "nativewind": "^2.0.11", "postcss": "^8.4.49", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-ratings": "^8.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-sse": "^1.2.1", "react-native-toast-message": "^2.2.1", "react-native-web": "^0.20.0", "react-test-renderer": "19.0.0", "react-use-websocket": "^4.11.1", "stream-chat-expo": "^6.2.0", "stream-chat-react-native": "^6.2.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@react-native-community/cli": "latest", "@testing-library/react-native": "^13.2.0", "@types/base-64": "^1.0.2", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.13", "@types/react": "~19.0.10", "@types/react-test-renderer": "^19.0.0", "autoprefixer": "^10.4.21", "expo": "^53.0.20", "jest": "^29.7.0", "jest-expo": "~53.0.9", "react-test-renderer": "19.0.0", "tailwindcss": "^3.3.2", "typescript": "~5.8.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["@babel/preset-env", "lodash", "postcss", "react-use-websocket", "stream-chat"]}}}, "private": true}