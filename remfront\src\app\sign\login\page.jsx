"use client";
import React, { useEffect, useState, useRef } from "react";
import Image from "next/image";
import styles from "./login.module.css";
import line from "../../../../public/line.svg";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import loginApi from "../../api/onboarding/login";
import Link from "next/link";
import ReCaptcha from "../../components/ReCaptcha/page";
import verifyCaptcha from "@/app/api/onboarding/verifycaptcha";
import { useTimer } from "@/app/context/TimerContext";
import RightSideAnimation from "../../components/RightSideAnimation/RightSideAnimation";

require("dotenv").config();

const login = () => {
  const router = useRouter();
  const { startTimer } = useTimer();
  const ref = useRef(null);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [User, setUser] = useState("");
  const [isCaptchaVerified, setIsCaptchaVerified] = useState(false);
  const recaptchaRef = useRef(null);
  const [captchaToken, setCaptchatoken] = useState(null);
  const [loginBtnIsActive, setLoginBtnIsActive] = useState(false);

  const handleCaptchaChange = (value) => {
    setIsCaptchaVerified(!!value);
    setCaptchatoken(value);
  };

  const Data = {
    email: email,
    password: password,
  };

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const URL = `${BaseURL}/login/`;
  const CaptchaURL = `${BaseURL}/verifycaptch/`;

  const handleEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@.]/g, "");
    setEmail(inputValue);
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setLoginBtnIsActive(true);

    try {
      // Verify captcha
      const verify = await verifyCaptcha(CaptchaURL, captchaToken);
      if (!verify?.data?.success) {
        toast.error("Captcha Not Verified Successfully");
        return;
      }
    } catch (error) {
      console.log("cap", error);
      if (error.response.data.success == false) {
        recaptchaRef?.current?.reset();
        setLoginBtnIsActive(false);
      }
      return;
    }
    try {
      // Attempt login
      const res = await loginApi(URL, Data);
      if (res.status !== 200) {
        toast.error("Login failed.");
        window.grecaptcha.reset();
        return;
      }

      const { two_factor, msg, data } = res.data;

      if (two_factor) {
        localStorage.setItem("userEmail", email);
        sessionStorage.setItem("two_factor_enabled", true);
        toast.success(msg);
        router.push("/sign/2fa");
      } else {
        const {
          user_id,
          firstname,
          lastname,
          tokens: { access, refresh },
          user_email,
          chat_token,
        } = data;

        localStorage.setItem("userID", user_id);
        localStorage.setItem("userName", firstname);
        localStorage.setItem("lastname", lastname);
        localStorage.setItem("user", access);
        localStorage.setItem("refreshToken", refresh);
        localStorage.setItem("userEmail", user_email);
        localStorage.setItem("chatToken", chat_token);
        sessionStorage.setItem("two_factor_enabled", false);
        sessionStorage.setItem("user", access);
        setLoginBtnIsActive(false);

        setTimeout(() => {
          router.push("/pages/searchads");
        }, 1500);

        toast.success(res.data.message);
        startTimer();
      }
    } catch (error) {
      if (error.response) {
        setLoginBtnIsActive(false);
        const { status, data, config } = error.response;

        if (config.url === CaptchaURL && status === 400) {
          toast.error("Something went wrong please reload the page.");
        } else if (status === 401 || status === 400) {
          toast.error("Invalid Credentials.");
        } else {
          toast.error("An unexpected error occurred.");
        }
      } else {
        toast.error("An unexpected error occurred.");
      }
      console.error("Error:", error);
    }
  };

  return (
    <main className={styles.main}>
      <div className={styles.leftContainer}>
        <div className={styles.leftBody}>
          <div className={styles.logo}>Remflow</div>
          <h1 className={styles.heading}>Login</h1>
          <div className={styles.subHeading}>
            Enter your credentials to access your account
          </div>

          <div className={styles.orContainer}>
            <div className={styles.line1}>
              <Image src={line} alt="line" />
            </div>
            {/* <div className={styles.or}>or</div> */}
            <div className={styles.line2}>
              <Image src={line} alt="line" />
            </div>
          </div>

          <form action="" onSubmit={onSubmit}>
            <div className={styles.emailContainer}>
              <div className={styles.email}>
                <label className={styles.nameLabel} htmlFor="email">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  maxLength={260}
                  value={email}
                  onChange={handleEmail}
                  required
                  // autoComplete="off"
                />
              </div>
            </div>
            <div className={styles.passwordContainer}>
              {/* <a className={styles.forgotpass} href="/sign/forgotPassword">
                Forgot password ?
              </a> */}
              <Link className={styles.forgotpass} href="/sign/forgotPassword">
                Forgot password ?
              </Link>
              <div className={styles.password}>
                <label className={styles.nameLabel} htmlFor="password">
                  password
                </label>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  maxLength={260}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  // autoComplete="off"
                />

                <span
                  className={styles.hidePass}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {!showPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      width={20}
                      height={20}
                    >
                      <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 576 512"
                      width={20}
                      height={20}
                    >
                      <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                    </svg>
                  )}
                </span>
              </div>
            </div>

            <div className={styles.captchaCont}>
              <ReCaptcha onChange={handleCaptchaChange} ref={recaptchaRef} />
            </div>

            <div className={styles.loginBtnContainer}>
              <button
                type="submit"
                className={styles.loginBtn}
                disabled={loginBtnIsActive}
              >
                Login
              </button>
            </div>
          </form>
          {/* ToastContainer removed - already provided globally in LayoutWrapper.js */}
          <div className={styles.lastPart}>
            You don't have an account ?{" "}
            <span>
              {" "}
              <a className={styles.signUp} href="/">
                Register{" "}
              </a>{" "}
            </span>
            <div className={styles.baseLinksContainer}>
              <div className={styles.baseLinks}>
                <a href="">About Us</a>
              </div>
              <div className={styles.baseLinks}>
                {" "}
                <div className={styles.baseLinks}>
                  <a href="">Conatct Us</a>
                </div>
              </div>
              <div className={styles.baseLinks}>
                {" "}
                <div className={styles.baseLinks}>
                  <a href="">FAQ</a>
                </div>
              </div>
              <div className={styles.baseLinks}>
                <a href="">Privacy Policy</a>
              </div>
              <div className={styles.baseLinks}>
                <a href="">Affiliation Partner</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <RightSideAnimation />
    </main>
  );
};

export default login;
