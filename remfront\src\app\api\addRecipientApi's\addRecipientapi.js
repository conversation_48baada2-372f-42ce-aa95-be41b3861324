import axios from "axios";

const Base_Url = process.env.NEXT_PUBLIC_Base_URL;

if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const addRecipientApi = async (Data) => {
  const res = await axios({
    url: `${Base_Url}/add-recipient/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};
export const getAllRecipientApi = async (Data) => {
  const res = await axios({
    url: `${Base_Url}/get-all-recipient/`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};

export const deleteRecipientApi = async (id) => {
  const res = await axios({
    url: `${Base_Url}/delete-recipient/${id}`,
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};

export const editRecipientApi = async (Data, id) => {
  const res = await axios({
    url: `${Base_Url}/edit-recipient/${id}`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};
