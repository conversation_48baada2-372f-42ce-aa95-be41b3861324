import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  Platform,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import Layout from "../components/Layout";
import customFetchWithToken from "./utils/axiosInterceptor";
import { showToastSuccess, showToastError, toastConfig } from "../hooks/toast";
import * as ImagePicker from "expo-image-picker";
import * as SecureStore from "expo-secure-store";
import ConfirmationModal from "../components/ConfirmationModal";
import { useRouter } from "expo-router";

export default function UserProfile() {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [mobile, setMobile] = useState<string>("");
  const [countryPhone, setCountryPhone] = useState("");
  const [telegramId, setTelegramId] = useState("");
  const [whatsappId, setWhatsappId] = useState("");
  const [weChatId, setWeChatId] = useState("");
  const [otherMessagingId, setOtherMesesgingId] = useState("");
  const [profilePicture, setProfilePicture] = useState<string | File | null>(
    "https://remflow.s3.eu-central-1.amazonaws.com/test.png"
  );
  const [oldPass, setOldPass] = useState("");
  const [newPass, setNewPass] = useState("");
  const [confirmPass, setConfirmPass] = useState("");
  const [openNotifications, setOpenNotifications] = useState(false);
  const [newFile, setNewFile] = useState<any>(null);

  const [proofOfAdderss, setProofOfAdderss] = useState("");
  const userIdRef = useRef<string>("");
  const [sourceOfFunds, setSourceOfFunds] = useState("");
  const [bankStatement, setBankStatement] = useState("");
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [secret, setSecret] = useState("");

  const router = useRouter();

  useEffect(() => {
    const fetchToken = async () => {
      const token = await SecureStore.getItemAsync("userID");
      userIdRef.current = token || "";
    };
    fetchToken();
  }, []);

  const Data = {
    old_password: oldPass,
    new_password: newPass,
    confirm_password: confirmPass,
  };




  const upadtePassFunc = async () => {
    try {
      const res = await customFetchWithToken.post(
        "/update-user-password/",
        Data
      );
      console.log(res);
      showToastSuccess(res.data.message);
    } catch (error: any) {
      console.error(error);
      showToastError(error.response.data.message);
    }
  };

  const getProfileInfoAPIFunc = async () => {
    try {
      const res = await customFetchWithToken.get("/view-user-details/");

      setFirstName(res.data.data.firstname);
      setLastName(res.data.data.lastname);
      setEmail(res.data.data.email);
      setMobile(res.data.data.mobile);
      setProfilePicture(res.data.data.img_logo);
    } catch (error) {
      console.error(error);
    }
  };

  const fetchPeerDetailsApi = async () => {
    try {
      const res = await customFetchWithToken.get("/get-peer-details/");
      setTelegramId(res.data.data?.telegram_id);
      setWhatsappId(res.data.data?.whatsapp_id);
      setWeChatId(res.data.data?.wechat_id);
      setOtherMesesgingId(res.data.data?.any_other_id);
    } catch (error) {
      console.error(error);
    }
  };

  const Data1 = {
    telegram_id: telegramId,
    whatsapp_id: `+${countryPhone}${whatsappId}`,
    wechat_id: weChatId,
    any_other_id: otherMessagingId,
  };

  const handleVerification = async () => {
    showToastSuccess("Sending");

    try {
      const res = await customFetchWithToken.post("/peer-registration/", Data1);
      showToastSuccess(res.data.message);
    } catch (error: any) {
      showToastError(error.response.data.message);
      console.error(error);
    }
  };

  const handleDocumentsUpload = async (type: string) => {
    try {
      // Request permission to access photos
      const permissionResult =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        alert("Permission to access photos is required!");
        return;
      }

      // Open the image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ["images"],
        allowsEditing: true, // Allow users to edit the image
        aspect: [1, 1], // Crop the image to a square aspect ratio
        quality: 1, // High-quality image
      });

      console.log(result);
      // Ensure the result is valid
      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.error("Image selection was canceled or no assets found.");
        return;
      }

      const selectedImageUri = result.assets[0].uri;

      // Update the profile picture state
      type === "proof_of_address"
        ? setProofOfAdderss(selectedImageUri)
        : type === "source_of_funds"
        ? setSourceOfFunds(selectedImageUri)
        : type === "bankStatement"
        ? setBankStatement(selectedImageUri)
        : "";
    } catch (error) {
      console.error("An error occurred:", error);
      showToastError("An error occurred while updating profile info.");
    }
  };

  const handleUserStatusDocsSubmit = async () => {
    try {
      const formData = new FormData();
      formData.append("user_id", userIdRef.current);
      formData.append("flag", "document");
      // Handle docs append
      if (proofOfAdderss && typeof proofOfAdderss === "string") {
        formData.append("proof_of_address", {
          uri: proofOfAdderss,
          type: "image/jpeg",
          name: "proof_of_address.jpg",
        } as unknown as Blob);
      }
      if (sourceOfFunds && typeof sourceOfFunds === "string") {
        formData.append("source_of_fund", {
          uri: sourceOfFunds,
          type: "image/jpeg",
          name: "source_of_Funds.jpg",
        } as unknown as Blob);
      }
      if (bankStatement && typeof bankStatement === "string") {
        formData.append("bank_statement", {
          uri: bankStatement,
          type: "image/jpeg",
          name: "bankStatement.jpg",
        } as unknown as Blob);
      }

      if (!proofOfAdderss || !sourceOfFunds || !bankStatement) {
        showToastError("Please upload all required documents.");
        return;
      }

      // Make the request
      const response = await customFetchWithToken.post(
        "/upload-document/",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      showToastSuccess("Sending...");
      if (response.data) {
        showToastSuccess("Documents updated successfully");
      }
    } catch (error) {
      console.error("Profile update error:", error);
      showToastError("Failed to upload Documents");
    }
  };

  const handleImageChange = async () => {
    try {
      // Request permission to access photos
      const permissionResult =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        alert("Permission to access photos is required!");
        return;
      }

      // Open the image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ["images"],
        allowsEditing: true, // Allow users to edit the image
        aspect: [1, 1], // Crop the image to a square aspect ratio
        quality: 1, // High-quality image
      });

      console.log(result);
      // Ensure the result is valid
      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.error("Image selection was canceled or no assets found.");
        return;
      }

      const selectedImageUri = result.assets[0].uri;

      // Update the profile picture state
      setProfilePicture(selectedImageUri);
    } catch (error) {
      console.error("An error occurred:", error);
      showToastError("An error occurred while updating profile info.");
    }
  };

  const profileInfoChangeFunc = async () => {
    if (!firstName || !lastName || !mobile) {
      showToastError(
        "Please fill in all required Personal Information fields."
      );
      return;
    }
    try {
      const formData = new FormData();

      // Append text fields
      formData.append("first_name", firstName);
      formData.append("last_name", lastName);
      formData.append("phoneNumber", mobile.toString());

      // Handle profile picture
      if (profilePicture && typeof profilePicture === "string") {
        formData.append("profile_picture", {
          uri: profilePicture,
          type: "image/jpeg",
          name: "profile_picture.jpg",
        } as unknown as Blob);
      }

      // Make the request
      const response = await customFetchWithToken.put(
        "/edit-user-details/",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (response.data) {
        showToastSuccess("Profile updated successfully");
      }
    } catch (error) {
      console.error("Profile update error:", error);
      showToastError("Failed to update profile");
    }
  };

  // const profileInfoChangeFunc = async () => {
  //   try {
  //     // Create a new FormData object
  //     const formData = new FormData();
  //     formData.append("first_name", firstName);
  //     formData.append("last_name", lastName);
  //     formData.append("phoneNumber", mobile.toString()); // Convert number to string
  //     if (profilePicture && typeof profilePicture === "string") {
  //       const response = await fetch(profilePicture);
  //       const blob = await response.blob();
  //       const file = new File([blob], "profile_picture.jpg", {
  //         type: blob.type || "image/jpeg",
  //       });
  //       formData.append("profile_picture", profilePicture); // Append the File
  //     }

  //     // Log FormData entries for debugging
  //     for (let [key, value] of formData.entries()) {
  //       console.log(`${key}:`, value);
  //     }

  //     // Send the request
  //     const res = await customFetchWithToken.put(
  //       "/edit-user-details/",
  //       formData
  //     );
  //     console.log("Response:", res);
  //     showToastSuccess(res.data.message);
  //   } catch (error) {
  //     console.error("An error occurred:", error);
  //     showToastError("An error occurred while updating profile info.");
  //   }
  // };

  useEffect(() => {
    getProfileInfoAPIFunc();
    fetchPeerDetailsApi();
  }, []);

  const handleDeleteAccount = async () => {
    // try {
    //   const response = await customFetchWithToken.delete("/delete-user-account/");
    //   if (response.data) {
    //     showToastSuccess("Account deleted successfully");
    //     // Clear stored credentials and redirect to login
    //     await SecureStore.deleteItemAsync("userID");
    //     await SecureStore.deleteItemAsync("token");
    //     // Navigate to login (you may need to adjust this based on your routing)
    //     router.replace("/");
    //   }
    // } catch (error: any) {
    //   console.error("Account deletion error:", error);
    //   showToastError(error.response?.data?.message || "Failed to delete account");
    // }
    showToastSuccess("Account deleted successfully");
  };

  return (
    <Layout>
      <SafeAreaView className="flex-1 bg-gray-100 w-screen">
        <ScrollView className="flex-1 p-4">
          <View className="w-full flex-row  justify-between items-center mb-6">
            <Text className="text-2xl font-pbold">Profile</Text>

            {/* {openNotifications && (
              <View className="w-full absolute right-0">
                <Notifications />
              </View>
            )} */}
          </View>

          <View className="bg-white p-4 rounded-lg mb-6 items-center">
            {profilePicture && typeof profilePicture === "string" && (
              <Image
                source={{ uri: profilePicture }}
                style={{ width: 100, height: 100 }}
              />
            )}

            <TouchableOpacity onPress={handleImageChange}>
              <Text className="text-blue-500 font-pmedium">Change Picture</Text>
            </TouchableOpacity>
          </View>
          {isDeleteModalVisible && (
            <ConfirmationModal
              isVisible={isDeleteModalVisible}
              onClose={() => setIsDeleteModalVisible(false)}
              onConfirm={handleDeleteAccount}
              title="Delete Account"
              message="Are you sure you want to delete your account?"
            />
          )}
          <View className="bg-white p-4 rounded-lg mb-6">
            <Text className="text-lg font-psemitBold mb-4">
              Personal Information
            </Text>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium">First Name</Text>
              <TextInput
                maxLength={10}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={firstName}
                onChangeText={(item) => setFirstName(item)}
              />
            </View>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium">Last Name</Text>
              <TextInput
                maxLength={10}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={lastName}
                onChangeText={(item) => setLastName(item)}
              />
            </View>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium">Email</Text>
              <TextInput
                maxLength={40}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={email}
                onChangeText={(item) => setEmail(item)}
                keyboardType="email-address"
              />
            </View>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium">Mobile Number</Text>
              <TextInput
                maxLength={15}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={mobile as string}
                onChangeText={(item) => setMobile(item)}
                keyboardType="phone-pad"
              />
            </View>
          </View>

          <View className="flex-row justify-between mb-6">
            <TouchableOpacity
              className="bg-[#4153ed] py-3 px-6 rounded-lg"
              onPress={profileInfoChangeFunc}
            >
              <Text className="text-white font-psemitBold">Save Changes</Text>
            </TouchableOpacity>
            <TouchableOpacity
              className="bg-red-500 py-3 px-6 rounded-lg"
              onPress={() => setIsDeleteModalVisible(true)}
            >
              <Text className="text-white font-psemitBold">Delete Account</Text>
            </TouchableOpacity>
          </View>

          <View className="bg-white p-4 rounded-lg mb-6">
            <Text className="text-lg font-psemitBold mb-4">
              Change Password
            </Text>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium text-md">
                Old Password
              </Text>
              <TextInput
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={oldPass}
                maxLength={100}
                onChangeText={(text) => setOldPass(text)}
                secureTextEntry
                placeholder="enter old password"
              />
            </View>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium text-md">
                New Password
              </Text>
              <TextInput
                className="bg-gray-100 p-2 rounded font-pmedium"
                maxLength={100}
                value={newPass}
                onChangeText={(text) => setNewPass(text)}
                secureTextEntry
                placeholder="enter new password"
              />
            </View>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium text-md">
                Confirm Password
              </Text>
              <TextInput
                maxLength={100}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={confirmPass}
                onChangeText={(text) => setConfirmPass(text)}
                secureTextEntry
                placeholder="confirm new password"
              />
            </View>
            <TouchableOpacity
              className="bg-[#4153ed] py-3 rounded-lg items-center"
              onPress={upadtePassFunc}
            >
              <Text className="text-white font-psemitBold">
                Update Password
              </Text>
            </TouchableOpacity>
          </View>

          <View className="bg-white p-4 rounded-lg mb-6">
            <Text className="text-lg font-psemitBold mb-4">
              Setup Two Factor Authentication
            </Text>
            <TouchableOpacity className="bg-[#4153ed] py-3 rounded-lg items-center">
              <Text className="text-white font-psemitBold">
                2FA authentication setup
              </Text>
            </TouchableOpacity>
          </View>

          <View className="bg-white p-4 rounded-lg mb-6">
            <Text className="text-lg font-psemitBold mb-4">
              Peer Registration
            </Text>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium">Telegram ID</Text>
              <TextInput
                maxLength={100}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={telegramId}
                onChangeText={(text) => setTelegramId(text)}
              />
            </View>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium">Whatsapp ID</Text>
              <TextInput
                maxLength={100}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={whatsappId}
                onChangeText={(text) => setWhatsappId(text)}
              />
            </View>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium">WeChat ID</Text>
              <TextInput
                maxLength={100}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={weChatId}
                onChangeText={(text) => setWeChatId(text)}
              />
            </View>
            <View className="mb-4">
              <Text className="mb-2 text-md font-pmedium">
                Any other messaging app ID
              </Text>
              <TextInput
                maxLength={100}
                className="bg-gray-100 p-2 rounded font-pmedium"
                value={otherMessagingId}
                onChangeText={setOtherMesesgingId}
              />
            </View>
            <TouchableOpacity className="bg-[#4153ed] py-3 rounded-lg items-center">
              <Text className="text-white font-psemitBold">Submit</Text>
            </TouchableOpacity>
          </View>
          <View className="bg-white p-4 rounded-lg mb-6">
            <Text className="text-lg font-psemitBold mb-4">
              To Achieve Silver Status
            </Text>
            <View className="mb-4 ">
              <Text className="mb-2 font-pmedium">
                Upload proof of address*
              </Text>
              <Text className="text-sm text-gray-500 mb-2 font-pmedium">
                (please upload a Proof of address dated within 3 months, bank
                statement, utility bill or government issued letter)
              </Text>
              <TouchableOpacity
                className="bg-gray-200 p-3 rounded-lg flex-row items-center justify-center"
                onPress={() => handleDocumentsUpload("proof_of_address")}
              >
                {!proofOfAdderss ? (
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Ionicons
                      name="cloud-upload-outline"
                      size={24}
                      color="black"
                    />
                    <Text className="ml-2 font-pmedium">
                      Click here to Upload
                    </Text>
                  </View>
                ) : (
                  <Text>Image Selected</Text>
                )}
              </TouchableOpacity>
            </View>
            <View className="mb-4 ">
              <Text className="mb-2 font-pmedium">Source of Funds*</Text>
              <Text className="text-md font-pmedium text-gray-500 mb-2">
                (please upload a Proof of source of funds showing how the money
                you will transact on remflow was earned. E.g. employer salary
                slip, contract of sale, invoice, trading account screenshot,
                plus a bank statement showing the funds received to your account
                from this source)
              </Text>
              <TouchableOpacity
                className="bg-gray-200 p-3 rounded-lg flex-row items-center justify-center"
                onPress={() => handleDocumentsUpload("source_of_funds")}
              >
                {!sourceOfFunds ? (
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Ionicons
                      name="cloud-upload-outline"
                      size={24}
                      color="black"
                    />
                    <Text className="ml-2 font-pmedium">
                      Click here to Upload
                    </Text>
                  </View>
                ) : (
                  <Text>Image Selected</Text>
                )}
              </TouchableOpacity>
            </View>
            <View className="mb-4">
              <Text className="mb-2 font-pmedium">Upload Bank Statement*</Text>
              <Text className="text-sm text-gray-500 mb-2 font-pmedium">
                (please upload a recent bank statement showing your name,
                address, account number and a transaction, account balance can
                be hidden if you wish)
              </Text>
              <TouchableOpacity
                className="bg-gray-200 p-3 rounded-lg flex-row items-center justify-center"
                onPress={() => handleDocumentsUpload("bankStatement")}
              >
                {!bankStatement ? (
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Ionicons
                      name="cloud-upload-outline"
                      size={24}
                      color="black"
                    />
                    <Text className="ml-2 font-pmedium">
                      Click here to Upload
                    </Text>
                  </View>
                ) : (
                  <Text>Image Selected</Text>
                )}
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              className="bg-[#4153ed] py-3 rounded-lg items-center"
              onPress={handleUserStatusDocsSubmit}
            >
              <Text className="text-white font-psemitBold">Submit</Text>
            </TouchableOpacity>
          </View>

          <View className="p-4 rounded-lg mb-6 border-[#ffd700] border-2">
            <Text className="text-lg  mb-4 font-pbold bg-[#ffd700] p-2 text-center rounded-md">
              To Achieve Gold
            </Text>
            <View className="ml-4">
              <Text className="mb-2 text-md font-psemitBold">
                • 100 Successful Trades with Remflow
              </Text>
              <Text className="mb-2 text-md font-psemitBold">
                • Escrow Funds with Remflow
              </Text>
              <Text className="mb-2 text-md font-psemitBold">
                • Maintain 90%+ rating on REMFLOW transactions
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Layout>
  );
}