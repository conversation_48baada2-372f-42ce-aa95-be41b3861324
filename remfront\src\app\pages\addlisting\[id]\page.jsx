"use client";
import { useState, useEffect, useRef } from "react";
import styles from "./addlisting.module.css";
import PaymentMethod from "../../../components/paymentMethod/page";
import editListingApi from "@/app/api/addListing/editListing";
import { toast, ToastContainer } from "react-toastify";
import Layout from "../../../components/Layout/page";
import { useRouter } from "next/navigation";
import { useSearchParams, useParams } from "next/navigation";
import "react-toastify/dist/ReactToastify.css";
import Login from "@/app/sign/login/page";
import getAvailableAccApi from "@/app/api/addListing/getAvailableAccounts";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = () => {
  const searchParams = useSearchParams();
  const params = useParams();
  const router = useRouter();

  const [hide, setHide] = useState(false);
  const [currencyAccepted, setCurrencyAccepted] = useState(
    searchParams.get("currencyAccepted")
  );
  const [currencyPayout, setCurrencyPayout] = useState(
    searchParams.get("currencyPayout")
  );
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const [loadCurrencyTo, setLoadCurrencyTo] = useState([]);
  const [liquidity, setLiquidity] = useState(
    searchParams.get("availableLiquidity")
  );
  const [minTradeLimit, setMinTradeLimit] = useState(
    searchParams.get("minLiquidity")
  );
  const [maxTradeLimit, setMaxTradeLimit] = useState(
    searchParams.get("maxLiquidity")
  );
  const [tradeFee, setTradeFee] = useState(
    searchParams.get("trade_fee_percent")
  );
  const [finalTradeFee, setFinalTradeFee] = useState(
    searchParams.get("finalTradeFee")
  );
  const [fxRate, setFxRate] = useState("");
  const [messageAccepted, setMessageAccepted] = useState("");
  const [messagePayout, setMessagePayout] = useState("");
  const [dataAccepted, setDataAccepted] = useState([]);
  const [dataPayout, setDataPayout] = useState([]);
  const [dataFromChildAccepted, setDataFromChildAccepted] = useState(
    searchParams.get("payIn")
  );
  const [paymentAccept, setPaymentAccept] = useState(searchParams.get("payIn"));
  const [paymentPayout, setPaymentPayout] = useState(
    searchParams.get("payOut")
  );
  const [dataFromChildPayout, setDataFromChildPayout] = useState(
    searchParams.get("payOut")
  );
  const [renderCount, setRenderCount] = useState(0);
  const [availableAccData, setAvailableAccData] = useState([]);
  const [availableAccDataMessage, setAvailableAccDataMessage] = useState("");
  const [selectedAvailableAccId, setSelectedAvailableAccId] = useState(null);
  const [selectedTimeLimit, setSelectedTimeLimit] = useState(
    searchParams.get("time_limit")
  );
  const [termsandConditions, setTermsandConditions] = useState(
    searchParams.get("terms")
  );

  const authTokenRef = useRef(null);
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }

  if (!token) {
    router.push("/sign/login");
  }

  const handleCurrencyChangeAccepted = (event) => {
    setCurrencyAccepted(event.target.value);
  };
  const handleCurrencyChangePayout = (event) => {
    setCurrencyPayout(event.target.value);
  };
  const handlePaymentChangePayout = (event) => {
    const selectedPaymentMethod = event.target.value;
    setPaymentPayout(selectedPaymentMethod);
    setDataFromChildPayout(selectedPaymentMethod);
  };
  const handlePaymentChangeAccepted = (event) => {
    const selectedPaymentMethod = event.target.value;
    setPaymentAccept(selectedPaymentMethod);
    setDataFromChildAccepted(selectedPaymentMethod);
  };

  const handleTermsandConditions = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");

    if (inputValue.length > 200) {
      setTermsandConditions("");
    } else {
      setTermsandConditions(inputValue);
    }
  };

  const handleSetAvailableTradeLimit = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");
    if (inputValue < 1 || inputValue.length > 10) {
      setLiquidity("");
    } else {
      setLiquidity(inputValue);
    }
  };
  const handleSetMinTradeLimit = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");

    if (inputValue < 1 || inputValue.length > 10) {
      setMinTradeLimit("");
    } else {
      setMinTradeLimit(inputValue);
    }
  };

  const handleSetMaxTradeLimit = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");

    if (inputValue < 1 || inputValue.length > 10) {
      setMaxTradeLimit("");
    } else {
      setMaxTradeLimit(inputValue);
    }
  };

  const handleSetTradeFee = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");

    if (inputValue > 100 || inputValue.length > 20) {
      setTradeFee("100");
    } else {
      setTradeFee(inputValue);
    }
  };
  // get req Currency 👇
  const fetchCurrencyFromData = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/currency-list/?currency_from=true`
      );
      const data = await resCurrency.json();
      setLoadCurrencyFrom(data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };
  const fetchCurrencyToData = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/currency-list/?currency_to=true`
      );
      const data = await resCurrency.json();
      setLoadCurrencyTo(data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };
  // get req Currency 👆

  // get req fx-rate 👇
  const fetchFxRate = async () => {
    if (currencyAccepted.length < 1 || currencyPayout.length < 1) {
      return null;
    }
    try {
      const resFx = await fetch(
        `${BaseURL}/currency-converter/?from_currency=${currencyAccepted}&to_currency=${currencyPayout}`
      );
      const data = await resFx.json();
      if (!resFx.ok) {
        setFxRate("select currencies for rate");
      } else {
        setFxRate(data.data.rate);
      }
    } catch (error) {
      console.log(error);
    }
  };
  // get req fx-rate 👆

  //get final trade fee

  const fetchFinalTradeFee = async () => {
    if (!tradeFee || !fxRate) {
      return;
    }
    try {
      const resFx = await fetch(
        `${BaseURL}/final-exchange-rate/?trade_fee=${tradeFee}&indicative_fx_rate=${fxRate}`
      );
      const data = await resFx.json();
      if (!resFx.ok) {
        setFinalTradeFee("enter trade fee % for rate");
      } else {
        setFinalTradeFee(Number(data.data.final_exchange_rate));
      }
    } catch (error) {
      console.log(error);
    }
  };

  //get final trade fee

  const handleSelectedSavedAcc = (e) => {
    const value = e.target.value;

    if (value === "no-accounts") {
      router.push("/pages/accounts");
      return;
    }
    const paymentMethodAcc = value.split(" - ")[1];

    const filteredData = availableAccData.find((el) =>
      el.data.some((item) => item.value === paymentMethodAcc)
    );

    if (filteredData) {
      setSelectedAvailableAccId(filteredData.id);
    }
  };
  //get Payment methods

  const fetchPaymentMethodsAccepted = async () => {
    if (currencyAccepted.length < 1) {
      return;
    }
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyAccepted}`
      );
      const data = await resCurrency.json();
      setDataAccepted(data.data);
      setMessageAccepted(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchPaymentMethodspayout = async () => {
    if (currencyPayout.length < 1) {
      return;
    }
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyPayout}`
      );
      const data = await resCurrency.json();
      setDataPayout(data.data);
      setMessagePayout(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  //get Payment methods

  const getAvailableAccounts = async () => {
    if (!dataFromChildAccepted || dataFromChildAccepted === "-1") {
      return null;
    }
    try {
      const res = await customFetchWithToken.get(
        `/get-user-choice-payment-fields-data/?payment_method=${dataFromChildAccepted}`
      );
      setAvailableAccDataMessage(res.data.message);
      setAvailableAccData(res.data.data);
    } catch (error) {
      console.log(error);
      setAvailableAccDataMessage(error.response?.data?.message || "Error fetching accounts");
    }
  };
  useEffect(() => {
    fetchCurrencyFromData();
    fetchCurrencyToData();
  }, []);

  useEffect(() => {
    fetchFxRate();
  }, [currencyPayout]);

  useEffect(() => {
    fetchFinalTradeFee();
  }, [tradeFee]);

  useEffect(() => {
    fetchPaymentMethodspayout();
  }, [currencyPayout]);

  useEffect(() => {
    fetchPaymentMethodsAccepted();
  }, [currencyAccepted]);

  useEffect(() => {
    getAvailableAccounts();
  }, [dataFromChildAccepted]);

  const Data = {
    currency_accepted: currencyAccepted,
    currency_payout: currencyPayout,
    available_liquidity: Number(liquidity),
    terms_and_conditions: termsandConditions,
    min_liquidity: Number(minTradeLimit),
    max_liquidity: Number(maxTradeLimit),
    indicative_fx_rate: fxRate,
    payin_option: dataFromChildAccepted,
    payout_option: dataFromChildPayout,
    trade_fee: Number(tradeFee),
    user_payment_option: selectedAvailableAccId,
    time_limit: Number(selectedTimeLimit),
  };

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const URL = `${BaseURL}/edit-listings/${params.id}`;

  const editListingHandler = async (e) => {
    e.preventDefault();

    if (currencyAccepted.length == 0) {
      return toast.error("Please enter accepted currencies");
    }

    if (currencyPayout.length == 0) {
      return toast.error("Please enter payout currencies");
    }

    if (liquidity.length == 0) {
      return toast.error("Please enter a liquidity value");
    }

    if (minTradeLimit.length == 0) {
      return toast.error("Please enter a minimum trade limit");
    }

    if (maxTradeLimit.length == 0) {
      return toast.error("Please enter a maximum trade limit");
    }

    if (finalTradeFee.length == 0) {
      return toast.error("Please enter a final trade fee");
    }

    if (tradeFee.length == 0) {
      return toast.error("Please enter a trade fee");
    }
    if (selectedTimeLimit == null) {
      return toast.error("Please enter a selected time limit");
    }

    if (termsandConditions.length == 0) {
      return toast.error("Please enter the terms and conditions");
    }

    try {
      const res = await customFetchWithToken.put(
        `/edit-listings/${params.id}`,
        Data
      );

      if (res.status === 200 || res.status === 201) {
        toast.success(res.data.message);
        setTimeout(() => {
          router.push("/pages/mylistings");
        }, 1000);
      } else {
        toast.error("Edit Listing Failed.");
      }
    } catch (error) {
      toast.error(error.response.data.message);
      console.log(error);
    }
  };

  const hideHam = () => {
    setHide(!hide);
  };

  const editListingTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Edit Listing</h1>
      <p className={styles.pageSubtitle}>
        Update your listing details
      </p>
    </div>
  );

  return (
    <div>
      <Layout title={editListingTitle}>
        <div className={styles.rightContainerBody}>
          <div className={styles.body}>
            {/* Header Section - Hidden on desktop, shown only on mobile */}
            <div className={styles.addListingHeader}>
              <div className={styles.headerContent}>
                <h1 className={styles.pageTitle}>Edit Listing</h1>
                <p className={styles.pageSubtitle}>
                  Update your listing details
                </p>
              </div>
            </div>

            {/* Currency Selection */}
            <div className={styles.firstformWrapper}>
              <div className={styles.firstName}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="currencyAccepted">Currency accepted</label>
                </div>
                <div className={styles.firstNameInput}>
                  <select
                    name="currencyAccepted"
                    id="currencyAccepted"
                    value={currencyAccepted}
                    onChange={handleCurrencyChangeAccepted}
                  >
                    <option value="-1">Please select a currency</option>
                    {loadCurrencyFrom?.map((currency) => (
                      <option
                        key={currency.id}
                        value={currency.currency_code}
                      >
                        {currency.currency_code}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className={styles.firstName}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="currencyPayout">Currency of payout</label>
                </div>
                <div className={styles.firstNameInput}>
                  <select
                    name="currencyPayout"
                    id="currencyPayout"
                    value={currencyPayout}
                    onChange={handleCurrencyChangePayout}
                  >
                    <option value="-1">Please select a currency</option>
                    {loadCurrencyTo.map((currency) => (
                      <option
                        key={currency.id}
                        value={currency.currency_code}
                      >
                        {currency.currency_code}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Payment Methods */}
            <div className={styles.firstformWrapper}>
              <div className={styles.firstName}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="paymentMethodAccepted">Payment Method Accepted</label>
                </div>
                <div className={styles.firstNameInput}>
                  <select
                    name="paymentMethodAccepted"
                    id="paymentMethodAccepted"
                    onChange={handlePaymentChangeAccepted}
                    value={paymentAccept || "-1"}
                  >
                    <option value="-1">Please select a Payment Method</option>
                    {dataAccepted?.map((method) => (
                      <option key={method.id} value={method.payment_method}>
                        {method.payment_method}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
              <div className={styles.firstName}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="paymentPayout">Payment Method Payout</label>
                </div>
                <div className={styles.firstNameInput}>
                  <select
                    name="paymentPayout"
                    id="paymentPayout"
                    onChange={handlePaymentChangePayout}
                    value={paymentPayout || "-1"}
                  >
                    <option value="-1">Please select a Payment Method</option>
                    {dataPayout?.map((method) => (
                      <option key={method.id} value={method.payment_method}>
                        {method.payment_method}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </div>

            {/* Trading Limits Section */}
            <div className={styles.tradingLimitsWrapper}>
              <div className={styles.tradingLimitItem}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="liquidity_available">
                    Liquidity Available
                  </label>
                </div>
                <div className={styles.firstNameInput}>
                  <input
                    style={{
                      WebkitAppearance: "none",
                      margin: 0,
                    }}
                    type="text"
                    id="liquidity_available"
                    placeholder="enter liquidity available"
                    min="0"
                    onChange={handleSetAvailableTradeLimit}
                    value={liquidity}
                    required
                  />
                </div>
              </div>
              <div className={styles.tradingLimitItem}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="Minimum_trade_limit">
                    Minimum trade limit
                  </label>
                </div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="Minimum_trade_limit"
                    placeholder="enter minimum liquidity"
                    min="0"
                    pattern="\d*"
                    maxLength="20"
                    onChange={handleSetMinTradeLimit}
                    value={minTradeLimit}
                    required
                  />
                </div>
              </div>
              <div className={styles.tradingLimitItem}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="Maximum_trade_limit">
                    Maximum trade limit
                  </label>
                </div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="Maximum_trade_limit"
                    placeholder="enter maximum liquidity"
                    value={maxTradeLimit}
                    pattern="\d*"
                    maxLength="20"
                    onChange={handleSetMaxTradeLimit}
                    required
                  />
                </div>
              </div>
            </div>

            {/* FX Rate, Listing Margin, Final Rate Section */}
            <div className={styles.tradingLimitsWrapper}>
              <div className={styles.tradingLimitItem}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="Indicative">Official FX Rate</label>
                </div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="Indicative"
                    value={fxRate ? Number(fxRate).toFixed(2) : ""}
                    readOnly
                    placeholder="select both currencies"
                    required
                  />
                </div>
              </div>
              <div className={styles.tradingLimitItem}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="Trade">% Listing Margin</label>{" "}
                </div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="Trade"
                    placeholder="enter trade fee percentage"
                    pattern="\d*"
                    maxLength="20"
                    value={tradeFee}
                    onChange={handleSetTradeFee}
                    required
                  />
                </div>
              </div>
              <div className={styles.tradingLimitItem}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="finalTrade">Final Rate</label>{" "}
                </div>
                <div className={styles.firstNameInput}>
                  <input
                    type="text"
                    id="finalTrade"
                    value={finalTradeFee}
                    pattern="\d*"
                    maxLength="20"
                    readOnly
                    placeholder="Enter Listing Margin"
                    required
                  />
                </div>
              </div>
            </div>

            {/* Available Accounts and Time Limit */}
            <div className={styles.thirdformWrapper}>
              {!Array.isArray(dataAccepted) ||
              dataAccepted.some(
                (item) => item.payment_method !== "Crypto"
              ) ? (
                <div className={styles.addressName}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="availableAccounts">Available Accounts</label>{" "}
                  </div>
                  <div className={styles.addressNameInput1}>
                    <select
                      className={styles.addressNameSelect}
                      onChange={handleSelectedSavedAcc}
                      value={selectedAvailableAccId || "-1"}
                    >
                      <option value="-1">
                        {availableAccDataMessage === "Data found."
                          ? "Select available Payment Method"
                          : "You Don't have any Saved Accounts"}
                      </option>
                      {availableAccDataMessage !== "Data found." && (
                        <option value="no-accounts">
                          Go to Saved Accounts page to Create an account to
                          Complete the Listing
                        </option>
                      )}
                      {availableAccDataMessage === "Data found." &&
                        availableAccData.map((item, index) => (
                          <option key={index} value={item.id}>
                            {item.data[0].key} - {item.data[0].value}
                          </option>
                        ))}
                    </select>
                  </div>
                </div>
              ) : null}

              <div className={styles.thirdformWrapper}>
                <div className={styles.addressName}>
                  <div className={styles.firstNameLabel}>
                    <label htmlFor="finalTrade">Set Trade Time Limit</label>{" "}
                  </div>
                  <div className={styles.addressNameInput1}>
                    <select
                      className={styles.addressNameSelect}
                      value={selectedTimeLimit}
                      onChange={(e) => setSelectedTimeLimit(e.target.value)}
                    >
                      <option value="-1">Select Time Limit</option>
                      <option value="5">5 minutes</option>
                      <option value="10">10 minutes</option>
                      <option value="15">15 minutes</option>
                      <option value="20">20 minutes</option>
                      <option value="25">25 minutes</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Terms and Conditions */}
            <div className={styles.thirdformWrapper}>
              <div className={styles.addressName}>
                <div className={styles.firstNameLabel}>
                  <label htmlFor="terms">
                    Terms and conditions (max 200 characters)
                  </label>{" "}
                </div>
                <div className={styles.addressNameInput}>
                  <textarea
                    className={styles.textAreaBox}
                    rows="4"
                    cols="50"
                    maxLength="200"
                    id="terms"
                    value={termsandConditions}
                    onChange={handleTermsandConditions}
                    required
                  ></textarea>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className={styles.listing_BtnCont}>
              <button
                className={styles.listing_Btn}
                onClick={editListingHandler}
              >
                Update Listing
              </button>
            </div>
          </div>
        </div>
      </Layout>
      <ToastContainer />
    </div>
  );
};

export default page;
