import axios from "axios";
import { apiHandlerWrapper } from "@/app/utils/apiHandlerWrapper";
const Base_url = process.env.NEXT_PUBLIC_Base_URL;

let token;
if (typeof window !== "undefined") {
  token = sessionStorage.getItem("user");
}

export const getAllNotificationApi = async () => {
  const res = await axios({
    url: `${Base_url}/read-notification/`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
// export const getAllUnreadNotificationApi = async () => {
//   if (typeof window !== "undefined") {
//     var token2 = sessionStorage.getItem("user");
//   }
//   const res = await axios({
//     url: `${Base_url}/get-notification/`,
//     method: "GET",
//     headers: {
//       Authorization: `Bearer ${token2}`,
//     },
//   });
//   return res;
// };

export const getAllUnreadNotificationApi = () => {
  const res = apiHandlerWrapper("get-notification", "GET");
  return res;
};
export const readSingleNotificationApi = async (id) => {
  const res = await axios({
    url: `${Base_url}/read-notification/?notification_id=${id}`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const clearAllNotificationsApi = async () => {
  const res = await axios({
    url: `${Base_url}/clear-all-notification/`,
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
