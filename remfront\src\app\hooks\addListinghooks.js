// useFxRate.js
"use client";
import { useState, useEffect } from "react";

export const useFxRate = (currencyAccepted, currencyPayout) => {
  const [fxRate1, setFxRate1] = useState("");
  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  useEffect(() => {
    const fetchFxRate = async () => {
      if (!currencyAccepted || !currencyPayout) {
        // Return early if either of the currencies is empty
        setFxRate1("");
        return;
      }

      try {
        const resFx = await fetch(
          `${BaseURL}/currency-converter/?from_currency=${currencyAccepted}&to_currency=${currencyPayout}`
        );
        const data = await resFx.json();
        if (!resFx.ok) {
          setFxRate1("select currencies for rate");
        } else {
          setFxRate1(data.data.rate);
        }
      } catch (error) {
        console.log(error);
      }
    };

    fetchFxRate();
  }, [currencyAccepted, currencyPayout]);

  return fxRate1;
};

export const useFinalTradeFee = (tradeFee, fxRate) => {
  const [finalTradeFee1, setFinalTradeFee1] = useState("");
  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  useEffect(() => {
    const fetchFinalTradeFee = async () => {
      if (!tradeFee || !fxRate) {
        return;
      }
      try {
        const resFx = await fetch(
          `${BaseURL}/final-exchange-rate/?trade_fee=${tradeFee}&indicative_fx_rate=${fxRate}`
        );
        const data = await resFx.json();
        if (!resFx.ok) {
          setFinalTradeFee1("enter trade fee % for rate");
        } else {
          setFinalTradeFee1(data.data.final_exchange_rate);
        }
      } catch (error) {
        console.log(error);
      }
    };

    fetchFinalTradeFee();
  }, [tradeFee, fxRate]);

  return finalTradeFee1;
};

export const useCurrencyFromData = () => {
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  useEffect(() => {
    const fetchCurrencyFromData = async () => {
      try {
        const resCurrency = await fetch(
          `${BaseURL}/currency-list/?currency_from=true`
        );
        const data = await resCurrency.json();
        setLoadCurrencyFrom(data.data);
      } catch (error) {
        console.error("Error fetching currency data:", error);
      }
    };

    fetchCurrencyFromData();
  }, []);

  return loadCurrencyFrom;
};
export const useCurrencyToData = () => {
  const [loadCurrencyTo, setLoadCurrencyTo] = useState([]);
  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  useEffect(() => {
    const fetchCurrencyToData = async () => {
      try {
        const resCurrency = await fetch(
          `${BaseURL}/currency-list/?currency_to=true`
        );
        const data = await resCurrency.json();
        setLoadCurrencyTo(data.data);
      } catch (error) {
        console.error("Error fetching currency data:", error);
      }
    };

    fetchCurrencyToData();
  }, []);

  return loadCurrencyTo;
};

export const usePaymentMethodsAccepted = (currencyAccepted) => {
  const [dataAccepted1, setDataAccepted1] = useState([]);
  const [messageAccepted1, setMessageAccepted1] = useState("");
  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  useEffect(() => {
    const fetchPaymentMethodsAccepted = async () => {
      try {
        const resCurrency = await fetch(
          `${BaseURL}/payment-list/?currency=${currencyAccepted}`
        );
        const data = await resCurrency.json();

        setDataAccepted1(data.data);
        setMessageAccepted1(data.message);
      } catch (error) {
        console.error("Error fetching currency data:", error);
      }
    };

    fetchPaymentMethodsAccepted();
  }, [currencyAccepted]);

  return { dataAccepted1, messageAccepted1 };
};
