import { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
} from "react-native";
import Layout from "@/components/Layout";
import ChatScreen from "../../components/Chat";
import * as SecureStore from "expo-secure-store";
import { useWebsocketContext } from "@/app/context/AuthContext";
import { useRouter, useLocalSearchParams, Link } from "expo-router";
import { toastConfig, showToastError, showToastSuccess } from "@/hooks/toast";
import TradeTimer from "@/components/TradeTime";
import TradeReviewModal from "@/components/TradeReviewModal";

interface PayinPayoutData {
  key: string;
  value: string;
}

// Type for Peer/User Details
interface UserDetails {
  email: string;
  firstname: string;
  id: number;
  lastname: string;
  username: string;
}

// Type for Data Field
interface Data {
  payin_data: string;
  payin_option: any;
  payout_data: PayinPayoutData[];
  payout_option: string;
  username: string;
}

// Type for Listing Data
interface ListingData {
  available_liquidity: number;
  currency_accepted: string;
  currency_payout: string;
  details: string;
  final_trade_fee: number;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  max_liquidity: number;
  min_liquidity: number;
  payin_option: string;
  payout_option: string;
  terms_and_conditions: string;
  time_limit: number;
  trade_fee: number;
}

interface TradeData {
  currency_from: string;
  currency_to: string;
  data: Data;
  flag: string;
  listing_data: ListingData;
  listing_id: number;
  order_id: string;
  order_status: string;
  peer_id: UserDetails;
  trade_amount: number;
  user_details: UserDetails;
  user_id: number;
  peer_details?: UserDetails;
}

interface TimeLimit {
  left_time_in_milliseconds: number;
}

const Tradepage = () => {
  const router = useRouter();
  const params = useLocalSearchParams();
  const orderNumber = Array.isArray(params.id) ? params.id[0] : params.id;
  const passedType = params.type;
  const [activeStep, setActiveStep] = useState(0);
  const [tradeData, setTradeData] = useState<TradeData>();
  const [timeLimit, setTimeLimit] = useState<TimeLimit>({
    left_time_in_milliseconds: 0,
  });
  const [btnNameUser, setBtnNameUser] = useState("Pay to Peer");
  const [btnNamePeer, setBtnNamePeer] = useState("Notify Seller");
  const [userTimeLine, setUserTimeLine] = useState("");
  const [peerTimeLine, setPeerTimeLine] = useState("");
  const [tradeDecision, setTradeDecision] = useState("");
  const [payoutDetails, setPayoutDetails] = useState();
  const { connection, sendMessage, lastJsonMessage } = useWebsocketContext();
  const [username, setUserName] = useState("");
  const [isReviewModalVisible, setIsReviewModalVisible] = useState(false);

  console.log("activeStep", activeStep);
  // websocket
  useEffect(() => {
    if (connection && lastJsonMessage) {
      const dataValue: any = lastJsonMessage;
      const {
        action,
        order_id,
        message,
        flag,
        time_line_state,
        button,
        order_data,
        next_action,
        time_limit,
        stepper,
      } = dataValue?.data || {};

      // Only update activeStep if stepper is a valid number
      if (stepper !== undefined && stepper !== null) {
        setActiveStep(Number(stepper));
      }

      if (action === "accept_request" && order_id === orderNumber) {
        showToastSuccess(message);
        handleSendWebsocketMsg();
        // Only update activeStep if stepper is a valid number
        if (stepper !== undefined && stepper !== null) {
          setActiveStep(Number(stepper));
        }
      } else if (action === "reject_request" && order_id === orderNumber) {
        showToastError(message);
      } else if (action === "trade_cancelled" && order_id === orderNumber) {
        showToastError(dataValue?.message);
        setTimeout(() => {
          router.push("/searchads");
        }, 3000);
      } else if (dataValue?.type === "order_details") {
        // console.log("from trade page");
        setTradeData(order_data);
        setPayoutDetails(order_data?.data?.payout_data);
        setTimeLimit(time_limit);
      } else if (time_limit) {
        setTimeLimit(time_limit?.left_time_in_milliseconds);
      } else if (time_line_state && button) {
        flag === "user" ? setBtnNameUser(button) : setBtnNamePeer(button);
      } else if (dataValue?.error) {
        showToastError(dataValue?.error);
      }
      if (flag === "peer" && message) {
        showToastSuccess(message);
      } else if (flag === "user" && message) {
        showToastSuccess(message);
      } else if (action === "done_by_peer" && message) {
        showToastSuccess(message);
      } else if (action === "done_by_user" && message) {
        showToastSuccess(message);
      } else if (action === "increase_trade_time" && message) {
        showToastSuccess(message);
      }
      if (flag === "user") {
        setUserTimeLine(time_line_state);
      }
      if (flag === "peer") {
        setPeerTimeLine(time_line_state);
      }

      if (userTimeLine === "Trade_Completed") {
        const payload = {
          action: "done_by_user",
          order_id: orderNumber,
        };
        sendMessage(JSON.stringify(payload));
        setIsReviewModalVisible(true);
      }

      // console.log("socketConnection", dataValue);
    }
  }, [lastJsonMessage, orderNumber]);

  const handleCancelTrade = () => {
    const payload = {
      action: "trade_cancelled",
      order_id: orderNumber,
    };
    sendMessage(JSON.stringify(payload));
    showToastError("trade cancelled");
    // setTimeout(() => {
    //   router.push("/searchads");
    // }, 1000);
  };
  const handleSendWebsocketMsg = () => {
    const payload = {
      action: "get_order_details",
      order_id: orderNumber,
    };
    sendMessage(JSON.stringify(payload));
  };

  const sendPaymentFromPeerToSender = async () => {
    if (peerTimeLine === "Payment_Received") {
      const payload = {
        action: "received_payment_by_peer",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }
    if (peerTimeLine === "Pay_money_to_User") {
      const payload = {
        action: "send_money_to_user",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }
    if (peerTimeLine === "Paid_money_to_User") {
      const payload = {
        action: "initiate_payment_to_user",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }
    if (peerTimeLine === "Payment_Received_by_User") {
      const payload = {
        action: "done_by_peer",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }
    if (peerTimeLine === "Trade_Completed") {
      const payload = {
        action: "done_by_peer",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }
  };

  const sendPaymentFromSenderToPeer = async () => {
    if (userTimeLine === "Pay_Money_to_Peer") {
      const payload = {
        action: "send_money_to_peer",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }

    if (userTimeLine === "Paid_money_to_peer") {
      const payload = {
        action: "initiate_payment",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }

    if (userTimeLine === "Payment_Received_by_User") {
      const payload = {
        action: "received_payment_by_user",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
    }
    if (userTimeLine === "Trade_Completed") {
      const payload = {
        action: "done_by_user",
        order_id: orderNumber,
      };
      sendMessage(JSON.stringify(payload));
      if (passedType === "user") {
        setIsReviewModalVisible(true);
      }
    }
  };

  const getTradeHistoryUser = () => {
    const payload = {
      action: "get_trade_history_user_data",
      order_id: orderNumber,
    };
    sendMessage(JSON.stringify(payload));
  };
  const getTradeHistoryPeer = () => {
    const payload = {
      action: "get_trade_history_peer_data",
      order_id: orderNumber,
    };
    sendMessage(JSON.stringify(payload));
  };

  useEffect(() => {
    passedType === "user"
      ? getTradeHistoryUser()
      : console.log("not running peer History");
  }, []);

  useEffect(() => {
    {
      passedType !== "user"
        ? getTradeHistoryPeer()
        : console.log("not running  user History");
    }
  }, []);

  useEffect(() => {
    handleSendWebsocketMsg();
  }, []);

  // websocket

  async function getValueFor(key: any) {
    try {
      let result = await SecureStore.getItemAsync(key);
      if (result) {
        setUserName(result);
        return result;
      } else {
        console.error("No value found for the given key.");
        return null;
      }
    } catch (error) {
      console.error("Error retrieving value from SecureStore:", error);
      showToastError("Error retrieving stored User Name.");
      return null;
    }
  }

  const getUserName = async () => {
    try {
      const res = await getValueFor("userName");
      if (res) {
        setUserName(res);
      }
    } catch (error) {
      console.error("Error fetching user Name:", error);
      showToastError("Failed to retrieve user Name.");
    }
  };

  useEffect(() => {
    getUserName();
    // console.log("WEBSOCKET CONNECTED");
    if (connection && lastJsonMessage) {
      // console.log("WEBSOCKET11 CONNECTED");
    }
  }, [connection, lastJsonMessage]);

  // Handlers for the review modal
  const handleReviewSubmit = (rating: number, comment: string) => {
    console.log("Review Submitted:", { rating, comment, orderNumber });
    // TODO: Send review data via WebSocket or API call
    const payload = {
      action: "submit_trade_review", // Define appropriate action name
      order_id: orderNumber,
      rating: rating,
      comment: comment,
    };
    sendMessage(JSON.stringify(payload));
    setIsReviewModalVisible(false); // Close modal after submission
    // Optionally navigate away or show success message
    showToastSuccess("Review submitted successfully!");
    // Optionally navigate back after review
    // setTimeout(() => {
    //   router.push("/searchads"); // Or wherever appropriate
    // }, 1500);
  };

  const handleModalClose = () => {
    setIsReviewModalVisible(false);
    // Optionally navigate away if the user closes without reviewing
    // setTimeout(() => {
    //  router.push("/searchads"); // Or wherever appropriate
    // }, 500);
  };

  return (
    <Layout>
      <SafeAreaView className=" bg-white">
        <ScrollView nestedScrollEnabled={true} className="p-4">
          <View className="flex flex-row  items-center mb-4">
            <View className="bg-slate-300 mr-5 rounded-md">
              <Text className="px-6 py-2 font-pmedium text-lg">BUY</Text>
            </View>
            <View>
              <Text className="font-pmedium text-md">
                USDT from{" "}
                {tradeData?.flag
                  ? tradeData.flag === "user"
                    ? `${tradeData?.peer_details?.firstname} ${tradeData?.peer_details?.lastname}`
                    : `${tradeData?.user_details?.firstname} ${tradeData?.user_details?.lastname}`
                  : "Pending..."}
              </Text>
            </View>
          </View>
          <View>
            <Text className="font-pmedium text-md">The order is created</Text>
          </View>
          <View>
            <Text className="font-pmedium text-md">
              Trade window Status -{" "}
              <TradeTimer
                duration={timeLimit?.left_time_in_milliseconds}
                orderNumber={orderNumber}
              />
            </Text>
          </View>
          <View>
            <Text className="font-pmedium text-md">
              Order Number - {orderNumber}
            </Text>
          </View>
          <View>
            <Text className="font-pmedium text-md">
              Time created - {tradeData?.listing_data?.time_limit}
            </Text>
          </View>

          <View className="w-full border-dashed border-[0.5px] mt-3"></View>
          <View className="w-full h-12 flex justify-center items-center bg-yellow-300 rounded-md  mt-3">
            <Text className="text-center font-pmedium text-lg">
              You are the sender
            </Text>
          </View>
          <View className="w-full flex justify-center items-start my-5">
            <Text className="text-center font-pmedium text-lg">Next Step:</Text>
            {activeStep === 0 ? (
              <View>
                {passedType === "user" ? (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Wait for the peer to accept the trade request
                  </Text>
                ) : (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    hey there
                  </Text>
                )}
              </View>
            ) : activeStep === 1 ? (
              <View>
                {passedType === "user" ? (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Click on the Pay to peer Button to Initialize payment to
                    peer
                  </Text>
                ) : (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Please wait for the user to Initialize payment to peer
                  </Text>
                )}
              </View>
            ) : activeStep === 2 ? (
              <View>
                {passedType === "user" ? (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Now make the payment first and then Click on paid to Peer
                    button
                  </Text>
                ) : (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Please wait for the user to confirm payment to Peer
                  </Text>
                )}
              </View>
            ) : activeStep === 3 ? (
              <View>
                {passedType === "user" ? (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Wait for the Peer to mark the payment as Received
                  </Text>
                ) : (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Click on the Payment Received button to confirm payment
                    received from the user
                  </Text>
                )}
              </View>
            ) : activeStep === 4 ? (
              <View>
                {passedType === "user" ? (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Wait for the Peer to Initailize the payment to you
                  </Text>
                ) : (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Click on the Pay to User button to initialize the payment to
                    the sender
                  </Text>
                )}
              </View>
            ) : activeStep === 5 ? (
              <View>
                {passedType === "user" ? (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Wait for the Peer to confirm payment to you
                  </Text>
                ) : (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Make the payment first then Click on the Paid to User button
                    to confirm payment to the sender
                  </Text>
                )}
              </View>
            ) : activeStep === 6 ? (
              <View>
                {passedType === "user" ? (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Click on the Payment received button to confirm payment
                    received from the peer
                  </Text>
                ) : (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Wait for the user to confirm payment received from you
                  </Text>
                )}
              </View>
            ) : activeStep === 7 ? (
              <View>
                {passedType === "user" ? (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Click on the Trade Completed button to mark the trade as
                    completed
                  </Text>
                ) : (
                  <Text className="text-sm bg-blue-500 text-white p-1 rounded-md">
                    Click on the Trade Completed button to mark the trade as
                    completed
                  </Text>
                )}
              </View>
            ) : null}
          </View>

          <View className="">
            <Text className="font-pregular text-lg">
              Currency Pair : {tradeData?.currency_from} -
              {tradeData?.currency_to}
            </Text>
            <Text className="font-pregular text-lg">
              Rate : {tradeData?.listing_data.final_trade_fee.toFixed(2)}
            </Text>
            <Text className="font-pregular text-lg">
              Amount : {tradeData?.trade_amount}
            </Text>
            <Text className="font-pregular text-lg">
              Limit: {tradeData?.listing_data?.min_liquidity} -
              {tradeData?.listing_data?.max_liquidity}
            </Text>
          </View>
          <Text className="font-pregular text-md my-5">
            Please make payment to this account to fund your trade with this
            peer.
          </Text>
          <View className="w-full flex-row  border-[1px] border-gray-300 rounded-md ">
            <View className="w-4/12  border-r-[1px]  border-gray-300 p-3">
              <View className="w-full  bg-slate-200 p-1 rounded-md">
                <Text className="font-pregular text-md text-blue-600">
                  {tradeData?.listing_data?.payin_option}
                </Text>
              </View>
              <View className="w-full mt-2">
                <Text className="font-pmedium text-md text-gray-800">
                  {tradeData?.listing_data?.terms_and_conditions}
                </Text>
              </View>
            </View>
            <View className="w-8/12 p-3">
              <View className="py-2">
                <Text className="font-pmedium">
                  Name:{" "}
                  {tradeData?.flag
                    ? tradeData.flag === "user"
                      ? `${tradeData?.peer_details?.firstname} ${tradeData?.peer_details?.lastname}`
                      : `${tradeData?.user_details?.firstname} ${tradeData?.user_details?.lastname}`
                    : "Pending..."}
                </Text>
              </View>
              <View className="pt-2">
                <Text className="font-pmedium">Payment Method: </Text>
              </View>
              <View className="py-2">
                <Text className="font-pmedium">
                  {tradeData?.flag === "peer" ? (
                    tradeData?.data?.payout_option
                  ) : Array.isArray(tradeData?.data?.payin_data) ? (
                    tradeData?.data?.payin_data.map((item, index) => (
                      <View key={index} className="w-full flex flex-row">
                        <Text style={{ fontWeight: "bold" }}>{item.key}: </Text>
                        <Text>
                          {"  "}
                          {item.value}
                        </Text>
                      </View>
                    ))
                  ) : (
                    <Text>
                      <Text style={{ fontWeight: "bold" }}>
                        {tradeData?.data?.payin_data}
                      </Text>
                    </Text>
                  )}
                </Text>
              </View>
              <View className="py-2">
                <Text className="font-pmedium">
                  Payment Details -{" "}
                  {tradeData?.flag === "peer"
                    ? tradeData?.data?.payout_option
                    : tradeData?.data?.payin_option}
                </Text>
              </View>
            </View>
          </View>
          <Text className="font-pregular text-md my-5  text-blue-600">
            Please upload a receipt image in the chat to confirm payment has
            been sent
          </Text>
          <View className="w-full">
            <View className="w-full flex-row justify-around">
              {tradeData?.flag === "peer" ? (
                <TouchableOpacity
                  onPress={sendPaymentFromSenderToPeer}
                  className="w-[48%] h-12 bg-yellow-400 flex justify-center items-center rounded-md"
                >
                  <Text className="font-pmedium text-md px-6">
                    {btnNamePeer}
                  </Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity
                  onPress={sendPaymentFromSenderToPeer}
                  className="w-[48%] h-12 bg-yellow-400 flex justify-center items-center rounded-md"
                >
                  <Text className="font-pmedium text-md px-6">
                    {btnNameUser}
                  </Text>
                </TouchableOpacity>
              )}
              <View className="w-[48%] h-12 bg-gray-200 flex justify-center items-center rounded-md">
                <TouchableOpacity onPress={handleCancelTrade}>
                  <Text className="font-pmedium text-md px-6">Cancel</Text>
                </TouchableOpacity>
              </View>
            </View>
            <View className="w-full my-3  h-12 bg-red-600 flex justify-center items-center rounded-md">
              <Text className="font-pmedium text-white text-md px-6">
                Report
              </Text>
            </View>
          </View>
          {/* <View className="w-full my-3  h-12 bg-red-600 flex justify-center items-center rounded-md"> */}

          {/* </View> */}
          {/* <View className=" my-3 mb-10  rounded-md">
            <ChatScreen orderNumber={orderNumber} />
          </View> */}
        </ScrollView>
        {/* Render the modal */}
        <TradeReviewModal
          isVisible={isReviewModalVisible}
          onClose={handleModalClose}
          onSubmit={handleReviewSubmit}
          tradeOrderId={orderNumber || ""}
        />
      </SafeAreaView>
    </Layout>
  );
};

export default Tradepage;
