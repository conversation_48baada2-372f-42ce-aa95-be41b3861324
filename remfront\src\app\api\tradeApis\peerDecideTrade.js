import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const peerDecideTradeApi = async (orderId, decision) => {
  const res = await axios({
    url: `${Base_url}/peer-trade-request/?order_id=${orderId}&flag=${decision}`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
