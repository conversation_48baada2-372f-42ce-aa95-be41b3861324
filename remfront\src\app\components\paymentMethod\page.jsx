"use client";
import { useEffect, useState } from "react";
import styles from "./pay.module.css";

const page = ({
  PmessageAccepted,
  PmessagePayout,
  PcurrencyTo,
  PCurrencyFrom,
  PaymentMdataAccepted,
  PaymentMdataPayout,
  onDataFromChildAccepted,
  onDataFromChildPayout,
  id,
  resetTrigger,
}) => {
  const [selectedPaymentMethodAccepted, setSelectedPaymentMethodAccepted] = useState("");
  const [selectedPaymentMethodPayout, setSelectedPaymentMethodPayout] = useState("");
  const [selectedCardIndexAccepted, setSelectedCardIndexAccepted] = useState(null);
  const [selectedCardIndexPayout, setSelectedCardIndexPayout] = useState(null);

  const handleSelectedPaymentMethodAccepted = (paymentMethod, index) => {
    setSelectedCardIndexAccepted(index);
    setSelectedPaymentMethodAccepted(paymentMethod);
  };

  const handleSelectedPaymentMethodPayout = (paymentMethod, index) => {
    setSelectedCardIndexPayout(index);
    setSelectedPaymentMethodPayout(paymentMethod);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      onDataFromChildPayout(selectedPaymentMethodPayout);
    }, 300);
    return () => clearTimeout(timer);
  }, [selectedPaymentMethodPayout]);

  useEffect(() => {
    onDataFromChildAccepted(selectedPaymentMethodAccepted);
  }, [selectedPaymentMethodAccepted]);

  useEffect(() => {
    if (resetTrigger) {
      setSelectedPaymentMethodAccepted("");
      setSelectedPaymentMethodPayout("");
      setSelectedCardIndexAccepted(null);
      setSelectedCardIndexPayout(null);
    }
  }, [resetTrigger]);

  return (
    <div className={styles.paymentContainer}>
      <div className={styles.paymentCard}>
        <div className={styles.cardHeader}>
          <h3 className={styles.cardTitle}>Payment Methods</h3>
          <p className={styles.cardSubtitle}>Select your preferred payment options</p>
        </div>

        <div className={styles.paymentSections}>
          {/* Send From Section */}
          <div className={styles.paymentSection}>
            <div className={styles.sectionHeader}>
              <span className={styles.sectionLabel}>Send From</span>
              {selectedPaymentMethodAccepted && (
                <span className={styles.selectedIndicator}>{selectedPaymentMethodAccepted}</span>
              )}
            </div>
            
            {PmessageAccepted === "Data found." ? (
              <div className={styles.methodsList}>
                {PaymentMdataAccepted.map((payM, index) => (
                  <button
                    key={index}
                    className={`${styles.methodItem} ${
                      selectedCardIndexAccepted === index ? styles.methodItemSelected : ''
                    }`}
                    onClick={() => handleSelectedPaymentMethodAccepted(payM.payment_method, index)}
                  >
                    <div className={styles.methodContent}>
                      <div className={styles.methodIcon}>
                        <span className={styles.methodInitial}>
                          {payM.payment_method.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <span className={styles.methodName}>{payM.payment_method}</span>
                    </div>
                    {selectedCardIndexAccepted === index && (
                      <div className={styles.checkIcon}>
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                          <path d="M16.6667 5L7.5 14.1667L3.33333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            ) : (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>
                  <svg width="32" height="32" viewBox="0 0 48 48" fill="none">
                    <path d="M24 44C35.0457 44 44 35.0457 44 24C44 12.9543 35.0457 4 24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M24 16V24" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M24 32H24.02" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <p className={styles.emptyText}>Please select a currency to view available payment methods</p>
              </div>
            )}
          </div>

          {/* Divider */}
          <div className={styles.sectionDivider}></div>

          {/* Receive To Section */}
          <div className={styles.paymentSection}>
            <div className={styles.sectionHeader}>
              <span className={styles.sectionLabel}>Receive To</span>
              {selectedPaymentMethodPayout && (
                <span className={styles.selectedIndicator}>{selectedPaymentMethodPayout}</span>
              )}
            </div>
            
            {PmessagePayout === "Data found." ? (
              <div className={styles.methodsList}>
                {PaymentMdataPayout.map((payM, index) => (
                  <button
                    key={index}
                    className={`${styles.methodItem} ${
                      selectedCardIndexPayout === index ? styles.methodItemSelected : ''
                    }`}
                    onClick={() => handleSelectedPaymentMethodPayout(payM.payment_method, index)}
                  >
                    <div className={styles.methodContent}>
                      <div className={styles.methodIcon}>
                        <span className={styles.methodInitial}>
                          {payM.payment_method.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <span className={styles.methodName}>{payM.payment_method}</span>
                    </div>
                    {selectedCardIndexPayout === index && (
                      <div className={styles.checkIcon}>
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                          <path d="M16.6667 5L7.5 14.1667L3.33333 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                    )}
                  </button>
                ))}
              </div>
            ) : (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>
                  <svg width="32" height="32" viewBox="0 0 48 48" fill="none">
                    <path d="M24 44C35.0457 44 44 35.0457 44 24C44 12.9543 35.0457 4 24 4C12.9543 4 4 12.9543 4 24C4 35.0457 12.9543 44 24 44Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M24 16V24" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M24 32H24.02" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <p className={styles.emptyText}>Please select a currency to view available payment methods</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
