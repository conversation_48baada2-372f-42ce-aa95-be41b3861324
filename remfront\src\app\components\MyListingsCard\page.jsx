"use client";
import { useState, useEffect, useRef } from "react";
import styles from "./myListingsCard.module.css";
import { useRouter } from "next/navigation";
import {
  disableOneListing,
  disableAllListings,
} from "../../api/myListingsApis/mylistings";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
// import { useRouter } from "next/router";
const page = ({
  id,
  currency_accepted,
  currency_payout,
  available_liquidity,
  min_liquidity,
  max_liquidity,
  fx_rate,
  final_trade_fee,
  payIn,
  payOut,
  is_deleted,
  time_limit,
  user_payment_option_id,
  terms_and_conditions,
  listingDisabled,
  trade_fee_percent,
}) => {
  const router1 = useRouter();
  const ref = useRef(null);

  const [isChecked, setIsChecked] = useState(is_deleted);
  const router = useRouter();

  const handleEditListing = () => {
    router.push(
      `/pages/addlisting/${id}?currencyAccepted=${currency_accepted}&currencyPayout=${currency_payout}&availableLiquidity=${available_liquidity}&minLiquidity=${min_liquidity}&maxLiquidity=${max_liquidity}&fxRate=${fx_rate}&finalTradeFee=${final_trade_fee}&trade_fee_percent=${trade_fee_percent}&payIn=${payIn}&payOut=${payOut}&time_limit=${time_limit}&user_payment_option_id=${user_payment_option_id}&terms=${terms_and_conditions}`
    );
  };

  const disableIndividualListing = async () => {
    try {
      listingDisabled(true);
      const res = await customFetchWithToken.delete(`/delete-listings/${id}`);
      setIsChecked(!isChecked);
      router1.refresh();

      if (!ref.current) {
        toast.success(res.data.message);
      }
      ref.current = true;
    } catch (error) {
      console.log(error);
    }
  };
  // Update the checkbox state if 'is_deleted' prop changes
  useEffect(() => {
    setIsChecked(is_deleted);
  }, [is_deleted]);

  return (
    <article className={styles.listingCard} aria-labelledby={`listing-${id}`}>
      {/* Card Header */}
      <header className={styles.cardHeader}>
        <div className={styles.listingId}>
          <span className={styles.idLabel}>ID:</span>
          <span className={styles.idValue} id={`listing-${id}`}>{id}</span>
        </div>
        <div className={styles.cardActions}>
          <button
            className={styles.editButton}
            onClick={handleEditListing}
            aria-label={`Edit listing ${id}`}
            title="Edit listing"
          >
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              aria-hidden="true"
            >
              <path
                d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M18.5 2.50001C18.8978 2.10219 19.4374 1.87869 20 1.87869C20.5626 1.87869 21.1022 2.10219 21.5 2.50001C21.8978 2.89784 22.1213 3.4374 22.1213 4.00001C22.1213 4.56262 21.8978 5.10219 21.5 5.50001L12 15L8 16L9 12L18.5 2.50001Z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
          <div className={styles.toggleContainer}>
            <label className={styles.toggleLabel} htmlFor={`toggle-${id}`}>
              <span className={styles.toggleText}>
                {isChecked ? "Disabled" : "Active"}
              </span>
              <button
                id={`toggle-${id}`}
                className={`${styles.toggleButton} ${isChecked ? styles.toggleDisabled : styles.toggleActive}`}
                onClick={disableIndividualListing}
                aria-pressed={isChecked}
                aria-label={`${isChecked ? "Enable" : "Disable"} listing ${id}`}
              >
                <span className={styles.toggleSlider}></span>
              </button>
            </label>
          </div>
        </div>
      </header>

      {/* Card Content */}
      <div className={styles.cardContent}>
        {/* Currency Information */}
        <div className={styles.currencySection}>
          <div className={styles.currencyPair}>
            <div className={styles.currencyItem}>
              <span className={styles.currencyLabel}>Pay-in Currency</span>
              <span className={styles.currencyValue}>{currency_accepted}</span>
            </div>
            <div className={styles.currencyArrow}>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                <path d="M5 12H19M19 12L12 5M19 12L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <div className={styles.currencyItem}>
              <span className={styles.currencyLabel}>Pay-out Currency</span>
              <span className={styles.currencyValue}>{currency_payout}</span>
            </div>
          </div>
        </div>

        {/* Financial Details */}
        <div className={styles.detailsGrid}>
          <div className={styles.detailItem}>
            <span className={styles.detailLabel}>Available Liquidity</span>
            <span className={styles.detailValue}>{available_liquidity}</span>
          </div>
          <div className={styles.detailItem}>
            <span className={styles.detailLabel}>Min Liquidity</span>
            <span className={styles.detailValue}>{min_liquidity}</span>
          </div>
          <div className={styles.detailItem}>
            <span className={styles.detailLabel}>Max Liquidity</span>
            <span className={styles.detailValue}>{max_liquidity}</span>
          </div>
          <div className={styles.detailItem}>
            <span className={styles.detailLabel}>FX Rate</span>
            <span className={styles.detailValue}>
              {fx_rate ? Number(fx_rate).toFixed(4) : "N/A"}
            </span>
          </div>
          <div className={styles.detailItem}>
            <span className={styles.detailLabel}>Trade Fee</span>
            <span className={styles.detailValue}>{final_trade_fee}</span>
          </div>
          <div className={styles.detailItem}>
            <span className={styles.detailLabel}>Time Limit</span>
            <span className={styles.detailValue}>{time_limit} min</span>
          </div>
        </div>

        {/* Payment Methods */}
        <div className={styles.paymentMethods}>
          <div className={styles.paymentMethod}>
            <span className={styles.paymentLabel}>Pay-in Method</span>
            <span className={styles.paymentValue}>{payIn || "Not specified"}</span>
          </div>
          <div className={styles.paymentMethod}>
            <span className={styles.paymentLabel}>Pay-out Method</span>
            <span className={styles.paymentValue}>{payOut || "Not specified"}</span>
          </div>
        </div>

        {/* Terms and Conditions */}
        {terms_and_conditions && (
          <div className={styles.termsSection}>
            <span className={styles.termsLabel}>Terms & Conditions</span>
            <p className={styles.termsText}>{terms_and_conditions}</p>
          </div>
        )}
      </div>
    </article>
  );
};

export default page;
