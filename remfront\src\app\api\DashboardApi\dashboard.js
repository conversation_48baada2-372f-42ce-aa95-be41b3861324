import axios from "axios";
import { apiHandlerWrapper } from "@/app/utils/apiHandlerWrapper";
require("dotenv").config();

const BaseUrl = process.env.NEXT_PUBLIC_Base_URL;

export const getTransactionTableApi = async () => {
  const res = apiHandlerWrapper("get-transaction", "GET");
  return res;
};
export const getDashboardDataApi = async () => {
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
  }
  const res = await axios({
    url: `${BaseUrl}/dashboard/`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "GET",
  });
  return res;
};
