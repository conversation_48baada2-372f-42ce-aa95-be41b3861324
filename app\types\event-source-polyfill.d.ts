declare module 'event-source-polyfill' {
  export class EventSourcePolyfill implements EventSource {
    constructor(url: string | URL, eventSourceInitDict?: EventSourceInit & { headers?: Record<string, string> });
    readonly CLOSED: number;
    readonly CONNECTING: number;
    readonly OPEN: number;
    readonly url: string;
    readonly readyState: number;
    readonly withCredentials: boolean;
    onopen: (event: Event) => void;
    onmessage: (event: MessageEvent) => void;
    onerror: (event: Event) => void;
    addEventListener<K extends keyof EventSourceEventMap>(type: K, listener: (this: EventSource, ev: EventSourceEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
    removeEventListener<K extends keyof EventSourceEventMap>(type: K, listener: (this: EventSource, ev: EventSourceEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    dispatchEvent(event: Event): boolean;
    close(): void;
  }
} 