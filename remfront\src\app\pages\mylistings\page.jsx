"use client";
import { useState, useEffect, useRef } from "react";
import axios from "axios";
import styles from "./mylistings.module.css";
import Layout from "../../components/Layout/page";
import MyListingsCard from "../../components/MyListingsCard/page";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import Login from "@/app/sign/login/page";
import { useRouter } from "next/navigation";
import ReactPaginate from "react-paginate";
import {
  getListings,
  disableAllListings,
  enableAllListings,
} from "@/app/api/myListingsApis/mylistings";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import LoadingSpinner from "@/app/components/LoadingSpinner/page";

const page = () => {
  const router = useRouter();
  const authTokenRef = useRef(null);
  const userEmailRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const [loadCurrencyTo, setLoadCurrencyTo] = useState([]);
  const [currencyTo, setCurrencyTo] = useState("");
  const [currencyFrom, setCurrencyFrom] = useState("");
  const [listingId, setListingId] = useState("");
  const [fetchListResults, setFetchListingResults] = useState([]);
  const [page, setPage] = useState(1);
  const [showActivePagination, setShowActivePagination] = useState(false);
  const [showDisablePagination, setShowDisablePagination] = useState(false);
  const [isDisabledListings, setIsDisabledListings] = useState(false);
  const [loading1, setLoading1] = useState(false);
  const [allIsActive, setAllIsActive] = useState(true);
  const [activeIsActive, setActiveIsActive] = useState(false);
  const [disabledIsActive, setDisabledIsActive] = useState(false);

  let authToken;
  if (typeof window !== "undefined") {
    authToken = sessionStorage.getItem("user");
  }
  const [noOfRecords, setNoOfRecords] = useState(0);
  const itemsPerPage = 10;
  const pageCount = Math.ceil(noOfRecords / itemsPerPage);

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  // search by currency

  const handleCurrencyFrom = (event) => {
    setCurrencyFrom(event.target.value);
  };

  const handleCurrencyTo = (event) => {
    setCurrencyTo(event.target.value);
  };

  const handleListingid = (e) => {
    const value = e.target.value;
    if (value.length > 20) {
      setListingId("");
    } else {
      setListingId(value);
    }
  };
  const fetchCurrencyDataFrom = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/currency-list/?currency_from=true`
      );
      const data = await resCurrency.json();
      setLoadCurrencyFrom(data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchCurrencyDataTo = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/currency-list/?currency_to=true`
      );
      const data = await resCurrency.json();
      setLoadCurrencyTo(data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  useEffect(() => {
    fetchCurrencyDataTo();
  }, []);
  useEffect(() => {
    fetchCurrencyDataFrom();
  }, []);

  const fetchListingsByCurrency = async () => {
    setIsDisabledListings(false);
    setPage(1);
    setLoading(true);
    try {
      const res = await customFetchWithToken.get(
        `/get-listings/?currency_accepted=${currencyFrom}&currency_payout=${currencyTo}&id=${listingId}`
      );

      const filteredListings = res.data.results.filter(
        (listing) => listing.is_deleted === false
      );
      setNoOfRecords(res.data.count);

      setShowDisablePagination(false);
      setShowActivePagination(true);
      setFetchListingResults(filteredListings);
      //   return res;
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  // search by currency

  const getDisabledListingsOnly = () => {
    setDisabledIsActive(true);
    setActiveIsActive(false);
    setAllIsActive(false);
    if (!isDisabledListings) {
      setIsDisabledListings(true); // Mark that we want to fetch disabled listings
      setPage(1); // Reset page to 1 (this will trigger the useEffect)
    }
  };

  // Effect to fetch listings based on the page and listing type (active or disabled)
  useEffect(() => {
    const fetchListings = async () => {
      setLoading1(true);
      try {
        let res;
        if (isDisabledListings) {
          res = await customFetchWithToken.get(
            `/get-listings/?page=${page}&user_data=true&active=false`
          );
          const filteredListings = res.data.results.filter(
            (listing) => listing.is_deleted === true
          );
          setNoOfRecords(res.data.count);
          setShowActivePagination(false);
          setShowDisablePagination(true);
          setFetchListingResults(filteredListings);
        }
      } catch (error) {
        console.error("Error:", error);
      } finally {
        setLoading1(false);
      }
    };

    // Only fetch listings if the page is set or type of listing changes
    if (page && isDisabledListings !== null) {
      fetchListings();
    }
  }, [page, isDisabledListings]);

  const handlePageClick = async (event) => {
    setPage(event.selected + 1);
    setLoading(true);
    try {
      const resData = await customFetchWithToken.get(
        `/get-listings/?page=${event.selected + 1}&user_data=true&active=true`
      );
      // const resData = await response.json();

      const PassData = resData.data.results;
      if (PassData.length < 1) {
        toast.warn("No data available for this Search ");
      }

      setNoOfRecords(resData.data.count);
      setShowDisablePagination(false);
      setShowActivePagination(true);
      setFetchListingResults(PassData);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const handlePageClickDisabled = async (event) => {
    setPage(event.selected + 1);
    setLoading(true);
    try {
      const resData = await customFetchWithToken.get(
        `/get-listings/?page=${event.selected + 1}&user_data=true&active=false`
      );
      // const resData = await response.json();

      const PassData = resData.data.results;
      if (PassData.length < 1) {
        toast.warn("No data available for this Search ");
      }

      setNoOfRecords(resData.data.count);
      setShowActivePagination(false);
      setShowDisablePagination(true);
      setFetchListingResults(PassData);
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  const getAllListings = async () => {
    setAllIsActive(true);
    setActiveIsActive(false);
    setDisabledIsActive(false);
    setIsDisabledListings(false);
    setPage(1);
    setLoading(true);
    try {
      const res = await customFetchWithToken.get(
        `/get-listings/?&user_data=true`
      );

      const filteredListings = res.data.results;
      setNoOfRecords(res.data.count);

      setShowDisablePagination(false);
      setShowActivePagination(true);
      setFetchListingResults(filteredListings);
      //   return res;
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const getActiveListings = async () => {
    setActiveIsActive(true);
    setAllIsActive(false);
    setDisabledIsActive(false);

    setIsDisabledListings(false);
    setPage(1);
    setLoading(true);
    try {
      const res = await customFetchWithToken.get(
        `/get-listings/?page=${page}&user_data=true&active=true`
      );

      const filteredListings = res.data.results.filter(
        (listing) => listing.is_deleted === false
      );
      setNoOfRecords(res.data.count);

      setShowDisablePagination(false);
      setShowActivePagination(true);
      setFetchListingResults(filteredListings);
      //   return res;
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  const disableAllListingsFunc = async () => {
    try {
      const res = await customFetchWithToken.delete(
        "/disabled-enabled-listings/?flag=disable"
      );
      toast.success(res.data.message);
      // router.refresh();
    } catch (error) {
      console.log(error);
    }
  };
  const enableAllListingsFunc = async () => {
    try {
      const res = await customFetchWithToken.delete(
        "/disabled-enabled-listings/?flag=enable"
      );
      toast.success(res.data.message);
      // router.refresh();
    } catch (error) {
      console.log(error);
    }
  };

  const [dataFromChild, setDataFromChild] = useState(false);

  const handleListingDisabled = (data) => {
    setTimeout(() => {
      setDataFromChild(data);
    }, [1000]);
  };

  if (dataFromChild === true) {
    getActiveListings();
    setDataFromChild(false);
  }

  useEffect(() => {
    getActiveListings();
  }, []);

  if (!authToken) {
    router.push("/sign/login");
  }
  const myListingsTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>My Listings</h1>
      <p className={styles.pageSubtitle}>
        Manage your active and completed trading listings
      </p>
    </div>
  );

  return (
    <>
      <div className={styles.pageWrapper}>
        <Layout title={myListingsTitle}>
          {/* Header Section - Hidden on desktop, shown only on mobile */}
          <div className={styles.mobileHeader}>
            <div className={styles.headerContent}>
              <h1 className={styles.pageTitle}>My Listings</h1>
              <p className={styles.pageSubtitle}>
                Manage your active and completed trading listings
              </p>
            </div>
          </div>
          {/* Mobile Navigation */}
          <div className={styles.mobileNavContainer}>
            <div className={styles.mobileTabGroup}>
              <button
                className={`${styles.mobileTab} ${
                  allIsActive ? styles.mobileTabActive : ""
                }`}
                onClick={getAllListings}
                aria-pressed={allIsActive}
              >
                All Listings
              </button>
              <button
                className={`${styles.mobileTab} ${
                  activeIsActive ? styles.mobileTabActive : ""
                }`}
                onClick={getActiveListings}
                aria-pressed={activeIsActive}
              >
                Active Listings
              </button>
              <button
                className={`${styles.mobileTab} ${
                  disabledIsActive ? styles.mobileTabActive : ""
                }`}
                onClick={getDisabledListingsOnly}
                aria-pressed={disabledIsActive}
              >
                Disabled Listings
              </button>
            </div>
            <div className={styles.mobileActionButtons}>
              <button
                className={`${styles.actionButton} ${styles.disableButton}`}
                onClick={disableAllListingsFunc}
                aria-label="Disable all listings"
              >
                Disable All
              </button>
              <button
                className={`${styles.actionButton} ${styles.enableButton}`}
                onClick={enableAllListingsFunc}
                aria-label="Enable all listings"
              >
                Enable All
              </button>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className={styles.desktopNavContainer}>
            <div className={styles.tabGroup}>
              <button
                className={`${styles.tab} ${allIsActive ? styles.tabActive : ""}`}
                onClick={getAllListings}
                aria-pressed={allIsActive}
              >
                All Listings
              </button>
              <button
                className={`${styles.tab} ${activeIsActive ? styles.tabActive : ""}`}
                onClick={getActiveListings}
                aria-pressed={activeIsActive}
              >
                Active Listings
              </button>
              <button
                className={`${styles.tab} ${disabledIsActive ? styles.tabActive : ""}`}
                onClick={getDisabledListingsOnly}
                aria-pressed={disabledIsActive}
              >
                Disabled Listings
              </button>
            </div>
            <div className={styles.actionButtonGroup}>
              <button
                className={`${styles.actionButton} ${styles.disableButton}`}
                onClick={disableAllListingsFunc}
                aria-label="Disable all listings"
              >
                Disable All Listings
              </button>
              <button
                className={`${styles.actionButton} ${styles.enableButton}`}
                onClick={enableAllListingsFunc}
                aria-label="Enable all listings"
              >
                Enable All Listings
              </button>
            </div>
          </div>

          {/* Main Content Area */}
          <div className={styles.mainContent}>
            <div className={styles.contentWrapper}>
              {/* Filter Section */}
              <div className={styles.filterSection}>
                <div className={styles.filterCard}>
                  <h2 className={styles.filterTitle}>Filter Listings</h2>
                  <div className={styles.filterForm}>
                    <div className={styles.filterRow}>
                      <div className={styles.inputGroup}>
                        <label htmlFor="currencyPayin" className={styles.inputLabel}>
                          Pay-in Currency
                        </label>
                        <select
                          name="currencyPayin"
                          id="currencyPayin"
                          className={styles.selectInput}
                          onChange={handleCurrencyFrom}
                          value={currencyFrom}
                          aria-describedby="currencyPayin-help"
                        >
                          <option value="">Select a Pay-in Currency</option>
                          {loadCurrencyFrom?.map((currency, index) => (
                            <option key={index} value={currency.currency_code}>
                              {currency.currency_code}
                            </option>
                          ))}
                        </select>
                        <span id="currencyPayin-help" className={styles.inputHelp}>
                          Choose the currency you accept
                        </span>
                      </div>
                      <div className={styles.inputGroup}>
                        <label htmlFor="currencyPayout" className={styles.inputLabel}>
                          Pay-out Currency
                        </label>
                        <select
                          name="currencyPayout"
                          id="currencyPayout"
                          className={styles.selectInput}
                          onChange={handleCurrencyTo}
                          value={currencyTo}
                          aria-describedby="currencyPayout-help"
                        >
                          <option value="">Select a Pay-out Currency</option>
                          {loadCurrencyTo?.map((currency, index) => (
                            <option key={index} value={currency.currency_code}>
                              {currency.currency_code}
                            </option>
                          ))}
                        </select>
                        <span id="currencyPayout-help" className={styles.inputHelp}>
                          Choose the currency you pay out
                        </span>
                      </div>
                      <div className={styles.inputGroup}>
                        <label htmlFor="listingId" className={styles.inputLabel}>
                          Listing ID (Optional)
                        </label>
                        <input
                          type="number"
                          id="listingId"
                          name="listingId"
                          className={styles.textInput}
                          placeholder="Enter Listing ID"
                          onChange={handleListingid}
                          value={listingId}
                          aria-describedby="listingId-help"
                          maxLength="20"
                        />
                        <span id="listingId-help" className={styles.inputHelp}>
                          Filter by specific listing ID
                        </span>
                      </div>
                      <div className={styles.inputGroup}>
                        <button
                          type="button"
                          className={styles.searchButton}
                          onClick={fetchListingsByCurrency}
                          disabled={loading}
                          aria-label="Search listings with current filters"
                        >
                          {loading ? (
                            <span className={styles.buttonSpinner}></span>
                          ) : (
                            "Search"
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {/* Pagination - Top */}
              {(showActivePagination || showDisablePagination) && fetchListResults.length > 0 && (
                <div className={styles.paginationWrapper}>
                  <div className={styles.paginationContainer}>
                    <ReactPaginate
                      previousLabel={
                        <span className={styles.paginationArrow}>
                          ← Previous
                        </span>
                      }
                      nextLabel={
                        <span className={styles.paginationArrow}>
                          Next →
                        </span>
                      }
                      breakLabel={"..."}
                      breakClassName={styles.paginationBreak}
                      activeClassName={styles.paginationActive}
                      pageCount={pageCount}
                      onPageChange={showActivePagination ? handlePageClick : handlePageClickDisabled}
                      marginPagesDisplayed={1}
                      pageRangeDisplayed={3}
                      containerClassName={styles.paginationList}
                      pageClassName={styles.paginationItem}
                      pageLinkClassName={styles.paginationLink}
                      previousClassName={styles.paginationNav}
                      previousLinkClassName={styles.paginationNavLink}
                      nextClassName={styles.paginationNav}
                      nextLinkClassName={styles.paginationNavLink}
                      breakLinkClassName={styles.paginationBreakLink}
                      renderOnZeroPageCount={null}
                      aria-label="Listings pagination"
                    />
                  </div>
                  <div className={styles.paginationInfo}>
                    <span className={styles.resultsCount}>
                      Showing {fetchListResults.length} of {noOfRecords} listings
                    </span>
                  </div>
                </div>
              )}

              {/* Listings Grid */}
              <div className={styles.listingsSection}>
                {loading || loading1 ? (
                  <div className={styles.loadingContainer}>
                    <LoadingSpinner text="Loading listings..." />
                  </div>
                ) : (
                  <>
                    {fetchListResults.length > 0 ? (
                      <div className={styles.listingsGrid}>
                        {fetchListResults.map((listing, index) => (
                          <MyListingsCard
                            key={`${listing.id}-${index}`}
                            id={listing.id}
                            currency_accepted={listing.currency_accepted.currency_code}
                            currency_payout={listing.currency_payout.currency_code}
                            available_liquidity={listing.available_liquidity}
                            min_liquidity={listing.min_liquidity}
                            max_liquidity={listing.max_liquidity}
                            fx_rate={listing.indicative_fx_rate}
                            trade_fee_percent={listing.trade_fee}
                            final_trade_fee={listing.final_trade_fee}
                            payIn={listing.payin_option?.payment_method}
                            payOut={listing.payout_option?.payment_method}
                            is_deleted={listing.is_deleted}
                            time_limit={listing.time_limit}
                            terms_and_conditions={listing.terms_and_conditions}
                            listingDisabled={handleListingDisabled}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className={styles.emptyState}>
                        <div className={styles.emptyStateIcon}>
                          <svg
                            width="64"
                            height="64"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            aria-hidden="true"
                          >
                            <path
                              d="M9 11H15M9 15H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L19.7071 9.70711C19.8946 9.89464 20 10.149 20 10.4142V19C20 20.1046 19.1046 21 18 21H17Z"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                        <h3 className={styles.emptyStateTitle}>No Listings Available</h3>
                        <p className={styles.emptyStateDescription}>
                          {currencyFrom || currencyTo || listingId
                            ? "No listings match your current filters. Try adjusting your search criteria."
                            : "You haven't created any listings yet. Create your first listing to get started."}
                        </p>
                        {(currencyFrom || currencyTo || listingId) && (
                          <button
                            className={styles.clearFiltersButton}
                            onClick={() => {
                              setCurrencyFrom("");
                              setCurrencyTo("");
                              setListingId("");
                              getAllListings();
                            }}
                          >
                            Clear Filters
                          </button>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>

              {/* Pagination - Bottom */}
              {(showActivePagination || showDisablePagination) && fetchListResults.length > 0 && pageCount > 1 && (
                <div className={styles.paginationWrapper}>
                  <div className={styles.paginationContainer}>
                    <ReactPaginate
                      previousLabel={
                        <span className={styles.paginationArrow}>
                          ← Previous
                        </span>
                      }
                      nextLabel={
                        <span className={styles.paginationArrow}>
                          Next →
                        </span>
                      }
                      breakLabel={"..."}
                      breakClassName={styles.paginationBreak}
                      activeClassName={styles.paginationActive}
                      pageCount={pageCount}
                      onPageChange={showActivePagination ? handlePageClick : handlePageClickDisabled}
                      marginPagesDisplayed={1}
                      pageRangeDisplayed={3}
                      containerClassName={styles.paginationList}
                      pageClassName={styles.paginationItem}
                      pageLinkClassName={styles.paginationLink}
                      previousClassName={styles.paginationNav}
                      previousLinkClassName={styles.paginationNavLink}
                      nextClassName={styles.paginationNav}
                      nextLinkClassName={styles.paginationNavLink}
                      breakLinkClassName={styles.paginationBreakLink}
                      renderOnZeroPageCount={null}
                      aria-label="Listings pagination"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </Layout>
      </div>
      {/* ToastContainer removed - already provided globally in LayoutWrapper.js */}
    </>
  );
};

export default page;
