import React from "react";
import { View, Text } from "react-native";

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <View className="flex-1 justify-center items-center">
          <Text className="text-lg text-red-500">Something went wrong:</Text>
          <Text className="text-red-500">{this.state.error.message}</Text>
        </View>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
