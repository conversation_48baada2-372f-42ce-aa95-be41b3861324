"use client";
import { useState } from "react";
import QRCodeSVG from "qrcode.react";

const QRCodeGenerator = () => {
  const [text, setText] = useState("");

  const otpSecret = "MHFZO2ZXGNXM6ZZLCXMWLRHIQTDGSYVP";
  const issuer = "remflow";
  const email = "<EMAIL>";

  const otpAuthUrl = `otpauth://totp/${issuer}:${encodeURIComponent(
    email
  )}?secret=${otpSecret}&issuer=${encodeURIComponent(issuer)}`;

  return (
    <div>
      <h2>QR Code for Two-Factor Authentication</h2>
      {otpAuthUrl && <QRCodeSVG value={otpAuthUrl} />}
    </div>
  );
};

export default QRCodeGenerator;
