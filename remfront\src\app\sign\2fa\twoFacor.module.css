@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}


.bodyWrapper {
    height: 100vh;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    @media screen and (max-width: 576px) {
        width: 100%;
        /* margin: auto; */
        margin-top: 17px !important;
        margin-bottom: 60px !important;
    }
}

.container {
    width: 50%;
    height: 500px;
    border-radius: 12px;
    border: 1px solid #4153ED;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;

    @media screen and (max-width: 576px) {
        width: 95%;
        padding: 10px 30px;
        height: auto;
        text-align: center;
    }

}

.title {
    margin-bottom: 10px;
    font-weight: 600;
    font-size: 26px;
}




.email {
    display: flex;
    flex-direction: column;
    margin: 10px 0;
}

.email>label {
    margin-bottom: 10px;

    font-weight: bold;
    font-family: poppins;
}

.email>input {
    background-color: transparent;
    height: 35px;
    border-radius: 8px;
    outline: none;
    border: 1px solid #ccc;
    color: black;
    padding: 10px;
}

.loginBtnContainer {
    width: 65%;
    margin-top: 17px;
    margin-bottom: 60px;


    @media screen and (max-width: 576px) {
        width: 75%;
        margin: auto;
        margin-top: 17px !important;
        margin-bottom: 60px !important;
    }
}

.loginBtn {
    width: 100%;
    color: #4153ed;
    height: 42px;
    border-radius: 12px;
    border: 2px solid #4153ed;
    background: transparent;
    font-weight: 600;
    font-size: 16px;
    font-family: poppins;
    cursor: pointer;

    @media screen and (max-width: 576px) {
        font-size: 14px;
    }

}