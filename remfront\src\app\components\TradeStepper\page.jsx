"use client";
import React from "react";
import styles from "./tradeStepper.module.css";
import CustomStepper from "../CustomStepper/CustomStepper";

const Page = ({ activeStep }) => {
  return (
    <CustomStepper
      className={styles.stepperCont}
      steps={[
        { label: "Trade Accepted" },
        { label: "Payment to Peer" },
        { label: "Peer Received" },
        { label: "Payment to Sender" },
        { label: "Sender Received" },
      ]}
      activeStep={activeStep}
      completedColor="#10B981"
      activeColor="#ed1d24"
      inactiveColor="#D1D5DB"
    />
  );
};

export default Page;
