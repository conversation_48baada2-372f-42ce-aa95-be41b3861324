"use client";
import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import styles from "./profile.module.css";
import Layout from "../../components/Layout/page";
import ProfileImg from "../../../../public/assets/profile/profileImg.png";
import tick from "../../../../public/assets/tick.png";
import {
  userVerificationPeerApi,
  getPeerDetailsApi,
} from "@/app/api/UserRegistration/userRegistartion";

import Login from "@/app/sign/login/page";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import upload from "../../../../public/assets/upload.png";
import { useRouter } from "next/navigation";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const page = () => {
  const authTokenRef = useRef(null);
  const userIdRef = useRef(null);
  const [uploadMark, setUploadMark] = useState(false);
  const [uploadMark1, setUploadMark1] = useState(false);
  const [uploadMark2, setUploadMark2] = useState(false);
  const [proofOfAdderss, setProofOfAdderss] = useState("");
  const [sourceOfFunds, setSourceOfFunds] = useState("");
  const [bankStatement, setBankStatement] = useState("");
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [mobile, setMobile] = useState("");
  const [profilePic, setProfilePic] = useState("");
  const [phoneCountryArray, setPhoneCountryArray] = useState([]);
  const [countryPhone, setCountryPhone] = useState("");
  const [contact, setContact] = useState("");
  const [telegramId, setTelegramId] = useState("");
  const [whatsappId, setWhatsappId] = useState("");
  const [weChatId, setWeChatId] = useState("");
  const [otherMesesgingId, setOtherMesesgingId] = useState("");
  let userEmail;

  if (typeof window !== "undefined") {
    userEmail = localStorage.getItem("userEmail");
  }

  const phnCountryRef = useRef();

  const router = useRouter();

  let verificationStatus;
  let userIdNumber;
  let token;
  if (typeof window !== "undefined") {
    userIdNumber = localStorage.getItem("userID");
    token = sessionStorage.getItem("user");
    verificationStatus = localStorage.getItem("verificationStatus");

    if (token) {
      authTokenRef.current = token;
    }
    if (userIdNumber) {
      userIdRef.current = userIdNumber;
    }
  }

  if (!token) {
    router.push("/sign/login");
  }

  const Data1 = {
    telegram_id: telegramId,
    whatsapp_id: `+${countryPhone}${whatsappId}`,
    wechat_id: weChatId,
    any_other_id: otherMesesgingId,
  };
  const handleVerification = async () => {
    // if (secret && !verifyOtp) {
    //   toast.error("VerifyOTP first");
    //   return;
    // }
    toast.warn("Sending");

    try {
      const res = await customFetchWithToken.post("/peer-registration/", Data1);
      toast.success(res.data.message);
      // setTimeout(function () {
      //   router.push("/pages/searchads");
      // }, 1500);
    } catch (error) {
      toast.error(error.response.data.message);
      console.error(error);
      // setTimeout(function () {
      //   router.push("/pages/searchads");
      // }, 1500);
    }
  };

  const fetchPeerDetailsApi = async () => {
    try {
      const res = await customFetchWithToken.get("/get-peer-details/");
      setTelegramId(res.data.data?.telegram_id);
      setWhatsappId(res.data.data?.whatsapp_id);
      setWeChatId(res.data.data?.wechat_id);
      setOtherMesesgingId(res.data.data?.any_other_id);
    } catch (error) {
      console.error(error);
    }
  };

  // const fetchAndCreateFile1 = async (profilePicUrl) => {
  //   try {
  //     const response = await fetch(profilePicUrl);
  //     const blob = await response.blob();
  //     console.log("Original Blob type:", blob.type); // Debugging: check original blob type
  //     const fileType =
  //       blob.type === "binary/octet-stream" ? "image/jpeg" : blob.type;
  //     const file = new File([blob], "profile_picture", { type: fileType });
  //     console.log("Created File with overridden type:", file); // Debugging: check created File
  //     return file;
  //   } catch (error) {
  //     console.error("Error fetching or creating file:", error);
  //     return null;
  //   }
  // };



  const handleTelegramId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setTelegramId("");
    } else {
      setTelegramId(inputValue);
    }
  };

  const handleWhatsappId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setWhatsappId("");
    } else {
      setWhatsappId(inputValue);
    }
  };

  const handleWeChatId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setWeChatId("");
    } else {
      setWeChatId(inputValue);
    }
  };
  const handleOtherMesesgingId = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setOtherMesesgingId("");
    } else {
      setOtherMesesgingId(inputValue);
    }
  };

  // silver status

  const handleMobileNum = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");
    if (inputValue.length > 20) {
      setMobile("");
    } else {
      setMobile(inputValue);
    }
  };

  // getProfileInfoAPI

  const getProfileInfoAPIFunc = async () => {
    try {
      const res = await customFetchWithToken.get("/view-user-details/");
      setFirstName(res.data.data.firstname);
      setLastName(res.data.data.lastname);
      setEmail(res.data.data.email);
      setMobile(Number(res.data.data.mobile));
      setProfilePic(res.data.data.img_logo);
    } catch (error) {
      console.error(error);
    }
  };

  // getProfileInfoAPI

  const handleProofOfAddress = (e) => {
    // Array of allowed file extensions
    const allowedExtensions = ["image/jpeg", "image/png", "application/pdf"];

    // Check if the file has one of the allowed extensions
    if (allowedExtensions.includes(e.target.files[0].type)) {
      setProofOfAdderss(e.target.files[0]);
      setUploadMark(true);
    } else {
      // Optionally, show an error message or alert to inform the user about the invalid file type
      alert("Invalid file type. Only JPEG, PNG, and PDF files are allowed.");
    }
  };

  const handleSourceOfFunds = (e) => {
    const allowedExtensions = ["image/jpeg", "image/png", "application/pdf"];

    if (allowedExtensions.includes(e.target.files[0].type)) {
      setSourceOfFunds(e.target.files[0]);
      setUploadMark1(true);
    } else {
      alert("Invalid file type. Only JPEG, PNG, and PDF files are allowed.");
    }
  };

  const handleBankStatement = (e) => {
    const allowedExtensions = ["image/jpeg", "image/png", "application/pdf"];

    if (allowedExtensions.includes(e.target.files[0].type)) {
      setBankStatement(e.target.files[0]);
      setUploadMark2(true);
    } else {
      alert("Invalid file type. Only JPEG, PNG, and PDF files are allowed.");
    }
  };

  // silverHandleSubmitFunction

  const handleUserStatusDocsSubmit = async (e) => {
    e.preventDefault();
    const silverStatusFormData = new FormData();
    silverStatusFormData.append("user_id", userIdRef.current);
    silverStatusFormData.append("flag", "document");
    silverStatusFormData.append("bank_statement", bankStatement);
    silverStatusFormData.append("source_of_fund", sourceOfFunds);
    silverStatusFormData.append("proof_of_address", proofOfAdderss);
    if (!proofOfAdderss) {
      // Display an alert or handle the validation error as needed
      toast.error("Upload Prooof of Address");
      return;
    }
    if (!sourceOfFunds) {
      // Display an alert or handle the validation error as needed
      toast.error("Upload Source of Funds");
      return;
    }
    if (!bankStatement) {
      // Display an alert or handle the validation error as needed
      toast.error("Upload Bank Statement");
      return;
    }

    toast.warn("Sending");
    try {
      const res = await customFetchWithToken.post(
        "/upload-document/",
        silverStatusFormData
      );

      toast.success(res.data.message);
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
  };
  // silverHandleSubmitFunction

  // silver status

  //mobileCountry

  const getPhoneCountryDropDown = async () => {
    try {
      if (phnCountryRef.current == false) return;
      const res = await customFetchWithToken.get("/country-code/");
      setPhoneCountryArray(res.data.data);
      phnCountryRef.current = false;
    } catch (error) {
      console.error(error);
    }
  };

  //mobileCountry

  useEffect(() => {
    getProfileInfoAPIFunc();
  }, []);
  useEffect(() => {
    fetchPeerDetailsApi();
  }, []);
  useEffect(() => {
    getPhoneCountryDropDown();
  }, []);

  const profileTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Profile</h1>
      <p className={styles.pageSubtitle}>
        View and manage your personal information and account details
      </p>
    </div>
  );

  return (
    <>
      {/* {authTokenRef.current ? ( */}
      <div>
        <Layout title={profileTitle}>
          {verificationStatus === "User_Detail" ? (
            <div
              style={{
                fontSize: "12px",
                textAlign: "left",
                color: "gray",
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                width: "100%",
                marginTop: "30px",
              }}
            >
              <span style={{ marginRight: "5px" }}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  x="0px"
                  y="0px"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                >
                  <path d="M 12 0 C 5.371094 0 0 5.371094 0 12 C 0 18.628906 5.371094 24 12 24 C 18.628906 24 24 18.628906 24 12 C 24 5.371094 18.628906 0 12 0 Z M 12 2 C 17.523438 2 22 6.476563 22 12 C 22 17.523438 17.523438 22 12 22 C 6.476563 22 2 17.523438 2 12 C 2 6.476563 6.476563 2 12 2 Z M 12 5.8125 C 11.816406 5.8125 11.664063 5.808594 11.5 5.84375 C 11.335938 5.878906 11.183594 5.96875 11.0625 6.0625 C 10.941406 6.15625 10.851563 6.285156 10.78125 6.4375 C 10.710938 6.589844 10.6875 6.769531 10.6875 7 C 10.6875 7.226563 10.710938 7.40625 10.78125 7.5625 C 10.851563 7.71875 10.941406 7.84375 11.0625 7.9375 C 11.183594 8.03125 11.335938 8.085938 11.5 8.125 C 11.664063 8.164063 11.816406 8.1875 12 8.1875 C 12.179688 8.1875 12.371094 8.164063 12.53125 8.125 C 12.691406 8.085938 12.816406 8.03125 12.9375 7.9375 C 13.058594 7.84375 13.148438 7.71875 13.21875 7.5625 C 13.289063 7.410156 13.34375 7.226563 13.34375 7 C 13.34375 6.769531 13.289063 6.589844 13.21875 6.4375 C 13.148438 6.285156 13.058594 6.15625 12.9375 6.0625 C 12.816406 5.96875 12.691406 5.878906 12.53125 5.84375 C 12.371094 5.808594 12.179688 5.8125 12 5.8125 Z M 10.78125 9.15625 L 10.78125 18.125 L 13.21875 18.125 L 13.21875 9.15625 Z"></path>
                </svg>
              </span>
              Cannot change First Name and Last Name after submitting KYC
              documents!
            </div>
          ) : (
            ""
          )}

          <div className={styles.pageContainer}>
            {/* Header Section - Hidden on desktop, shown only on mobile */}
            <div className={styles.pageHeader}>
              <div className={styles.headerContent}>
                <h1 className={styles.pageTitle}>Profile</h1>
                <p className={styles.pageSubtitle}>
                  View and manage your personal information and account details
                </p>
              </div>
            </div>

            {/* Profile Information Card */}
            <div className={styles.profileCard}>
              <div className={styles.profileHeader}>
                <div className={styles.profileIcon}>👤</div>
                <div className={styles.profileInfo}>
                  <h2 className={styles.profileTitle}>Profile Information</h2>
                  <p className={styles.profileDescription}>
                    You can change your profile information in the Settings page
                  </p>
                </div>
              </div>

              <div className={styles.profileContent}>
                {/* Profile Picture Section */}
                <div className={styles.profilePictureSection}>
                  <div className={styles.profilePictureContainer}>
                    <Image
                      unoptimized={true}
                      src={profilePic || "/default-avatar.png"}
                      alt="Profile Picture"
                      width={120}
                      height={120}
                      className={styles.profilePicture}
                    />
                  </div>
                  <div className={styles.profilePictureInfo}>
                    <h3 className={styles.profileName}>{firstName} {lastName}</h3>
                    <p className={styles.profileEmail}>{email}</p>
                  </div>
                </div>

                {/* Profile Details Grid */}
                <div className={styles.profileDetailsGrid}>
                  <div className={styles.detailCard}>
                    <div className={styles.detailIcon}>👤</div>
                    <div className={styles.detailContent}>
                      <label className={styles.detailLabel}>First Name</label>
                      <div className={styles.detailValue}>{firstName || "Not provided"}</div>
                    </div>
                  </div>

                  <div className={styles.detailCard}>
                    <div className={styles.detailIcon}>👤</div>
                    <div className={styles.detailContent}>
                      <label className={styles.detailLabel}>Last Name</label>
                      <div className={styles.detailValue}>{lastName || "Not provided"}</div>
                    </div>
                  </div>

                  <div className={styles.detailCard}>
                    <div className={styles.detailIcon}>📧</div>
                    <div className={styles.detailContent}>
                      <label className={styles.detailLabel}>Email Address</label>
                      <div className={styles.detailValue}>{email || "Not provided"}</div>
                    </div>
                  </div>

                  <div className={styles.detailCard}>
                    <div className={styles.detailIcon}>📱</div>
                    <div className={styles.detailContent}>
                      <label className={styles.detailLabel}>Mobile Number</label>
                      <div className={styles.detailValue}>{mobile && mobile !== 0 ? mobile : "Not provided"}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>



            {/* Peer Registration Card */}
            <div className={styles.peerCard}>
              <div className={styles.peerHeader}>
                <div className={styles.peerIcon}>🤝</div>
                <div className={styles.peerInfo}>
                  <h2 className={styles.peerTitle}>Peer Registration</h2>
                  <p className={styles.peerDescription}>
                    Connect your messaging accounts for peer-to-peer trading
                  </p>
                </div>
              </div>

              <div className={styles.peerContent}>
                <div className={styles.peerGrid}>
                  <div className={styles.peerField}>
                    <div className={styles.fieldIcon}>📱</div>
                    <div className={styles.fieldContent}>
                      <label className={styles.fieldLabel}>Telegram ID</label>
                      <input
                        type="text"
                        className={styles.fieldInput}
                        placeholder="Enter your Telegram ID"
                        value={telegramId}
                        maxLength={260}
                        onChange={handleTelegramId}
                      />
                    </div>
                  </div>

                  <div className={styles.peerField}>
                    <div className={styles.fieldIcon}>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488" fill="#25D366"/>
                      </svg>
                    </div>
                    <div className={styles.fieldContent}>
                      <label className={styles.fieldLabel}>WhatsApp ID</label>
                      <div className={styles.whatsappContainer}>
                        {whatsappId && whatsappId.length < 1 ? (
                          <select
                            className={styles.countrySelect}
                            name="country"
                            value={countryPhone}
                            onChange={(e) => setCountryPhone(e.target.value)}
                            id="country"
                            required
                          >
                            <option value="-1">Select Country</option>
                            {phoneCountryArray.map((el, index) => (
                              <option key={index} value={el.phone_code}>
                                {el.country_name}
                              </option>
                            ))}
                          </select>
                        ) : null}
                        <input
                          type="text"
                          className={styles.fieldInput}
                          placeholder="Enter your WhatsApp ID"
                          value={whatsappId}
                          maxLength={260}
                          onChange={handleWhatsappId}
                        />
                      </div>
                    </div>
                  </div>

                  <div className={styles.peerField}>
                    <div className={styles.fieldIcon}>
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.099 4.19 2.815 5.498L2.815 17.812l2.784-1.784c.891.392 1.885.608 2.932.608.108 0 .216-.003.323-.009-.068-.272-.105-.556-.105-.849 0-3.827 3.736-6.93 8.346-6.93.108 0 .214.003.321.008C16.98 4.188 13.251 2.188 8.691 2.188z" fill="#1AAD19"/>
                        <path d="M6.785 5.983c.24 0 .434.194.434v1.739h1.739c.24 0 .434.194.434.434s-.194.434-.434.434H7.219v1.739c0 .24-.194.434-.434.434s-.434-.194-.434-.434V8.024H4.612c-.24 0-.434-.194-.434-.434s.194-.434.434-.434h1.739V5.417c0-.24.194-.434.434-.566z" fill="#FFF"/>
                        <path d="M23.096 14.943c0-3.827-3.736-6.93-8.347-6.93s-8.346 3.103-8.346 6.93c0 3.827 3.735 6.93 8.346 6.93.891 0 1.747-.108 2.532-.302l2.532 1.302-1.302-2.532c1.519-1.194 2.585-2.809 2.585-4.698z" fill="#1AAD19"/>
                        <path d="M18.405 12.781c.24 0 .434.194.434s-.194.434-.434.434h-3.477c-.24 0-.434-.194-.434-.434s.194-.434.434-.434h3.477z" fill="#FFF"/>
                        <path d="M11.928 12.781c.24 0 .434.194.434s-.194.434-.434.434H8.451c-.24 0-.434-.194-.434-.434s.194-.434.434-.434h3.477z" fill="#FFF"/>
                      </svg>
                    </div>
                    <div className={styles.fieldContent}>
                      <label className={styles.fieldLabel}>WeChat ID</label>
                      <input
                        type="text"
                        className={styles.fieldInput}
                        placeholder="Enter your WeChat ID"
                        value={weChatId}
                        maxLength={260}
                        onChange={handleWeChatId}
                      />
                    </div>
                  </div>

                  <div className={styles.peerField}>
                    <div className={styles.fieldIcon}>💭</div>
                    <div className={styles.fieldContent}>
                      <label className={styles.fieldLabel}>Other Messaging App ID</label>
                      <input
                        type="text"
                        className={styles.fieldInput}
                        placeholder="Enter other messaging app ID"
                        value={otherMesesgingId}
                        maxLength={260}
                        onChange={handleOtherMesesgingId}
                      />
                    </div>
                  </div>
                </div>

                <div className={styles.peerActions}>
                  <button
                    className={styles.submitButton}
                    onClick={handleVerification}
                  >
                    <span className={styles.buttonIcon}>🚀</span>
                    Submit Registration
                  </button>
                </div>
              </div>
            </div>
            {/* Status Upgrade Section */}
            {verificationStatus === "Dash_Board" && (
              <div className={styles.statusSection}>
                {/* Silver Status Card */}
                <div className={styles.statusCard}>
                  <div className={styles.statusHeader}>
                    <div className={styles.statusIcon}>🥈</div>
                    <div className={styles.statusInfo}>
                      <h2 className={styles.statusTitle}>Achieve Silver Status</h2>
                      <p className={styles.statusDescription}>
                        Upload required documents to unlock silver tier benefits
                      </p>
                    </div>
                  </div>

                  <div className={styles.statusContent}>
                    <div className={styles.documentsGrid}>
                      {/* Proof of Address */}
                      <div className={styles.documentCard}>
                        <div className={styles.documentHeader}>
                          <div className={styles.documentIcon}>🏠</div>
                          <div className={styles.documentInfo}>
                            <h3 className={styles.documentTitle}>Proof of Address</h3>
                            <p className={styles.documentDescription}>
                              Upload a recent utility bill, bank statement, or government letter (within 3 months)
                            </p>
                          </div>
                        </div>
                        <div className={styles.uploadContainer}>
                          <label htmlFor="POF" className={styles.uploadLabel}>
                            <div className={styles.uploadContent}>
                              <Image
                                className={styles.uploadIcon}
                                src={uploadMark ? tick : upload}
                                width={24}
                                height={24}
                                alt="Upload"
                              />
                              <span className={styles.uploadText}>
                                {proofOfAdderss
                                  ? "Document uploaded - waiting for review"
                                  : "Click to upload document"}
                              </span>
                            </div>
                          </label>
                          <input
                            className={styles.fileInput}
                            type="file"
                            id="POF"
                            onChange={handleProofOfAddress}
                            accept=".pdf,.jpg,.jpeg,.png"
                          />
                        </div>
                      </div>

                      {/* Source of Funds */}
                      <div className={styles.documentCard}>
                        <div className={styles.documentHeader}>
                          <div className={styles.documentIcon}>💰</div>
                          <div className={styles.documentInfo}>
                            <h3 className={styles.documentTitle}>Source of Funds</h3>
                            <p className={styles.documentDescription}>
                              Upload proof showing how your trading funds were earned (salary slip, invoice, etc.)
                            </p>
                          </div>
                        </div>
                        <div className={styles.uploadContainer}>
                          <label htmlFor="sof" className={styles.uploadLabel}>
                            <div className={styles.uploadContent}>
                              <Image
                                className={styles.uploadIcon}
                                src={uploadMark1 ? tick : upload}
                                width={24}
                                height={24}
                                alt="Upload"
                              />
                              <span className={styles.uploadText}>
                                {sourceOfFunds
                                  ? "Document uploaded - waiting for review"
                                  : "Click to upload document"}
                              </span>
                            </div>
                          </label>
                          <input
                            className={styles.fileInput}
                            type="file"
                            id="sof"
                            onChange={handleSourceOfFunds}
                            accept=".pdf,.jpg,.jpeg,.png"
                          />
                        </div>
                      </div>

                      {/* Bank Statement */}
                      <div className={styles.documentCard}>
                        <div className={styles.documentHeader}>
                          <div className={styles.documentIcon}>🏦</div>
                          <div className={styles.documentInfo}>
                            <h3 className={styles.documentTitle}>Bank Statement</h3>
                            <p className={styles.documentDescription}>
                              Upload a recent bank statement showing your name, address, and account details
                            </p>
                          </div>
                        </div>
                        <div className={styles.uploadContainer}>
                          <label htmlFor="bank_statement" className={styles.uploadLabel}>
                            <div className={styles.uploadContent}>
                              <Image
                                className={styles.uploadIcon}
                                src={uploadMark2 ? tick : upload}
                                width={24}
                                height={24}
                                alt="Upload"
                              />
                              <span className={styles.uploadText}>
                                {bankStatement
                                  ? "Document uploaded - waiting for review"
                                  : "Click to upload document"}
                              </span>
                            </div>
                          </label>
                          <input
                            className={styles.fileInput}
                            type="file"
                            id="bank_statement"
                            onChange={handleBankStatement}
                            accept=".pdf,.jpg,.jpeg,.png"
                          />
                        </div>
                      </div>
                    </div>

                    <div className={styles.statusActions}>
                      <button
                        className={styles.submitDocumentsButton}
                        onClick={handleUserStatusDocsSubmit}
                      >
                        <span className={styles.buttonIcon}>📄</span>
                        Submit Documents
                      </button>
                    </div>
                  </div>
                </div>

                {/* Gold Status Card */}
                <div className={styles.goldCard}>
                  <div className={styles.goldHeader}>
                    <div className={styles.goldIcon}>🥇</div>
                    <div className={styles.goldInfo}>
                      <h2 className={styles.goldTitle}>Achieve Gold Status</h2>
                      <p className={styles.goldDescription}>
                        Complete these requirements to unlock premium benefits
                      </p>
                    </div>
                  </div>

                  <div className={styles.goldContent}>
                    <div className={styles.requirementsList}>
                      <div className={styles.requirement}>
                        <div className={styles.requirementIcon}>🎯</div>
                        <div className={styles.requirementText}>
                          Complete 100 successful trades with Remflow
                        </div>
                      </div>
                      <div className={styles.requirement}>
                        <div className={styles.requirementIcon}>🔒</div>
                        <div className={styles.requirementText}>
                          Maintain escrow funds with Remflow
                        </div>
                      </div>
                      <div className={styles.requirement}>
                        <div className={styles.requirementIcon}>⭐</div>
                        <div className={styles.requirementText}>
                          Maintain 90%+ rating on all transactions
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

        </Layout>
        <ToastContainer />
      </div>
      {/* ) : (
        <Login />
      )} */}
    </>
  );
};

export default page;
