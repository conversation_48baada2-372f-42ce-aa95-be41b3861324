"use client";
import React, { useState, useEffect } from "react";
import { View, Text } from "react-native";
import * as SecureStore from "expo-secure-store";
import {
  Chat,
  Channel,
  MessageList,
  MessageInput,
  useCreateChatClient,
  OverlayProvider,
} from "stream-chat-expo";
import axios from "axios";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import ErrorBoundary from "./ErrorBoundary";

const chatApiKey = "yj2u7nnanuvb";

const ChatComponent = ({ userData }) => {
  console.log("4th got userData in CHATCOMPONENT:", userData);
  const [channel, setChannel] = useState(null);
  const chatClient = useCreateChatClient({
    apiKey: chatApiKey,
    userData: {
      id: userData.chatUserId,
      name: userData.chatUserName,
    },
    tokenOrProvider: userData.chatToken,
  });

  console.log("5th chatClient:", chatClient);

  useEffect(() => {
    if (chatClient && userData.channel_id && chatClient.userID) {
      console.log("6th Creating channel...");
      try {
        const newChannel = chatClient.channel(
          "messaging",
          userData.channel_id,
          {
            name: `Chat with ${userData.chatUserName}`,
          }
        );
        console.log("7th Channel created:", newChannel);
        newChannel.watch().then(() => {
          console.log("8th Channel watched successfully");
          setChannel(newChannel);
        });
      } catch (error) {
        console.error("Error creating channel:", error);
      }
    }
  }, [chatClient, userData.channel_id]);

  if (!channel) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg">Loading Chat...</Text>
      </View>
    );
  }

  return (
    <OverlayProvider>
      <Chat client={chatClient}>
        <Channel channel={channel}>
          <View className="flex-1">
            <View className="bg-gray-100 p-4 border-b border-gray-200">
              <Text className="text-xl font-bold">Peer to Peer Chat</Text>
              <Text className="text-gray-600">
                Welcome, {userData.chatUserName}
              </Text>
            </View>
            <MessageList />
            <MessageInput />
          </View>
        </Channel>
      </Chat>
    </OverlayProvider>
  );
};

export default function ChatScreen({ orderNumber }) {
  console.log("1st orderNumber:", orderNumber);
  const [userData, setUserData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const getValueFor = async (key) => {
    try {
      return await SecureStore.getItemAsync(key);
    } catch (error) {
      console.error(`Error retrieving ${key}:`, error);
      return null;
    }
  };

  const registerUser = async (email, orderNumber) => {
    try {
      const BaseURL = process.env.EXPO_PUBLIC_Base_URL;
      const response = await axios.post(`${BaseURL}/chat-info/`, {
        order_number: orderNumber,
        email: email,
      });

      if (response.status === 200) {
        const { user_id, channel_id, chat_token, peername } =
          response.data.data;
        console.log("3rd got response from R", response.data.data);
        setUserData({
          chatUserId: user_id,
          chatUserName: peername,
          chatToken: chat_token,
          channel_id: channel_id,
        });
        setError(null);
      } else {
        console.error("Failed to initialize chat:", response.data);
        setError("Failed to initialize chat");
      }
    } catch (error) {
      console.error("Chat initialization error:", error);
      setError("Failed to connect to chat service");
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    const initializeChat = async () => {
      try {
        const userEmail = await getValueFor("userEmail");
        console.log("2nd got userEmail:", userEmail);
        if (!userEmail || !orderNumber) {
          throw new Error("Missing required data");
        }
        await registerUser(userEmail, orderNumber);
      } catch (error) {
        console.error("Initialization error:", error);
        setError("Missing user information");
        setLoading(false);
      }
    };

    initializeChat();
  }, [orderNumber]);

  if (error) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg text-red-500">{error}</Text>
      </View>
    );
  }

  if (loading || !userData) {
    return (
      <View className="flex-1 justify-center items-center">
        <Text className="text-lg">Loading Chat...</Text>
      </View>
    );
  }

  return (
    <GestureHandlerRootView className="flex-1">
      <ErrorBoundary>
        <ChatComponent userData={userData} />
      </ErrorBoundary>
    </GestureHandlerRootView>
  );
}
