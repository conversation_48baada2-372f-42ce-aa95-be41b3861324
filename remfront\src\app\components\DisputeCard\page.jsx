"use client"
import React, { useState } from "react";
import Image from "next/image";
import styles from "./disputeCard.module.css";

const DisputeCard = ({ title, orderNo, evidence, comment, createdDate, index }) => {
  const [imageError, setImageError] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showEvidenceModal, setShowEvidenceModal] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const toggleEvidenceModal = () => {
    setShowEvidenceModal(!showEvidenceModal);
  };

  const truncateText = (text, maxLength = 80) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const getStatusColor = (index) => {
    const colors = ['warning', 'success', 'error', 'info'];
    return colors[index % colors.length];
  };

  const getStatusText = (index) => {
    const statuses = ['Under Review', 'Resolved', 'Escalated', 'Pending'];
    return statuses[index % statuses.length];
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Date not available';

    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffInMs = now - date;
      const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
      const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

      // If less than 1 minute ago
      if (diffInMinutes < 1) {
        return 'Just now';
      }
      // If less than 1 hour ago
      else if (diffInMinutes < 60) {
        return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
      }
      // If less than 24 hours ago
      else if (diffInHours < 24) {
        return `${diffInHours} hour${diffInHours !== 1 ? 's' : ''} ago`;
      }
      // If less than 7 days ago
      else if (diffInDays < 7) {
        return `${diffInDays} day${diffInDays !== 1 ? 's' : ''} ago`;
      }
      // Otherwise show formatted date
      else {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        });
      }
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getFullFormattedDate = (dateString) => {
    if (!dateString) return 'Date not available';

    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      return 'Invalid date';
    }
  };

  return (
    <>
      <tr className={`${styles.tableRow} ${isExpanded ? styles.expandedRow : ''}`}>
        {/* Dispute Type Column */}
        <td className={styles.tableCell}>
          <div className={styles.disputeTypeCell}>
            <div className={`${styles.statusIndicator} ${styles[getStatusColor(index)]}`}></div>
            <div className={styles.disputeInfo}>
              <div className={styles.disputeTitle}>{title || 'Untitled Dispute'}</div>
              <div className={styles.disputeStatus}>{getStatusText(index)}</div>
            </div>
          </div>
        </td>

        {/* Order Number Column */}
        <td className={styles.tableCell}>
          <div className={styles.orderNumberCell}>
            <span className={styles.orderNumber}>#{orderNo || 'N/A'}</span>
          </div>
        </td>

        {/* Evidence Column */}
        <td className={styles.tableCell}>
          <div className={styles.evidenceCell}>
            {evidence ? (
              <button
                className={styles.evidenceBtn}
                onClick={toggleEvidenceModal}
                type="button"
              >
                <svg className={styles.evidenceIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                </svg>
                <span>View Evidence</span>
              </button>
            ) : (
              <div className={styles.noEvidence}>
                <svg className={styles.noEvidenceIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728" />
                </svg>
                <span>No Evidence</span>
              </div>
            )}
          </div>
        </td>

        {/* Comment Column */}
        <td className={styles.tableCell}>
          <div className={styles.commentCell}>
            {comment ? (
              <div className={styles.commentContent}>
                <p className={styles.commentText}>
                  {isExpanded ? comment : truncateText(comment)}
                </p>
                {comment.length > 80 && (
                  <button
                    className={styles.expandCommentBtn}
                    onClick={toggleExpanded}
                    type="button"
                  >
                    {isExpanded ? 'Show less' : 'Read more'}
                  </button>
                )}
              </div>
            ) : (
              <div className={styles.noComment}>
                <span>No comment provided</span>
              </div>
            )}
          </div>
        </td>

        {/* Created Date Column */}
        <td className={styles.tableCell}>
          <div className={styles.dateCell}>
            <div className={styles.dateInfo}>
              <span className={styles.dateText} title={getFullFormattedDate(createdDate)}>
                {createdDate ? new Date(createdDate).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric'
                }) : 'Date not available'}
              </span>
              <div className={styles.dateMeta}>
                <svg className={styles.dateIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V7a2 2 0 012-2h2a2 2 0 012 2v0M8 7v8a2 2 0 002 2h4a2 2 0 002-2V7M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V9a2 2 0 00-2-2h-2" />
                </svg>
                <span className={styles.timeDisplay}>
                  {createdDate ? new Date(createdDate).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: true
                  }) : 'N/A'}
                </span>
              </div>
            </div>
          </div>
        </td>
      </tr>

      {/* Evidence Modal */}
      {showEvidenceModal && evidence && (
        <div className={styles.modalOverlay} onClick={toggleEvidenceModal}>
          <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
            <div className={styles.modalHeader}>
              <h3 className={styles.modalTitle}>Dispute Evidence</h3>
              <button
                className={styles.modalCloseBtn}
                onClick={toggleEvidenceModal}
                type="button"
                aria-label="Close modal"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div className={styles.modalBody}>
              {!imageError ? (
                <Image
                  src={evidence}
                  alt="Dispute evidence"
                  width={600}
                  height={400}
                  className={styles.modalImage}
                  onError={handleImageError}
                  style={{ objectFit: 'contain' }}
                />
              ) : (
                <div className={styles.modalImageError}>
                  <svg className={styles.errorIcon} viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                  <span>Unable to load evidence image</span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default DisputeCard;
