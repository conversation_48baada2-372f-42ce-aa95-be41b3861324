import { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Modal,
  Pressable,
} from "react-native";

import { MaterialIcons } from "@expo/vector-icons";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import { useWebsocketContext } from "@/app/context/AuthContext";
import { toastConfig, showToastError, showToastSuccess } from "@/hooks/toast";
import Toast from "react-native-toast-message";
import { Link, useRouter } from "expo-router";

// Define the type for recipient account data
// Interface for key-value pair in 'data' array
interface RecipientData {
  key: string;
  value: string;
}

// Main interface for the recipient data
interface RecipientAccount {
  recipient_id__id: number;
  recipient_id__firstname: string;
  recipient_id__lastname: string;
  recipient_id__surname: string | null;
  recipient_id__type: string;
  recipient_id__user_id: number;
  recipient_id__is_deleted: boolean;
  recipient_id__is_active: boolean;
  recipient_id__created_date: string; // ISO date string
  recipient_id__updated_date: string; // ISO date string
  recipient_id__email: string;
  recipient_id__ubo_shareholder_name: string | null;
  recipient_id__ubo_shareholder_date_of_incorporation: string | null;
  recipient_id__dob: string; // Date of birth as ISO string
  recipient_id__currency_accepted_id__currency_name: string | null;
  recipient_id__currency_payout_id__currency_name: string;
  recipient_id__payin_option_id__payment_method: string | null;
  recipient_id__payout_option_id__payment_method: string;
  recipient_id__country__country_name: string;
  data: RecipientData[];
}

interface RecipientModalProps {
  isVisible: boolean;
  onClose: () => void;
  payOutOption: string;
  payOutCurrency: string;
  enteredAmount: number;
  listingId: number;
}

export default function SearchRecipientModal({
  isVisible,
  onClose,
  payOutOption,
  enteredAmount,
  payOutCurrency,
  listingId,
}: RecipientModalProps) {
  const router = useRouter();
  const [recipientAcc, setRecipientAcc] = useState<RecipientAccount[]>([]);
  const [selectedCardId, setSelectedCardId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [orderId, setOrderId] = useState("");
  const singleRef = useRef(false);

  // websocket
  const { connection, messageHistory, sendMessage, recentMessage }: any =
    useWebsocketContext();

  // console.log("socketConnectionL1", JSON.parse(recentMessage?.data));

  useEffect(() => {
    if (connection && recentMessage?.data) {
      const dataValue = JSON.parse(recentMessage?.data);
      console.log("socketConnectionLFROM", dataValue);
      if (dataValue?.error) {
        showToastError(dataValue?.error);
      }
      if (dataValue.error === "Trade request already sent") {
        setOrderId(dataValue.data?.order_id);
        if (!singleRef.current) {
          showToastError("Trade request already sent");
        }
        singleRef.current = true;
      } else if (dataValue?.data?.order_id) {
        // Note the use of dataValue.data?.order_id for consistency
        setOrderId(dataValue?.data?.order_id);
        showToastSuccess(dataValue?.data?.message);
      }
    }
  }, [connection, recentMessage?.data]); // Removed orderId from dependencies

  const handleSendWebsocketMsg = (selectedCardId: any) => {
    const payload = {
      action: "send_request",
      listing_id: Number(listingId),
      trade_amount: Number(enteredAmount),
      recipient_id: Number(selectedCardId),
    };
    sendMessage(JSON.stringify(payload));
  };

  const handleCreateTrade = async () => {
    if (!selectedCardId) {
      showToastError("Select Your Preferred Recipient Account To Trade");
      return;
    }
    try {
      handleSendWebsocketMsg(selectedCardId);
    } catch (error) {
      console.log(error);
    }
  };

  // websocket

  const fetchRecipientAccounts = async () => {
    try {
      setLoading(true);
      const res = await customFetchWithToken.get(
        `/get-recipient-account/?payment_option=${payOutOption}`
      );

      setRecipientAcc(res.data.data.data);
      setLoading(false);
    } catch (error) {
      console.error(error);
    }
  };

  console.log("orderId 2nd exch modal", orderId);
  console.log("VISIBLE 2nd exch modal", isVisible);

  // useEffect(() => {
  //   if (orderId) {
  //     console.log("orderId Navigating with :", orderId);
  //     router.push(`/trade/${orderId}?type=user`);
  //   }
  // }, [orderId]);

  useEffect(() => {
    if (isVisible) fetchRecipientAccounts();
  }, [isVisible]);

  // const handleCreateTrade = () => {
  //   if (!selectedCardId) {
  //     Toast.show({
  //       type: "error",
  //       text1: "Select an account to proceed",
  //     });
  //     return;
  //   }
  //   Toast.show({
  //     type: "success",
  //     text1: "Trade Created Successfully",
  //   });
  //   onClose();
  // };

  return (
    <Modal visible={isVisible} transparent={false} animationType="slide">
      <View className="bg-white rounded-lg p-6">
        <View className="flex-row justify-between items-center mb-4">
          <Text className="text-xl font-bold">Create Trade</Text>

          <TouchableOpacity onPress={onClose}>
            <MaterialIcons name="cancel" size={30} color="black" />
          </TouchableOpacity>
        </View>
        <View className="flex-row justify-between items-center mb-4">
          <Text className="text-lg font-bold">
            {recipientAcc.length > 0
              ? `Recipient accounts to receive ${payOutCurrency}`
              : ""}
          </Text>
        </View>

        <ScrollView className="mb-4">
          {loading ? (
            <Text className="text-center text-gray-500">Loading...</Text>
          ) : recipientAcc.length ? (
            recipientAcc.map((acc, index) => (
              <TouchableOpacity
                key={index}
                onPress={() => setSelectedCardId(acc.recipient_id__id)}
                className={`p-4 mb-2 rounded-lg ${
                  selectedCardId === acc.recipient_id__id
                    ? "bg-blue-200 border-blue-600"
                    : "bg-gray-100"
                }`}
              >
                <Text className="font-pbold text-lg">
                  {acc.recipient_id__firstname}
                </Text>
                <Text className="text-gray-600 text-lg font-pmedium">
                  {acc.recipient_id__payout_option_id__payment_method}
                </Text>
                <Text className="text-gray-600 text-lg font-pmedium">
                  {acc.data[0].value}
                </Text>
              </TouchableOpacity>
            ))
          ) : (
            <View className="flex">
              <Text className="text-center text-gray-500 text-lg my-2">
                No recipient accounts found
              </Text>
              <Link
                className="flex  bg-yellow-300 rounded-md py-4"
                href={"/accounts"}
              >
                <Text className="text-center  text-slate-900 font-psemitBold  text-base my-2">
                  Create a recipinet Account to trade
                </Text>
              </Link>
            </View>
          )}
        </ScrollView>

        <TouchableOpacity
          onPress={handleCreateTrade}
          className="bg-green-600 p-4 rounded-lg"
        >
          <Text className="text-center text-white text-md font-pbold">
            Create Trade
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={onClose}
          className="bg-gray-400 p-4 rounded-lg my-3"
        >
          <Text className="text-center text-black text-md font-pbold">
            Close Modal
          </Text>
        </TouchableOpacity>
      </View>
      <Toast config={toastConfig} />
    </Modal>
  );
}
