
"use client";
import { useState, useRef, useEffect } from "react";
import styles from "./webcam.module.css";
import Image from "next/image";

const CustomWebcam = ({ handleKycSelfie }) => {
  const videoRef = useRef(null);
  const [capturedBlob, setCapturedBlob] = useState(null);
  const [showVideo, setShowVideo] = useState(true);
  const [capImg, setCapImg] = useState(null);
  let mediaStream;

  const handleCapture = () => {
    if (!videoRef.current) {
      console.error("Video element not found");
      return;
    }

    let canvas = document.createElement("canvas");
    canvas.width = videoRef.current.videoWidth;
    canvas.height = videoRef.current.videoHeight;
    let context = canvas.getContext("2d");
    context.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);

    let dataURL = canvas.toDataURL("image/png");
    let blobPromise = (async () => {
      return await fetch(dataURL).then((res) => res.blob());
    })();

    blobPromise.then((blob) => {
      let img = document.createElement("img");
      img.src = URL.createObjectURL(blob);
      setCapImg(img);
      setCapturedBlob(blob);

      handleKycSelfie(blob); // Replace this with your actual function
      setShowVideo(false);
    });
  };

  const handleRetake = () => {
    setCapturedBlob(null);
    setCapImg(null);
    setShowVideo(true);
    startCamera();
  };

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        mediaStream = stream;
        setShowVideo(true); // Ensure video is displayed after starting camera
      }
    } catch (error) {
      console.error("Error accessing camera:", error);
    }
  };

  // Open camera on page load
  useEffect(() => {
    navigator.mediaDevices
      .getUserMedia({ video: true })
      .then((stream) => {
        videoRef.current.srcObject = stream;
        mediaStream = stream;
      })
      .catch((error) => {
        console.error("Error accessing the camera:", error);
      });

    // Stop camera access when the component unmounts
    return () => {
      if (mediaStream) {
        mediaStream.getTracks().forEach((track) => track.stop());
      }
    };
  }, []);

  return (
    <div className={styles.container}>
      {!capImg ? (
        <video
          ref={videoRef}
          width={340}
          height={280}
          autoPlay
          style={{ display: showVideo ? "block" : "none" }}
        ></video>
      ) : (
        <div>
          <Image src={capImg.src} width={320} height={280} />
        </div>
      )}
      <div className={styles.btnContainer}>
        <>
          {!capturedBlob ? (
            <button className={styles.btn} onClick={handleCapture}>
              Capture Image
            </button>
          ) : (
            <button className={styles.btn} onClick={handleRetake}>
              Retake
            </button>
          )}
        </>
      </div>
    </div>
  );
};

export default CustomWebcam;
