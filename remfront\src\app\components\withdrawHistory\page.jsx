"use client";
import { useState } from "react";
import styles from "./withdrawHistory.module.css";
import ModifyModal from "../ModifyWithdrawModal/page";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const page = ({ id, amount, address, status, setEditSuccess }) => {
  const [showModal, setShowModal] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Get status color and icon
  const getStatusInfo = (status) => {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
        return { color: 'success', icon: '✅', label: 'Completed' };
      case 'pending':
      case 'processing':
        return { color: 'pending', icon: '⏳', label: 'Pending' };
      case 'failed':
      case 'rejected':
        return { color: 'failed', icon: '❌', label: 'Failed' };
      default:
        return { color: 'pending', icon: '⏳', label: status || 'Unknown' };
    }
  };

  const statusInfo = getStatusInfo(status);

  const handleDeleteWithdrawlReq = async () => {
    if (!window.confirm('Are you sure you want to delete this withdrawal request?')) {
      return;
    }

    setIsDeleting(true);
    try {
      const res = await customFetchWithToken.delete(
        `/delete-withdraw-request/${id}`
      );

      setEditSuccess(true);
      toast.success(res.data.message);
    } catch (error) {
      console.log(error);
      toast.error(error.response?.data?.message || 'Failed to delete withdrawal request');
    } finally {
      setIsDeleting(false);
    }
  };

  const copyToClipboard = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Address copied to clipboard!');
    } catch (err) {
      toast.error('Failed to copy address');
    }
  };

  return (
    <div className={styles.withdrawHistoryCard}>
      <ModifyModal
        id={id}
        showModal={showModal}
        setShowModal={setShowModal}
        amount={amount}
        address={address}
        setEditSuccess={setEditSuccess}
      />

      {/* Card Header */}
      <div className={styles.cardHeader}>
        <div className={styles.transactionId}>
          <span className={styles.idLabel}>Transaction #{id}</span>
          <div className={styles.actionButtons}>
            <button
              className={styles.editButton}
              onClick={() => setShowModal(!showModal)}
              aria-label="Edit withdrawal request"
              title="Edit Request"
            >
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
              </svg>
            </button>
            <button
              className={styles.deleteButton}
              onClick={handleDeleteWithdrawlReq}
              disabled={isDeleting}
              aria-label="Delete withdrawal request"
              title="Delete Request"
            >
              {isDeleting ? (
                <div className={styles.spinner}></div>
              ) : (
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="3,6 5,6 21,6"/>
                  <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"/>
                  <line x1="10" y1="11" x2="10" y2="17"/>
                  <line x1="14" y1="11" x2="14" y2="17"/>
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Card Body */}
      <div className={styles.cardBody}>
        <div className={styles.infoGrid}>
          {/* Amount */}
          <div className={styles.infoItem}>
            <div className={styles.infoLabel}>Amount</div>
            <div className={styles.infoValue}>
              <span className={styles.amountValue}>{amount}</span>
              <span className={styles.currency}>USDT</span>
            </div>
          </div>

          {/* Status */}
          <div className={styles.infoItem}>
            <div className={styles.infoLabel}>Status</div>
            <div className={styles.infoValue}>
              <span className={`${styles.statusBadge} ${styles['status' + statusInfo.color.charAt(0).toUpperCase() + statusInfo.color.slice(1)]}`}>
                <span className={styles.statusIcon}>{statusInfo.icon}</span>
                {statusInfo.label}
              </span>
            </div>
          </div>

          {/* Wallet Address */}
          <div className={styles.infoItem}>
            <div className={styles.infoLabel}>Wallet Address</div>
            <div className={styles.infoValue}>
              <div className={styles.addressContainer}>
                <span className={styles.addressText}>
                  {address.slice(0, 8)}...{address.slice(-8)}
                </span>
                <button
                  className={styles.copyButton}
                  onClick={() => copyToClipboard(address)}
                  aria-label="Copy wallet address"
                  title="Copy Address"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                    <path d="m5,15H4a2,2 0 0,1-2-2V4a2,2 0 0,1,2-2H13a2,2 0 0,1,2,2v1"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
