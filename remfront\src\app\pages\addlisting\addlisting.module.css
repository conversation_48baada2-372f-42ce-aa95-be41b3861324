/* Font Import */
/* @font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
} */

/* Main Container */
.main {
  background: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
  min-height: 100vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Hamburger Menu */
.hamMenu {
  display: none;
}

@media screen and (max-width: 576px) {
  .hamMenu {
    display: block;
    width: 100%;
    background-color: #4153ED;
    position: absolute;
    top: 0;
    height: 60px;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 40px;
  }
}

/* Left Container */
.leftContainerWrapper {
  width: 20%;
  position: relative;
}

@media screen and (max-width: 576px) {
  .leftContainerWrapper {
    width: 80%;
    position: absolute;
    background-color: #4153ED;
    height: 100%;
    transition: 0.4s linear;
  }
}

.leftContainerWrapperHide {
  width: 20%;
  position: relative;
}

@media screen and (max-width: 576px) {
  .leftContainerWrapperHide {
    width: 80%;
    position: absolute;
    background-color: #4153ED;
    height: 100%;
    transform: translateX(-350px);
    transition: 0.4s linear;
  }
}

/* Wrapper */
.wrapper {
  display: flex;
  width: 100%;
  min-height: 90vh;
  padding: 30px 10px;
  margin: auto;
  justify-content: space-between;
  gap: 20px;
}

@media screen and (max-width: 576px) {
  .wrapper {
    width: 100%;
    min-height: 100vh;
    padding: 10px;
  }
}

.leftContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
}

@media screen and (max-width: 576px) {
  .leftContainer {
    width: 100%;
    background-color: #4153ED;
    height: 100%;
  }
}

/* Logo Area */
.logoArea {
  height: 20%;
  width: 100%;
  background-color: #4153ed;
  border-radius: 15px 15px 15px 0px;
  display: flex;
  flex-direction: column;
}

@media screen and (max-width: 576px) {
  .logoArea {
    display: none;
  }
}

.logo {
  margin-top: 5px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media screen and (max-width: 576px) {
  .logo {
    margin-top: 0px;
  }
}

/* Profile Bar */
.profileBar {
  margin-top: 25px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50%;
}

.profileBarContainer {
  background-color: #4f535a;
  width: 80%;
  height: 35px;
  margin: auto;
  border-radius: 100px;
  background: rgba(255, 255, 255, 0.17);
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.profileImg {
  margin-top: 4px;
  margin-left: 15px;
  margin-right: 5px;
}

.profileName {
  color: #fff;
  font-family: Poppins;
  font-size: 11px;
  font-weight: 500;
}

.profileDropDown {
  margin-left: auto;
  margin-right: 10px;
}

/* Sidebar */
.sidebar {
  width: 100%;
  height: auto;
  border-radius: 15px;
  background: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.pages {
  margin-top: 30px;
  width: 100%;
  height: 100%;
}

.dashboardContainer,
.historyContainer,
.logoutContainer {
  width: 100%;
  height: 47px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #4153ed;
  transition: background-color 0.3s ease;
}

.dashboardContainer {
  border-bottom: 1px solid #ececec;
}

.dashboardContainer:hover,
.historyContainer:hover,
.logoutContainer:hover {
  background-color: #f5f6fa;
}

.sideIcons {
  margin-left: 20px;
  margin-right: 10px;
  color: #4153ed;
}

.dashboard {
  color: #4153ed;
}

/* Right Container */
.rightContainer {
  width: 78%;
  height: auto;
  display: flex;
  min-height: 100vh;
}

@media screen and (max-width: 576px) {
  .rightContainer {
    width: 100%;
  }
}

.rightContainerWrapper {
  width: 100%;
  color: #000;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
}

.rightContainerHeader {
  width: 100%;
  height: 94px;
  color: #000;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
  display: flex;
  align-items: center;
}

@media screen and (max-width: 576px) {
  .rightContainerHeader {
    padding: 15px;
    height: auto;
  }
}

.rightContainerBody {
  padding: 30px;
  border-radius: 20px;
  background: #fff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin: 20px;
}

@media screen and (max-width: 576px) {
  .rightContainerBody {
    padding: 5px;
    margin: 10px;
  }
}

.body {
  height: 100%;
  background-color: #fff;
  width: 100%;
}

/* Form Wrappers */
.firstformWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 30px;
}

@media screen and (max-width: 576px) {
  .firstformWrapper {
    flex-direction: column;
    gap: 15px;
  }
}

/* Trading Limits Section - Modified for single line */
.tradingLimitsWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 30px;
  margin-top: 30px;
}

@media screen and (max-width: 768px) {
  .tradingLimitsWrapper {
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 576px) {
  .tradingLimitsWrapper {
    flex-direction: column;
    gap: 15px;
  }
}

.tradingLimitItem {
  flex: 1;
  min-width: 0;
}

/* FX Rate Section - Separate row */
.fxRateWrapper {
  width: 100%;
  display: flex;
  margin-bottom: 30px;
}

.fxRateItem {
  width: 100%;
  max-width: 300px;
}

/* Second Form Wrapper - For Trading Limits */
.secondformWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 30px;
  margin-top: 30px;
}

@media screen and (max-width: 768px) {
  .secondformWrapper {
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 576px) {
  .secondformWrapper {
    flex-direction: column;
    gap: 15px;
  }
}

.thirdformWrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
}

@media screen and (max-width: 576px) {
  .thirdformWrapper {
    margin-bottom: 20px;
  }
}

/* Form Elements */
.firstName {
  flex: 1;
  min-width: 0;
}

@media screen and (max-width: 576px) {
  .firstName {
    width: 100%;
    margin-bottom: 20px;
  }
}

.sec_firstName {
  flex: 1;
  min-width: 0;
}

@media screen and (max-width: 576px) {
  .sec_firstName {
    width: 100%;
    margin-bottom: 20px;
  }
}

.firstNameLabel {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
  display: block;
}

@media screen and (max-width: 576px) {
  .firstNameLabel {
    margin-bottom: 5px;
    margin-left: 5px;
  }
}

.firstNameInput {
  display: flex;
  width: 100%;
}

@media screen and (max-width: 576px) {
  .firstNameInput {
    padding: 0;
  }
}

.firstNameInput input
 {
  border: 2px solid #e1e8ed;
  background-color: #f9f9f9;
  width: 100%;
  padding: 12px 15px;
  height: 20px;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  outline: none;
}

.firstNameInput select {
  border: 2px solid #e1e8ed;
  background-color: #f9f9f9;
  width: 100%;
  padding: 12px 15px;
  height: 45px;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  outline: none;
  @media screen and (max-width: 576px) {
  .addressName {
width: 89%;
  }
}
}

.firstNameInput input:focus,
.firstNameInput select:focus {
  border-color: #4153ed;
  background-color: #fff;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.firstNameInput input:hover,
.firstNameInput select:hover {
  border-color: #4153ed;
}

/* Address Name Styles */
.addressName {
  width: 100%;
  max-width: 600px;
}

@media screen and (max-width: 576px) {
  .addressName {
    width: 100%;
    max-width: 100%;
    padding: 0 16px;
    margin: 0;
  }
}

.addressNameInput input,
.addressNameInput textarea {
  border: 2px solid #e1e8ed;
  background-color: #f9f9f9;
  width: 100%;
  height: 45px;
  padding: 12px 15px;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  outline: none;
}

.addressNameInput input {
  height: 25px;
}

.addressNameInput input:focus,
.addressNameInput textarea:focus {
  border-color: #4153ed;
  background-color: #fff;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.addressNameInput1 {
  display: flex;
  border: 2px solid #e1e8ed;
  background-color: #f9f9f9;
  width: 100%;
  height: 40px;
  margin: 10px 0;
  border-radius: 8px;
  overflow: hidden;
}

.addressNameInput1 select,
.addressNameSelect {
  border: none;
  background-color: transparent;
  width: 100%;
  height: 100%;
  padding: 0 15px;
  font-size: 14px;
  outline: none;
  cursor: pointer;
}

.addressNameInput1:has(select:focus) {
  border-color: #4153ed;
  background-color: #fff;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.addressNameInput1:has(select:hover) {
  border-color: #4153ed;
}

/* Payment Box */
.fourthformWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40px;
  margin-bottom: 40px;
}

.paymentBoxContainer {
  width: 100%;
  max-width: 800px;
  height: auto;
  border-radius: 15px;
  border: 2px solid #e1e8ed;
  background: #fff;
  margin: auto;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.paymentBox {
  background-color: #fff;
  width: 100%;
  border-radius: 15px;
}

.paymentHeader {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  color: #2c3e50;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  border-bottom: 1px solid #e1e8ed;
}

.paymentDirection {
  padding: 15px;
  color: #6c757d;
  text-align: center;
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payFrom,
.payTo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  width: 50%;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.payFrom {
  border-bottom: 3px solid #4153ed;
  color: #4153ed;
  font-weight: 500;
}

.payTo {
  border-bottom: 3px solid #efefef;
  color: #6c757d;
}

.payTo:hover {
  color: #4153ed;
  border-bottom-color: #4153ed;
}

/* Payment Gateways */
.fifthformWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-x: auto;
  margin-bottom: 20px;
  padding: 10px;
}

.paymentGateways,
.BitcoinpaymentGateways {
  width: 120px;
  height: 120px;
  background-color: #F9F9F9;
  margin: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.paymentGateways:hover,
.BitcoinpaymentGateways:hover {
  border-color: #4153ed;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.2);
}

.BitcoinpaymentGateways {
  flex-direction: column;
}

/* Submit Button */
.listing_BtnCont {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 40px 0;
}

.listing_Btn {
  width: 100%;
  max-width: 365px;
  height: 50px;
  border-radius: 8px;
  border: 2px solid #4153ED;
  background: #4153ED;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-family: Poppins;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.listing_Btn:hover {
  background: #3142d4;
  border-color: #3142d4;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.3);
}

.listing_Btn:active {
  transform: translateY(0);
}

/* Textarea */
.textAreaBox {
  width: 100%;
  font-family: Poppins;
  background-color: #f9f9f9;
  border: 2px solid #e1e8ed;
  padding: 12px 15px;
  border-radius: 8px;
  font-size: 14px;
  resize: vertical;
  min-height: 100px;
  transition: all 0.3s ease;
  outline: none;
  box-sizing: border-box;
}

@media screen and (max-width: 576px) {
  .textAreaBox {
    width: 100%;
    margin: 0;
  }
}

.textAreaBox:focus {
  border-color: #4153ed;
  background-color: #fff;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

/* Accessibility and Focus States - Fixed */
.firstNameInput input:focus-visible,
.firstNameInput select:focus-visible,
.addressNameInput input:focus-visible,
.addressNameInput textarea:focus-visible,
.addressNameInput1 select:focus-visible,
.addressNameSelect:focus-visible,
.listing_Btn:focus-visible,
.textAreaBox:focus-visible {
  outline: 2px solid #4153ed;
  outline-offset: 2px;
}

/* Loading States */
.firstNameInput input:disabled,
.firstNameInput select:disabled,
.addressNameInput input:disabled,
.addressNameInput textarea:disabled,
.addressNameInput1 select:disabled,
.listing_Btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Error States */
.firstNameInput input.error,
.firstNameInput select.error,
.addressNameInput input.error,
.addressNameInput textarea.error,
.textAreaBox.error {
  border-color: #dc3545;
}

.firstNameInput input.error:focus,
.firstNameInput select.error:focus,
.addressNameInput input.error:focus,
.addressNameInput textarea.error:focus,
.textAreaBox.error:focus {
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

/* Success States */
.firstNameInput input.success,
.firstNameInput select.success,
.addressNameInput input.success,
.addressNameInput textarea.success,
.textAreaBox.success {
  border-color: #28a745;
}

.firstNameInput input.success:focus,
.firstNameInput select.success:focus,
.addressNameInput input.success:focus,
.addressNameInput textarea.success:focus,
.textAreaBox.success:focus {
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.rightContainerBody {
  animation: fadeIn 0.3s ease-out;
}

/* Add Listing Header Section */
.addListingHeader {
  margin-bottom: 32px;
  position: relative;
  overflow: hidden;
  display: none;
}

@media (max-width: 576px) {
  .addListingHeader {
    display: block;
    margin-bottom: 20px;
    padding: 0 16px;
    text-align: center;
  }
}

.headerContent {
  margin-bottom: 24px;
  position: relative;
  z-index: 2;
}

.pageTitle {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

@media (max-width: 576px) {
  .pageTitle {
    font-size: 24px;
  }
}

.pageSubtitle {
  font-size: 14px;
  color: #64748B;
  margin: 0;
  font-weight: 400;
  line-height: 1.4;
}

@media (max-width: 576px) {
  .pageSubtitle {
    font-size: 13px;
  }
}

/* Print Styles */
@media print {
  .hamMenu,
  .leftContainerWrapper,
  .leftContainerWrapperHide {
    display: none;
  }

  .rightContainer {
    width: 100%;
  }

  .listing_Btn {
    display: none;
  }
}

/* Toast Customization */
.toastWrapper {
  /* Base styles for the wrapper */
}

.toastWrapper :global(.Toastify__toast-container) {
  width: auto !important;
  min-width: 300px;
  max-width: 400px;
}

.toastWrapper :global(.Toastify__toast) {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  margin-bottom: 8px !important;
}

.toastWrapper :global(.Toastify__toast-body) {
  padding: 0 !important;
  margin: 0 !important;
}

.toastWrapper :global(.Toastify__close-button) {
  opacity: 0.7 !important;
  padding: 0 !important;
  margin-left: 12px !important;
}

.toastWrapper :global(.Toastify__close-button:hover) {
  opacity: 1 !important;
}
