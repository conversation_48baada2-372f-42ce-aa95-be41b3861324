name: Frontend PROD Pipeline

on:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 16

      # Deploy to VPS
      - name: Deploy to VPS
        uses: appleboy/ssh-action@master
        with:
          host: *********
          username: ubuntu
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /home/<USER>/remflow/remfront
            git pull
            npm run build
            pm2 restart remflow-frontend
