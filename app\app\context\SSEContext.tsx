import React, { createContext, useContext, useEffect, useRef } from 'react';
import { useWebsocketContext } from './AuthContext';
import EventSource from 'react-native-sse';
import * as SecureStore from 'expo-secure-store';
import refreshTokenApi from '@/app/api/onboarding/refreshTokenEndpoint';
import { AxiosError } from 'axios';
import { usePathname } from 'expo-router';
import Toast from 'react-native-toast-message';
import { showToastSuccess } from '@/hooks/toast';


interface SSEContextType {
  // Empty interface since we're just handling connections
}

const SSEContext = createContext<SSEContextType | undefined>(undefined);

export const SSEProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { token } = useWebsocketContext();
    const eventSourceRef = useRef<EventSource | null>(null);
    const pathname = usePathname();
    let sseErrorCount = 0;
    let token1: string | null = null;
    
    const getToken = async () => {
      const userToken = SecureStore.getItem("user");
      // setToken(userToken);
      token1 = userToken;
      // console.log("logggggggingggggggggggggggggg", userToken);
    };
  getToken();



  const refreshApiCall = async () => {
    try {
      const res = await refreshTokenApi();
      await SecureStore.setItemAsync("user", res.data.data.access_token);
      token1 = res.data.data.access_token;
      return res;
    } catch (error) {
      console.log("Error refreshing token:", error);
      throw error;
    }
  };

  const setupSSEConnection = () => {
    if (!token1) {
      console.log("No token found");
      return;
    }
    
    // Disconnect on login/register pages
    if (pathname === '/' || pathname === '/register') {
      if (eventSourceRef.current) {
        console.log("Disconnecting SSE on login/register page");
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
      return;
    }

    const url = new URL("https://dev.remflow.net/remflow/notification-sse/");
    
    try {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      const es = new EventSource(url, {
        withCredentials: true,
        headers: {
          Authorization: {
            toString: function () {
              return "Bearer " + token1;
            },
            Accept: "text/event-stream",
            Connection: "keep-alive",
          },
        },
      });

      es.addEventListener("open", () => {
        console.log("SSE connection opened");
        sseErrorCount = 0;
      });

      (es as any).addEventListener("notification", (event: any) => {
        console.log("New SSE message:", JSON.parse(event.data));
        const notifData = JSON.parse(event.data);
        // showToastSuccess(notifData.notifiaction_msg);
        if (notifData) {
          try {
            showToastSuccess(notifData.notifiaction_msg);
          } catch (error) {
            console.error("Error handling SSE message:", error);
            showToastSuccess("New notification received");
          }
        }
      });

      es.addEventListener("error", async () => {
        if (sseErrorCount < 3) {
          console.error("SSE connection error");
          try {
            await refreshApiCall();
            console.log("loggin the resfresh token api call")
            setupSSEConnection(); // Retry connection with new token
          } catch (error) {
            console.error("Failed to refresh token:", error);
          }
          sseErrorCount++;
        }
      });

      eventSourceRef.current = es;

      return () => {
        es.close();
        eventSourceRef.current = null;
      };
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 401) {
        refreshApiCall().catch(console.error);
      }
      console.error("Error setting up SSE:", error);
    }
  };

  useEffect(() => {
    if (token1 && pathname !== '/' && pathname !== '/register') {
      setupSSEConnection();
    } else if (pathname === '/' || pathname === '/register') {
      // Disconnect when navigating to login/register pages
      if (eventSourceRef.current) {
        console.log("Disconnecting SSE due to navigation to login/register page");
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    }

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
  }, [token1]);

  return (
    <SSEContext.Provider value={{}}>
      {children}
    </SSEContext.Provider>
  );
};

export const useSSE = () => {
  const context = useContext(SSEContext);
  if (context === undefined) {
    throw new Error('useSSE must be used within a SSEProvider');
  }
  return context;
}; 