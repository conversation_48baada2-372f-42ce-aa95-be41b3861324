/* Modern Trading Table */
.tableWrapper {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 24px 0;
  width: 100%;
}

.tableContainer {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  min-width: 0;
}

.table {
  width: 100%;
  min-width: 750px;
  border-collapse: collapse;
  table-layout: fixed;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Table Header */
.tableHead {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 35%, #581c87 70%, #0f172a 100%);
  position: relative;
  overflow: hidden;
}



.tableHeader {
  padding: 20px 12px;
  text-align: left;
  background: transparent;
  position: sticky;
  top: 0;
  
  white-space: nowrap;
  border: none;
  transition: all 0.3s ease;
  position: relative;
  box-sizing: border-box;
}

/* Center align the last header (Action column) */
.tableHeader:last-child {
  text-align: center;
}

.tableHeader:last-child .headerContent {
  align-items: center;
}

.tableHeader:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Specific Column Widths - matching table cells exactly */
.tableHeader:nth-child(1) {
  width: 20%;
  border-top-left-radius: 16px;
}

.tableHeader:nth-child(2) {
  width: 15%;
}

.tableHeader:nth-child(3) {
  width: 15%;
}

.tableHeader:nth-child(4) {
  width: 15%;
}

.tableHeader:nth-child(5) {
  width: 20%;
}

.tableHeader:nth-child(6) {
  width: 15%;
  border-top-right-radius: 16px;
}

.headerContent {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
}

.headerContent::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 18px;
  background: linear-gradient(135deg, #60a5fa, #3b82f6);
  border-radius: 2px;
  opacity: 0;
  transition: all 0.3s ease;
}

.tableHeader:hover .headerContent::before {
  opacity: 1;
  left: -10px;
}

.headerLabel {
  font-size: 14px;
  font-weight: 700;
  color: #ffffff;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  margin-bottom: 2px;
  position: relative;
}

.headerSubLabel {
  font-size: 11px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  text-transform: capitalize;
  letter-spacing: 0.3px;
}

/* Table Body */
.tableBody {
  background: white;
}

/* Table Row Styles */
.tableRow {
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.3s ease;
  background: white;
}

.tableRow:hover {
  background: #f8fafc;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.tableRow:last-child {
  border-bottom: none;
}

.tableCell {
  padding: 16px 12px;
  vertical-align: middle;
  border: none;
  position: relative;
  box-sizing: border-box;
}

/* Trader Cell Styles */
.traderCell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.traderInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
  flex: 1;
}

.traderHeader {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
}

.onlineIndicator {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  border: 2px solid #d1fae5;
  flex-shrink: 0;
  position: relative;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

.traderName {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.2;
}

.traderStats {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.completionRate {
  color: #059669;
  font-weight: 600;
  background: #d1fae5;
  padding: 2px 6px;
  border-radius: 4px;
  line-height: 1;
}

.tradeCount {
  color: #64748b;
  font-weight: 500;
}

/* Rate Cell Styles */
.rateCell {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.rateValue {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.rateAmount {
  font-size: 16px;
  font-weight: 700;
  color: #0f172a;
  line-height: 1;
}

.rateCurrency {
  font-size: 14px;
  font-weight: 600;
  color: #475569;
}

.rateSubtext {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

/* Range Cell Styles */
.rangeCell {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.rangeValue {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
  line-height: 1.3;
}

.rangeCurrency {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
}

/* Liquidity Cell Styles */
.liquidityCell {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.liquidityValue {
  font-size: 14px;
  font-weight: 600;
  color: #0f172a;
  line-height: 1.3;
}

.liquidityCurrency {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
}

/* Method Cell Styles */
.methodCell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.paymentMethod {
  display: flex;
  align-items: center;
  gap: 6px;
}

.methodBadge {
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid #93c5fd;
  line-height: 1;
}

.responseTime {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

/* Action Cell Styles */
.actionCell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.buyButton {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
  min-width: 80px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.buyButton:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.buyButton:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* Modal Styles (existing modal styles remain the same) */
.modalCont {
  max-width: 600px;
  width: 90vw;
  background: white;
  border-radius: 12px;
  padding: 0;
  outline: none;
  border: none;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.modalTop {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border-bottom: 1px solid #e2e8f0;
}

.left {
  display: flex;
  flex-direction: column;
}

.modalProfileWrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modalPic {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #e2e8f0;
}

.modalName {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
}

.orders {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.right {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #f1f5f9;
  transition: all 0.2s ease;
}

.right:hover {
  background: #e2e8f0;
  transform: scale(1.05);
}

.modalBottom {
  display: flex;
  gap: 24px;
  padding: 20px;
}

.leftB {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.infoPoints {
  font-size: 13px;
  color: #475569;
  line-height: 1.4;
}

.highlight {
  font-weight: 600;
  color: #1e293b;
  margin-left: 4px;
}

.terms {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tHeader {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.termsPara {
  font-size: 12px;
  color: #64748b;
  line-height: 1.5;
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.rightB {
  width: 280px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.validationCheck {
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
  min-height: 16px;
}

.payInput {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  transition: all 0.2s ease;
}

.payInput:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.payInput input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #1e293b;
}

.payInput input::placeholder {
  color: #9ca3af;
}

.receive {
  flex: 1;
}

.buttonBBUY {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);
}

.buttonBBUY:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

/* Empty State */
.emptyState {
  padding: 60px 40px;
  text-align: center;
  background: linear-gradient(135deg, #f9fafb, #f3f4f6);
}

.emptyContent {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  max-width: 400px;
  margin: 0 auto;
}

.emptyIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #e5e7eb, #d1d5db);
  border-radius: 50%;
  margin-bottom: 8px;
}

.emptyTitle {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: #374151;
}

.emptyDescription {
  margin: 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .table {
    min-width: 680px;
  }

  .tableHeader {
    padding: 18px 10px;
  }

  .tableCell {
    padding: 14px 10px;
  }

  .headerLabel {
    font-size: 13px;
  }

  .headerSubLabel {
    font-size: 10px;
  }

  .rateAmount {
    font-size: 15px;
  }

  .buyButton {
    padding: 7px 16px;
    font-size: 13px;
    min-width: 70px;
  }
}

@media (max-width: 1024px) {
  .tableWrapper {
    margin: 16px 0;
    border-radius: 12px;
  }

  .table {
    min-width: 620px;
  }

  .tableHeader {
    padding: 16px 8px;
  }

  .tableCell {
    padding: 12px 8px;
  }

  .headerLabel {
    font-size: 12px;
  }

  .headerSubLabel {
    font-size: 9px;
  }

  .traderName {
    font-size: 13px;
  }

  .rateAmount {
    font-size: 14px;
  }

  .buyButton {
    padding: 6px 14px;
    font-size: 12px;
    min-width: 60px;
  }
}

@media (max-width: 768px) {
  .tableWrapper {
    background: transparent;
    box-shadow: none;
    border-radius: 0;
    overflow: visible;
  }

  .tableContainer {
    overflow-x: visible;
  }

  .table {
    display: block;
    min-width: auto;
    table-layout: auto;
  }

  .tableHead {
    display: none;
  }

  .tableBody {
    display: block;
    background: transparent;
  }

  .tableRow {
    display: block;
    background: white;
    margin-bottom: 12px;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    padding: 16px;
  }

  .tableRow:hover {
    transform: none;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  .tableCell {
    display: block;
    padding: 8px 0;
    border: none;
  }

  .tableCell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #64748b;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 4px;
  }

  .traderCell {
    flex-direction: row;
    align-items: flex-start;
  }

  .actionCell {
    justify-content: flex-start;
    margin-top: 8px;
  }

  .buyButton {
    width: 100%;
    padding: 12px;
    font-size: 14px;
  }

  .modalBottom {
    flex-direction: column;
    gap: 16px;
  }

  .rightB {
    width: 100%;
  }

  .emptyState {
    padding: 40px 20px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .emptyIcon {
    width: 60px;
    height: 60px;
  }

  .emptyTitle {
    font-size: 18px;
  }

  .emptyDescription {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .tableWrapper {
    margin: 12px 0;
  }

  .tableRow {
    padding: 12px;
    margin-bottom: 8px;
  }

  .emptyState {
    padding: 32px 16px;
  }

  .emptyContent {
    gap: 12px;
  }

  .emptyIcon {
    width: 48px;
    height: 48px;
  }

  .emptyTitle {
    font-size: 16px;
  }

  .emptyDescription {
    font-size: 12px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .tableWrapper {
    background: #ffffff;
  }

  .tableHead {
    background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 35%, #581c87 70%, #0f172a 100%) !important;
  }

  .headerLabel {
    color: #f9fafb;
  }

  .headerSubLabel {
    color: rgba(249, 250, 251, 0.8);
  }

  .tableBody {
    background: #ffffff;
  }

  .tableRow {
    background: #ffffff;
    border-bottom-color: #e5e7eb;
  }

  .tableRow:hover {
    background: #f8fafc;
  }

  .traderName,
  .rateAmount,
  .rangeValue,
  .liquidityValue {
    color: #1f2937;
  }

  .completionRate {
    background: #ecfdf5;
    color: #059669;
  }

  .tradeCount,
  .rateSubtext,
  .rangeCurrency,
  .liquidityCurrency,
  .responseTime {
    color: #6b7280;
  }

  .methodBadge {
    background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
    color: #0369a1;
    border-color: #bae6fd;
  }

  .emptyState {
    background: linear-gradient(135deg, #fefefe, #fcfcfd);
  }

  .emptyTitle {
    color: #1f2937;
  }

  .emptyDescription {
    color: #6b7280;
  }

  .emptyIcon {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .tableWrapper {
    border: 2px solid #000;
  }

  .tableHead {
    background: #000;
  }

  .headerLabel {
    color: #fff;
  }

  .headerSubLabel {
    color: #ccc;
  }

  .emptyIcon {
    background: #000;
  }

  .emptyIcon svg {
    stroke: #fff;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .tableHead::before,
  .tableHeader,
  .headerContent::before,
  .tableRow,
  .buyButton,
  .onlineIndicator {
    animation: none;
    transition: none;
  }

  .tableHeader:hover,
  .tableRow:hover,
  .buyButton:hover {
    transform: none;
  }
} 