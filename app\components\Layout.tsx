import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { <PERSON>, useRouter } from "expo-router";
import customFetch<PERSON>ithToken from "@/app/utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";
import * as SecureStore from "expo-secure-store";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Notifications from "./Notifications";
import { showToastSuccess, showToastError, toastConfig } from "../hooks/toast";

const menuItems = [
  { icon: "grid-outline", label: "Dashboard", route: "/dashboard" },
  { icon: "add-circle-outline", label: "Add Listing", route: "/addlisting" },
  { icon: "search-outline", label: "Search Ads", route: "/searchads" },
  { icon: "cash-outline", label: "Remflow Funds", route: "/remflowfunds" },
  { icon: "hammer-outline", label: "Disputes", route: "/disputes" },
  { icon: "time-outline", label: "History", route: "/history" },
  {
    icon: "bookmark-outline",
    label: "Saved Accounts",
    route: "/accounts",
  },
  { icon: "list-outline", label: "My Listings", route: "/mylisting" },
  { icon: "person-outline", label: "Profile", route: "/profile" },
  { icon: "help-circle-outline", label: "Help", route: "/help" },
  // { icon: "person-outline", label: "Trade", route: "/trade" },
  { icon: "log-out-outline", label: "Logout", route: "" },
];

export default function Layout({ children }: { children: React.ReactNode }) {
  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green", width: "90%" }} // Adjust width to 100%
        contentContainerStyle={{ paddingHorizontal: 5 }}
        text1Style={{
          fontSize: 14,
          fontWeight: "bold",
          marginLeft: 20,
        }}
        text2Style={{
          fontSize: 29,
        }}
      />
    ),
    // You can add similar customizations for 'error' and 'info' types if needed
  };

  const showToast = (message: string) => {
    Toast.show({
      type: "success", // can also be 'error' or 'info'
      text1: message,
      // text2: "This is a toast message 👋",
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isNotiOpen, setIsNotiOpen] = useState(false);
  const [notificationCount, setNotificationCount] = useState(0);
  const router = useRouter();

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);

  const closeNotificationModalFunc = () => {
    setIsNotiOpen(false);
  };

  const clearCacheAndLogout = async () => {
    try {
      // Clear all SecureStore items
      const itemsToDelete = [
        "user",
        "userID",
        "userName",
        "lastname",
        "refreshToken",
        "userEmail",
        "chatToken",
      ];

      for (const item of itemsToDelete) {
        await SecureStore.deleteItemAsync(item);
      }
      console.log("SecureStore cleared.");

      // Clear AsyncStorage
      await AsyncStorage.clear();
      console.log("AsyncStorage cleared.");

      // Remove the router.replace("/") from here as it's already in handleLogout
      console.log("User logged out and cache cleared.");
    } catch (error) {
      console.error("Error clearing cache and logging out:", error);
    }
  };

  const handleLogout = async () => {
    try {
      const res = await customFetchWithToken.post("/destroy-user-token/");
      console.log("res", res.status);
      if (res.status == 200) {
        showToast("User Logged Out Successfully");
        await clearCacheAndLogout();
        setTimeout(() => {
          router.replace("/");
        }, 3000);
      }
    } catch (error) {
      console.error(error);
    }
    // After logout, navigate to login screen
    // router.replace("/login");
  };

  const getAllNotificationsHandler = async () => {
    try {
      const res = await customFetchWithToken("/get-notification/");
      setNotificationCount(res.data.count);
    } catch (error) {
      console.log(error);
    }
  };

  const notificationCountHandler = (data: number) => {
    setNotificationCount(data); // Still valid
  };

  useEffect(() => {
    getAllNotificationsHandler();
  }, []);

  return (
    <SafeAreaView className="flex-1 bg-gray-100">
      <View className="flex-auto">
        {/* Top Bar */}
        <View className="bg-[#4153ed] h-16 flex-row items-center justify-between px-4">
          <TouchableOpacity onPress={toggleMenu}>
            <Ionicons name="menu" size={24} color="white" />
          </TouchableOpacity>
          <Text className="text-white text-lg font-bold">RemFlow</Text>
          <TouchableOpacity onPress={() => setIsNotiOpen(!isNotiOpen)}>
            <View className="w-8 h-8 bg-gray-300 rounded-full items-center justify-center">
              <Ionicons name="notifications" size={20} color="white" />
              <View className="absolute -top-1 -right-1 bg-red-500 rounded-full w-4 h-4 items-center justify-center">
                <Text className="text-white text-xs">{notificationCount}</Text>
              </View>
            </View>
          </TouchableOpacity>
          {isNotiOpen && (
            <Notifications
              isVisible={isNotiOpen}
              onClose={() => setIsNotiOpen(false)}
              handleNotiCount={notificationCountHandler}
            />
          )}
          {/* <TouchableOpacity onPress={() => router.push("/profile")}>
            <View className="w-8 h-8 bg-gray-300 rounded-full items-center justify-center">
              <Ionicons name="person" size={20} color="white" />
            </View>
          </TouchableOpacity> */}
        </View>

        {/* Sliding Menu */}
        {isMenuOpen && (
          <View className="absolute left-0 top-16 bottom-0 w-64 bg-white z-10 shadow-lg">
            <ScrollView className="flex-1">
              {menuItems.map((item, index) => {
                if (item.label === "Logout") {
                  return (
                    <TouchableOpacity
                      key={index}
                      className="flex-row items-center py-4 px-6 border-b border-gray-200"
                      onPress={() => {
                        handleLogout();
                      }}
                    >
                      <Ionicons
                        name={item.icon as any}
                        size={24}
                        color="#4B5563"
                      />
                      <Text className="ml-4 text-gray-800 font-psemitBold">
                        {item.label}
                      </Text>
                    </TouchableOpacity>
                  );
                }

                return (
                  <Link key={index} href={item.route as any} asChild>
                    <TouchableOpacity
                      className="flex-row items-center py-4 px-6 border-b border-gray-200"
                      onPress={() => {
                        setIsMenuOpen(false);
                      }}
                    >
                      <Ionicons
                        name={item.icon as any}
                        size={24}
                        color="#4B5563"
                      />
                      <Text className="ml-4 text-gray-800 font-psemitBold text-md">
                        {item.label}
                      </Text>
                    </TouchableOpacity>
                  </Link>
                );
              })}
            </ScrollView>
          </View>
        )}

        {/* Main Content */}
        <View className="flex-1 bg-white">{children}</View>

        {/* Overlay to close menu when clicked outside */}
        {isMenuOpen && (
          <TouchableOpacity
            className="absolute inset-0 bg-black bg-opacity-50 z-0"
            onPress={() => setIsMenuOpen(false)}
          />
        )}
      </View>
      <Toast config={toastConfig} />
    </SafeAreaView>
  );
}
