import axios from "axios";
import {
  postApi<PERSON><PERSON><PERSON>,
  postApiHandlerWithBody,
} from "@/app/utils/postApiHandler";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
const X_MBX_APIKEY = process.env.NEXT_XMBXAPIKEY;
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const createWalletAddress = async () => {
  const res = await axios({
    url: `${Base_url}/create-wallet/?coin=USDT&network=TRX`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
      "X-MBX-APIKEY": X_MBX_APIKEY,
    },
  });
  return res;
};

export const addExternalWalletAddressApi = (Data) => {
  const res = postApiHandlerWithBody("withdraw-address", "POST", Data);
  return res;
};
