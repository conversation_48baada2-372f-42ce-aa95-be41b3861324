{"extends": "expo/tsconfig.base.json", "compilerOptions": {"types": ["node"], "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-native", "strict": true, "paths": {"@/*": ["./*"], "stream-chat-react-native-core/*": ["./node_modules/stream-chat-react-native-core/src/*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "app/index.tsx", "app/pages/profile.js", "app/utils/axiosInterceptor.js", "components/Chat.js", "components/ErrorBoundary.js", "types/**/*.d.ts", "nativewind-env.d.ts"]}