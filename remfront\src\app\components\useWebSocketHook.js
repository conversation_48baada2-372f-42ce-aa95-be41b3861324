// useWebSocket.js
import { useEffect, useRef, useState } from "react";

const useWebSocket = (url) => {
  const [messages, setMessages] = useState([]);
  const socketRef = useRef(null);

  useEffect(() => {
    // Create a new WebSocket instance
    socketRef.current = new WebSocket(url);

    socketRef.current.onopen = () => {
      console.log("WebSocket1 connected");
    };

    socketRef.current.onmessage = (event) => {
      console.log("WebSocket send message");
      setMessages((prevMessages) => [...prevMessages, event.data]);
    };

    socketRef.current.onclose = () => {
      console.log("WebSocket disconnected");
    };

    // Cleanup function to close the WebSocket connection
    return () => {
      socketRef.current.close();
    };
  }, [url]);

  const sendMessage = (message) => {
    if (socketRef.current.readyState === WebSocket.OPEN) {
      socketRef.current.send(message);
    } else {
      console.log("WebSocket is not open");
    }
  };

  return { messages, sendMessage };
};

export default useWebSocket;
