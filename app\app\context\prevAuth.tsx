import React, {
  createContext,
  useRef,
  useEffect,
  useContext,
  useState,
} from "react";
import useWebSocket, {
  ReadyState,
  Options,
  SendMessage,
} from "react-use-websocket";
import refreshTokenApi from "../api/onboarding/refreshTokenEndpoint";
import { usePathname } from "expo-router";
import * as SecureStore from "expo-secure-store";

interface AppContextType {
  connection: string | null;
  sendMessage: SendMessage;
  messageHistory: unknown[];
  recentMessage: unknown;
  lastJsonMessage: unknown;
}

export const AppContext = createContext<AppContextType | undefined>(undefined);

interface ContextStoreProps {
  children: React.ReactNode;
}

export const ContextStore: React.FC<ContextStoreProps> = ({ children }) => {
  const pathname = usePathname();
  const connection = useRef<string | null>(null);
  const [shouldReconnect, setShouldReconnect] = useState(true);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [recentMessage, setRecentMessage] = useState<unknown>(null);
  const [messageHistory, setMessageHistory] = useState<unknown[]>([]);

  const sessionLogoutHandler = async () => {
    try {
      console.log("Logged out successfully!");
      setTimeout(() => {
        window.location.href = "/sign/login";
      }, 1500);
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const refreshApiCall = async () => {
    try {
      const res = await refreshTokenApi();
      await SecureStore.setItem("user", res.data.data.access_token);
      return res;
    } catch (error) {
      console.error("Refresh token error:", error);
      throw error;
    }
  };

  const shouldConnectWebSocket = pathname !== "/" && pathname !== "/register";

  const options: Options = {
    onOpen: () => {
      console.log("Connected!!");
      setShouldReconnect(true);
    },
    onClose: () => {
      console.log("Disconnected!");
      setShouldReconnect(true);
    },
    // onError: (error) => {
    //   console.error("WebSocket error:", error);
    //   setShouldReconnect(true);
    // },
    onError: (error: any) => {
      console.error("WebSocket error:", error);
      console.error("WebSocket error details:", {
        isTrusted: error.isTrusted,
        message: error.message,
        type: error.type,
        target: error.target,
      });
      setShouldReconnect(true);
    },
    shouldReconnect: () => shouldReconnect,
    onMessage: (message) => {
      setRecentMessage(message);
    },
  };

  const { sendMessage, lastMessage, readyState, lastJsonMessage } =
    useWebSocket(
      shouldConnectWebSocket
        ? `wss://dev.remflow.net/ws/transaction/?token=${SecureStore.getItem(
            "user"
          )}`
        : null,
      options
    );

  useEffect(() => {
    if (lastJsonMessage !== null) {
      setMessageHistory((prev) => [...prev, lastJsonMessage]);
    }
  }, [lastJsonMessage]);

  useEffect(() => {
    if (
      (readyState === ReadyState.CLOSED || readyState === ReadyState.CLOSING) &&
      shouldConnectWebSocket
    ) {
      console.log("Attempting to reconnect...");

      const timeout = Math.min(1000 * 2 ** reconnectAttempts, 30000);
      const timeoutId = setTimeout(() => {
        if (shouldConnectWebSocket) {
          console.log("Reconnecting now...");
          setShouldReconnect(true);
          setReconnectAttempts((prev) => prev + 1);
        }
      }, timeout);

      return () => clearTimeout(timeoutId);
    }
  }, [readyState, reconnectAttempts, shouldConnectWebSocket]);

  useEffect(() => {
    if (readyState === ReadyState.OPEN) {
      setReconnectAttempts(0);
    }
  }, [readyState]);

  connection.current =
    {
      [ReadyState.CONNECTING]: "Connecting",
      [ReadyState.OPEN]: "Open",
      [ReadyState.CLOSING]: "Closing",
      [ReadyState.CLOSED]: "Closed",
      [ReadyState.UNINSTANTIATED]: "Uninstantiated",
    }[readyState] || null;

  return (
    <AppContext.Provider
      value={{
        connection: connection.current,
        sendMessage,
        messageHistory,
        recentMessage,
        lastJsonMessage,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useWebsocketContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useWebsocketContext must be used within a ContextStore");
  }
  return context;
};
