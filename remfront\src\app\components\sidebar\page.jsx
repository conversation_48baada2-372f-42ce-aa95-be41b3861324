"use client";
import { useState } from "react";
import Image from "next/image";
import styles from "./sidebar.module.css";
import Link from "next/link";
import disputes from "../../../../public/assets/sidebar/disputes.png";
import { useRouter, usePathname } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import ConfirmationModal from "../profileConfirmationModal/page";
import SidebarHeader from "../SidebarHeader/page";

const page = ({ token, status, handleRedirect }) => {
const [showLogoutModal, setShowLogoutModal] = useState(false);

  let localToken;
  let verificationStatus;
  const pathname = usePathname();

  if (typeof window !== "undefined") {
    verificationStatus = localStorage.getItem("verificationStatus");
    localToken = sessionStorage.getItem("user");
  }

  const router = useRouter();
  const sessionLogoutHanlder = async () => {
    try {
      toast.success("User Logged Out Successfully!");
      setTimeout(() => {
        if (typeof window !== "undefined") {
          localStorage.clear();
          sessionStorage.clear();
        }
        router.push("/sign/login");
      }, 1000);
    } catch (error) {
      console.error(error);
      toast.error(" Please try again.");
    }
  };
  const logoutHanlder = async () => {
    try {
      const res = await customFetchWithToken.post("/destroy-user-token/");
      toast.success("User Logged Out Successfully");
      if (res.data.status == 200) {
        setTimeout(() => {
          if (typeof window !== "undefined") {
            localStorage.clear();
            sessionStorage.clear();
          }
          router.push("/sign/login");
        }, 1000);
      }
    } catch (error) {
      if (
        (error && error.response.status == 401) ||
        (error && error.response.status == 400)
      ) {
        sessionLogoutHanlder();
      } else {
        console.error(error);
        toast.error("Failed to log out. Please try again.");
      }
    }
  };

  return (
    <>
    {showLogoutModal && (
      <ConfirmationModal
        isOpen={showLogoutModal}
        onClose={() => setShowLogoutModal(false)}
        onConfirm={logoutHanlder}
        title="Confirm Logout"
        message="Are you sure you want to logout?"
        confirmButtonText="Logout"
        cancelButtonText="Cancel"
      />
    )}
      {/* Unified Sidebar Container */}
      <div className={styles.sidebarWrapper}>
        <SidebarHeader
          token={token}
          status={status}
          handleRedirect={handleRedirect}
        />
        <div className={styles.sidebar}>
          <div className={styles.pages}>
            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/dashboard" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={pathname === "/pages/dashboard" ? "3" : "2"}
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"
                  />
                </svg>
              </span>

              <div className={styles.dashboard}>
                {" "}
                <Link
                  className={`${styles.btnEnabled} ${
                    pathname === "/pages/dashboard" ? styles.btnActive : ""
                  }`}
                  href="/pages/dashboard"
                >
                  Dashboard
                </Link>
              </div>
            </div>
            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/addlisting" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={pathname === "/pages/addlisting" ? "3" : "2"}
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 4.5v15m7.5-7.5h-15"
                  />
                </svg>
              </span>

              <div className={styles.dashboard}>
                {" "}
                <Link
                  className={`${
                    verificationStatus !== "Dash_Board"
                      ? styles.disabledForLaunch
                      : `${styles.btnEnabled} ${
                          pathname === "/pages/addlisting" ? styles.btnActive : ""
                        }`
                  }`}
                  href={
                    verificationStatus === "Dash_Board" ? "/pages/addlisting" : {}
                  }
                >
                  Add Listing
                </Link>
                {/* <Link
                  href="/pages/addlisting"
                  disabled={verificationStatus === "Dash_Board"}
                  
                >
                  Add Listing
                </Link> */}
              </div>
            </div>
            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/searchads" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={pathname === "/pages/searchads" ? "3" : "2"}
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"
                  />
                </svg>
              </span>

              <div className={styles.dashboard}>
                {" "}
                <Link
                  className={`${styles.btnEnabled} ${
                    pathname === "/pages/searchads" ? styles.btnActive : ""
                  }`}
                  href="/pages/searchads"
                >
                  Search Ads
                </Link>
              </div>
            </div>

            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/remflowFunds" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={pathname === "/pages/remflowFunds" ? "3" : "2"}
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                  />
                </svg>
              </span>

              <div className={styles.dashboard}>
                {" "}
                <Link
                  className={`${
                    verificationStatus !== "Dash_Board"
                      ? styles.disabledForLaunch
                      : `${styles.btnEnabled} ${
                          pathname === "/pages/remflowFunds"
                            ? styles.btnActive
                            : ""
                        }`
                  }`}
                  href={
                    verificationStatus === "Dash_Board"
                      ? "/pages/remflowFunds"
                      : {}
                  }
                >
                  Remflow Funds
                </Link>
              </div>
            </div>
            <div className={styles.dashboardContainer}>
              <Image
                className={`${styles.sideIcons} ${
                  pathname === "/pages/dispute" ? styles.sideIconsActive : ""
                }`}
                src={disputes}
                alt="disputes icon"
                width={15}
                height={15}
              />
              <div className={styles.dashboard}>
                <Link
                  className={`${
                    verificationStatus !== "Dash_Board"
                      ? styles.disabledForLaunch
                      : `${styles.btnEnabled} ${
                          pathname === "/pages/dispute" ? styles.btnActive : ""
                        }`
                  }`}
                  href={
                    verificationStatus === "Dash_Board" ? "/pages/dispute" : {}
                  }
                >
                  Disputes
                </Link>
              </div>
            </div>

            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/history" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={pathname === "/pages/history" ? "3" : "2"}
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"
                  />
                </svg>
              </span>

              <div className={styles.dashboard}>
                <Link
                  className={`${
                    verificationStatus !== "Dash_Board"
                      ? styles.disabledForLaunch
                      : `${styles.btnEnabled} ${
                          pathname === "/pages/history" ? styles.btnActive : ""
                        }`
                  }`}
                  href={
                    verificationStatus === "Dash_Board" ? "/pages/history" : {}
                  }
                >
                  History
                </Link>
              </div>
            </div>
            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/accounts" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={pathname === "/pages/accounts" ? "3" : "2"}
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M17.593 3.322c1.1.128 1.907 1.077 1.907 2.185V21L12 17.25 4.5 21V5.507c0-1.108.806-2.057 1.907-2.185a48.507 48.507 0 0 1 11.186 0Z"
                  />
                </svg>
              </span>

              <div className={styles.dashboard}>
                <Link
                  className={`${
                    verificationStatus !== "Dash_Board"
                      ? styles.disabledForLaunch
                      : `${styles.btnEnabled} ${
                          pathname === "/pages/accounts" ? styles.btnActive : ""
                        }`
                  }`}
                  href={
                    verificationStatus === "Dash_Board" ? "/pages/accounts" : {}
                  }
                >
                  Saved Accounts
                </Link>
              </div>
            </div>
            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/mylistings" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={pathname === "/pages/mylistings" ? "3" : "2"}
                  stroke="currentColor"
                  className="size-6"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0ZM3.75 12h.007v.008H3.75V12Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Zm-.375 5.25h.007v.008H3.75v-.008Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z"
                  />
                </svg>
              </span>

              <div className={styles.dashboard}>
                <Link
                  className={`${
                    verificationStatus !== "Dash_Board"
                      ? styles.disabledForLaunch
                      : `${styles.btnEnabled} ${
                          pathname === "/pages/mylistings" ? styles.btnActive : ""
                        }`
                  }`}
                  href={
                    verificationStatus === "Dash_Board" ? "/pages/mylistings" : {}
                  }
                >
                  My Listings
                </Link>
              </div>
            </div>
            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/profile" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  strokeWidth={pathname === "/pages/profile" ? "3" : "2"}
                  className="size-6"
                >
                  <path
                    fillRule="evenodd"
                    d="M7.5 6a4.5 4.5 0 1 1 9 0 4.5 4.5 0 0 1-9 0ZM3.751 20.105a8.25 8.25 0 0 1 16.498 0 .75.75 0 0 1-.437.695A18.683 18.683 0 0 1 12 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 0 1-.437-.695Z"
                    clipRule="evenodd"
                  />
                </svg>
              </span>

              <div className={styles.dashboard}>
                <Link
                  className={`${styles.btnEnabled} ${
                    pathname === "/pages/profile" ? styles.btnActive : ""
                  }`}
                  href="/pages/profile"
                >
                  Profile
                </Link>
              </div>
            </div>
            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/settings" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  strokeWidth={pathname === "/pages/settings" ? "3" : "2"}
                  className="size-6"
                >
                  <path
                    fillRule="evenodd"
                    d="M11.078 2.25c-.917 0-1.699.663-1.85 1.567L9.05 4.889c-.02.12-.115.26-.297.348a7.493 7.493 0 00-.986.57c-.166.115-.334.126-.45.083L6.3 5.508a1.875 1.875 0 00-2.282.819l-.922 1.597a1.875 1.875 0 00.432 2.385l.84.692c.095.078.17.229.154.43a7.598 7.598 0 000 1.139c.015.2-.059.352-.153.43l-.841.692a1.875 1.875 0 00-.432 2.385l.922 1.597a1.875 1.875 0 002.282.818l1.019-.382c.115-.043.283-.031.45.082.312.214.641.405.985.57.182.088.277.228.297.35l.178 1.071c.151.904.933 1.567 1.85 1.567h1.844c.916 0 1.699-.663 1.85-1.567l.178-1.072c.02-.12.114-.26.297-.349.344-.165.673-.356.985-.57.167-.114.335-.125.45-.082l1.02.382a1.875 1.875 0 002.28-.819l.923-1.597a1.875 1.875 0 00-.432-2.385l-.84-.692c-.095-.078-.17-.229-.154-.43a7.614 7.614 0 000-1.139c-.016-.2.059-.352.153-.43l.84-.692c.708-.582.891-1.59.433-2.385l-.922-1.597a1.875 1.875 0 00-2.282-.818l-1.02.382c-.114.043-.282.031-.449-.083a7.49 7.49 0 00-.985-.57c-.183-.087-.277-.227-.297-.348l-.179-1.072a1.875 1.875 0 00-1.85-1.567h-1.843zM12 15.75a3.75 3.75 0 100-7.5 3.75 3.75 0 000 7.5z"
                    clipRule="evenodd"
                  />
                </svg>
              </span>
              <div className={styles.dashboard}>
                <Link
                  className={`${
                    verificationStatus !== "Dash_Board"
                      ? styles.disabledForLaunch
                      : `${styles.btnEnabled} ${
                          pathname === "/pages/settings" ? styles.btnActive : ""
                        }`
                  }`}
                  href={
                    verificationStatus === "Dash_Board" ? "/pages/settings" : {}
                  }
                >
                  Settings
                </Link>
              </div>
            </div>
            <div className={styles.dashboardContainer}>
              <span
                className={`${styles.sideIcons} ${
                  pathname === "/pages/help" ? styles.sideIconsActive : ""
                }`}
              >
                <svg
                  width={18}
                  height={18}
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="currentColor"
                  strokeWidth={pathname === "/pages/help" ? "3" : "2"}
                  className="size-6"
                >
                  <path
                    fillRule="evenodd"
                    d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </span>
              <div className={styles.dashboard}>
                <Link
                  className={`${
                    verificationStatus !== "Dash_Board"
                      ? styles.disabledForLaunch
                      : `${styles.btnEnabled} ${
                          pathname === "/pages/help" ? styles.btnActive : ""
                        }`
                  }`}
                  href={
                    verificationStatus === "Dash_Board" ? "/pages/help" : {}
                  }
                >
                  Help
                </Link>
              </div>
            </div>
            {(token || localToken) && (
              <div className={styles.dashboardContainer}>
                <span className={styles.sideIcons}>
                  <svg
                    width={18}
                    height={18}
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth="1.5"
                    stroke="currentColor"
                    className="size-6"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M8.25 9V5.25A2.25 2.25 0 0 1 10.5 3h6a2.25 2.25 0 0 1 2.25 2.25v13.5A2.25 2.25 0 0 1 16.5 21h-6a2.25 2.25 0 0 1-2.25-2.25V15m-3 0-3-3m0 0 3-3m-3 3H15"
                    />
                  </svg>
                </span>
                <div className={styles.dashboard} onClick={()=> setShowLogoutModal(true)}>
                  Logout
                </div>
              </div>
            )}
          </div>
          {/* <ToastContainer /> */}
        </div>
      </div>
    </>
  );
};

export default page;
