import axios from "axios";
import refreshTokenApi from "../api/onboarding/refreshTokenEndpoint";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
let authToken;
// let expiry_access;
// let serverTime;

const sessionLogoutHandler = async () => {
  try {
    setTimeout(() => {
      if (typeof window !== "undefined") {
        localStorage.clear();
        sessionStorage.clear();
      }

      if (typeof window !== "undefined") {
        window.location.href = "/sign/login";
      }
    }, 1500);
  } catch (error) {
    console.error(error);
    console.log(" Please try again.");
  }
};
if (typeof window !== "undefined") {
  authToken = sessionStorage.getItem("user");
}

export const customFetchWithToken = axios.create({
  baseURL: Base_url,
  headers: {
    Authorization: `Bearer ${authToken}`,
  },
  withCredentials: true,
});

let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

customFetchWithToken.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response && error.response.status === 401) {
      if (!isRefreshing) {
        isRefreshing = true;
        try {
          const res = await refreshApiCall();

          authToken = res.data.data.access_token;
          customFetchWithToken.defaults.headers.Authorization = `Bearer ${authToken}`;
          processQueue(null, authToken);
          originalRequest.headers.Authorization = `Bearer ${authToken}`;
          return customFetchWithToken(originalRequest);
        } catch (err) {
          processQueue(err, null);
          return Promise.reject(err);
        } finally {
          isRefreshing = false;
        }
      } else {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return customFetchWithToken(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }
    }
    return Promise.reject(error);
  }
);

const refreshApiCall = async () => {
  try {
    const res = await refreshTokenApi();
    localStorage.setItem("user", res.data.data.access_token);
    return res;
  } catch (error) {
    if (error) {
      sessionLogoutHandler();
    }
    console.log(error);
    throw error;
  }
};

// import axios from "axios";
// import refreshTokenApi from "../api/onboarding/refreshTokenEndpoint";

// const Base_url = process.env.NEXT_PUBLIC_Base_URL;
// let authToken;
// let expiry_access;
// let serverTime;

// if (typeof window !== "undefined") {
//   authToken = sessionStorage.getItem("user");
//   expiry_access = localStorage.getItem("expires_access");
//   serverTime = localStorage.getItem("serverTime");
// }

// export const customFetchWithToken = axios.create({
//   baseURL: Base_url,
//   headers: {
//     Authorization: `Bearer ${authToken}`,
//   },
//   withCredentials: true,
// });

// let isRefreshing = false;
// let failedQueue = [];

// const processQueue = (error, token = null) => {
//   failedQueue.forEach((prom) => {
//     if (error) {
//       prom.reject(error);
//     } else {
//       prom.resolve(token);
//     }
//   });

//   failedQueue = [];
// };

// customFetchWithToken.interceptors.response.use(
//   (response) => response,
//   async (error) => {
//     const originalRequest = error.config;
//     if (error.response && error.response.status === 401) {
//       if (!isRefreshing) {
//         isRefreshing = true;
//         try {
//           const res = await refreshApiCall();
//           authToken = res.data.data.access_token;
//           customFetchWithToken.defaults.headers.Authorization = `Bearer ${authToken}`;
//           processQueue(null, authToken);
//           originalRequest.headers.Authorization = `Bearer ${authToken}`;
//           return customFetchWithToken(originalRequest);
//         } catch (err) {
//           processQueue(err, null);
//           throw err;
//         } finally {
//           isRefreshing = false;
//         }
//       }

//       return new Promise((resolve, reject) => {
//         failedQueue.push({ resolve, reject });
//       })
//         .then(async (token) => {
//           originalRequest.headers.Authorization = `Bearer ${token}`;
//           return customFetchWithToken(originalRequest);
//         })
//         .catch((err) => {
//           throw err;
//         });
//     }

//     throw error;
//   }
// );

// const refreshApiCall = async () => {
//   try {
//     const res = await refreshTokenApi();
//     localStorage.setItem("user", res.data.data.access_token);
//     localStorage.setItem("expires_access", res.data.data.expires_access_unix);
//     localStorage.setItem("serverTime", res.data.data.server_time_unix);
//     return res;
//   } catch (error) {
//     console.log(error);
//     throw error;
//   }
// };
