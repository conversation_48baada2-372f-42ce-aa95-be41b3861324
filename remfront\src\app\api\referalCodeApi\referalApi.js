import axios from "axios";

const Base_Url = process.env.NEXT_PUBLIC_Base_URL;

if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
  var userEmail = localStorage.getItem("userEmail");
  var userName = localStorage.getItem("userName");
}
const Data1 = {
  email: userEmail,
  name: userName,
};

export const generateRefCode = async (Data) => {
  const res = await axios({
    url: `${Base_Url}/referral-code/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};

export const verifyRefCode = async () => {
  const res = await axios({
    url: `${Base_Url}/check-status/`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
