/* Modern Transaction Table Styles */

/* Desktop Table Styles */
.tableRow {
  border-bottom: 1px solid #e2e8f0;
  transition: all 0.2s ease;
  background: white;
}

.tableRow:hover {
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.tableCell {
  padding: 16px 12px;
  vertical-align: middle;
  border: none;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Date Container */
.dateContainer {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dateText {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.2;
}

.timeText {
  font-size: 12px;
  color: #718096;
  font-weight: 400;
}

/* ID Containers */
.idContainer {
  display: flex;
  align-items: center;
}

.idLabel {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  font-weight: 600;
  color: #4a5568;
  background: #f7fafc;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.traderIdText {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  font-weight: 500;
  color: #2d3748;
}


/* Amount Container */
.amountContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.amountText {
  font-size: 16px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.2;
}

.currencyText {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
}

/* Currency Container */
.currencyContainer {
  display: flex;
  align-items: center;
}

.currencyBadge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

/* Status Badge Styles */
.statusBadge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
  transition: all 0.2s ease;
  min-width: 80px;
  justify-content: center;
}

.statusIcon {
  font-size: 12px;
  line-height: 1;
}

.statusText {
  line-height: 1;
}

/* Status Variants */
.statusSuccess {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
}

.statusPending {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
}

.statusFailed {
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);
}

.statusDefault {
  background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(160, 174, 192, 0.3);
}

/* Mobile Card Styles */
.mobileCard {
  display: none;
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.mobileCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.mobileCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e2e8f0;
}

.mobileTransactionInfo {
  flex: 1;
}

.mobileOrderId {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.mobileDateText {
  font-size: 13px;
  color: #718096;
  font-weight: 500;
}

.mobileStatusBadge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  text-transform: capitalize;
  min-width: 70px;
  justify-content: center;
}

.mobileCardBody {
  margin-bottom: 16px;
}

.mobileAmountSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.mobileAmountContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.mobileAmountText {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.2;
}

.mobileCurrencyText {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  margin-top: 2px;
}

.mobileTraderInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.mobileTraderLabel {
  font-size: 11px;
  color: #718096;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mobileTraderValue {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  font-weight: 600;
  color: #4a5568;
  margin-top: 2px;
}

.mobileCardFooter {
  padding-top: 12px;
  border-top: 1px solid #f7fafc;
}

.mobileMetadata {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mobileMetaItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.mobileMetaLabel {
  font-size: 10px;
  color: #a0aec0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.mobileMetaValue {
  font-size: 12px;
  color: #4a5568;
  font-weight: 600;
}

.mobileCurrencyBadge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
  font-size: 10px;
  font-weight: 700;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
  .tableRow {
    display: none;
  }

  .mobileCard {
    display: block;
  }
}

/* Hover Effects for Mobile */
@media (hover: hover) and (pointer: fine) {
  .statusBadge:hover,
  .mobileStatusBadge:hover {
    transform: scale(1.05);
  }

  .currencyBadge:hover,
  .mobileCurrencyBadge:hover {
    transform: scale(1.05);
  }
}

/* Focus States for Accessibility */
.mobileCard:focus-within {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .statusBadge,
  .mobileStatusBadge,
  .currencyBadge,
  .mobileCurrencyBadge {
    border: 2px solid currentColor;
  }

  .mobileCard {
    border: 2px solid #2d3748;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .tableRow,
  .mobileCard,
  .statusBadge,
  .mobileStatusBadge {
    transition: none;
  }

  .tableRow:hover,
  .mobileCard:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .mobileCard {
    display: block;
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .tableRow {
    display: table-row;
  }
}