"use client";
import { useState, useEffect } from "react";
import styles from "./requestWithdrawRemfunds.module.css";
import WithdrawlHistory from "../withdrawHistory/page";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { set } from "lodash";

const requestWithdrawRemfunds = ({ walletAddress, onClose }) => {
  const router = useRouter();
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(true);
  const [withdrawRequestArray, setWithdrawRequestArray] = useState([]);
  const [editSuccess, setEditSuccess] = useState(false);
  const [selectedNetwork, setSelectedNetwork] = useState("");
  const [amount, setAmount] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [savedExternalWalletsArray, setSavedExternalWalletsArray] = useState([]);
  const [selectedExternalWallet, setSelectedExternalWallet] = useState("");
  const [withdrawlFee, setWithdrawlFee] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleShowRequestModal = () => {
    setShowRequestModal(true);
    setShowHistoryModal(false);
  };
  const handleShowHistoryModal = () => {
    setShowHistoryModal(true);
    setShowRequestModal(false);
  };

  const handleCloseModal = () => {
    // Call onClose to notify parent to remove the modal
    if (onClose) {
      onClose();
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCloseModal();
    }
  };

  // Handle ESC key press
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        handleCloseModal();
      }
    };

    document.addEventListener('keydown', handleEscKey);
    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'unset';
    };
  }, []);



  const validateForm = () => {
    const newErrors = {};

    if (!selectedNetwork) {
      newErrors.network = "Please select a network";
    }
    if (!selectedExternalWallet) {
      newErrors.wallet = "Please select an external wallet";
    }
    if (!amount || parseFloat(amount) <= 0) {
      newErrors.amount = "Please enter a valid amount";
    }
    if (!verificationCode || verificationCode.length !== 6) {
      newErrors.verification = "Please enter a valid 6-digit verification code";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSetAmount = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");
    if (inputValue.length > 20) {
      setAmount(inputValue.slice(0, 20));
    } else {
      setAmount(inputValue);
    }
    if (errors.amount) {
      setErrors(prev => ({ ...prev, amount: "" }));
    }
  };

  const handle2faVerificationCode = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9]/g, "");
    if (inputValue.length <= 6) {
      setVerificationCode(inputValue);
    }
    if (errors.verification) {
      setErrors(prev => ({ ...prev, verification: "" }));
    }
  };

  const getExternalWallets = async () => {
    try {
      const res = await customFetchWithToken.get("/get-external-wallet/");

      setSavedExternalWalletsArray(res.data.data);
    } catch (error) {
      console.log(error);
    }
  };

  const handleCreateWithdrawlReq = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const res = await customFetchWithToken.post("/create-withdraw-request/", {
        amount: parseFloat(amount),
        address: selectedExternalWallet,
      });
      toast.success(res.data.message);
      setEditSuccess(true);
      // Reset form
      setAmount("");
      setVerificationCode("");
      setSelectedNetwork("");
      setSelectedExternalWallet("");
      setErrors({});
    } catch (error) {
      console.log(error);
      toast.error(error.response?.data?.message || "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const getWithdrawRequest = async () => {
    try {
      const res = await customFetchWithToken.get("/get-withdraw-request/");
      setWithdrawRequestArray(res.data.data);
      setEditSuccess(false);
    } catch (error) {
      console.log(error);
    }
  };

  const getWithdrawlFee = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/withdraw-fee/?network=${selectedNetwork}&coin=TRX`
      );

      setWithdrawlFee(res.data.data[0].withdrawFee);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getExternalWallets();
  }, []);
  useEffect(() => {
    if (selectedNetwork) {
      getWithdrawlFee();
    }
  }, [selectedNetwork]);
  useEffect(() => {
    getWithdrawRequest();
  }, [editSuccess]);
  return (
    <div className={styles.modalOverlay} onClick={handleOverlayClick}>
      <div className={styles.modalContainer}>
        <div className={styles.modalHeader}>
          <button
            className={styles.closeButton}
            onClick={handleCloseModal}
            aria-label="Close modal"
            type="button"
          >
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          <h1 className={styles.modalTitle}>Withdraw Management</h1>
          <p className={styles.modalSubtitle}>Manage your withdrawal requests and view transaction history</p>
        </div>

          <div className={styles.tabContainer}>
            <button
              className={!showHistoryModal ? styles.tabActive : styles.tabInactive}
              onClick={handleShowRequestModal}
              aria-pressed={!showHistoryModal}
            >
              <span className={styles.tabIcon}>💸</span>
              Request Withdraw
            </button>
            <button
              className={showHistoryModal ? styles.tabActive : styles.tabInactive}
              onClick={handleShowHistoryModal}
              aria-pressed={showHistoryModal}
            >
              <span className={styles.tabIcon}>📋</span>
              Withdraw History
            </button>
          </div>
      {showRequestModal && (
        <div className={styles.formContainer}>
          <div className={styles.formCard}>
            <div className={styles.formHeader}>
              <h2 className={styles.formTitle}>New Withdrawal Request</h2>
              <p className={styles.formDescription}>Fill in the details below to request a withdrawal</p>
            </div>

            <div className={styles.formGroup}>
              <label className={styles.label} htmlFor="network-select">
                Network <span className={styles.required}>*</span>
              </label>
              <div className={styles.selectWrapper}>
                <select
                  id="network-select"
                  className={`${styles.select} ${errors.network ? styles.selectError : ''}`}
                  value={selectedNetwork}
                  onChange={(e) => {
                    setSelectedNetwork(e.target.value);
                    if (errors.network) {
                      setErrors(prev => ({ ...prev, network: "" }));
                    }
                  }}
                  aria-describedby={errors.network ? "network-error" : undefined}
                >
                  <option value="">Select Network</option>
                  <option value="BSC">BSC (Binance Smart Chain)</option>
                  <option value="TRX">TRX (Tron)</option>
                </select>
                <div className={styles.selectIcon}>
                  <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                    <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
              {errors.network && (
                <span id="network-error" className={styles.errorMessage} role="alert">
                  {errors.network}
                </span>
              )}
            </div>

            {withdrawlFee && (
              <div className={styles.feeNotice}>
                <div className={styles.feeIcon}>ℹ️</div>
                <span className={styles.feeText}>
                  Withdrawal fee: <strong>{withdrawlFee} USDT</strong>
                </span>
              </div>
            )}

            <div className={styles.formGroup}>
              <label className={styles.label} htmlFor="wallet-select">
                External Wallet <span className={styles.required}>*</span>
              </label>
              <div className={styles.selectWrapper}>
                <select
                  id="wallet-select"
                  className={`${styles.select} ${errors.wallet ? styles.selectError : ''}`}
                  value={selectedExternalWallet}
                  onChange={(e) => {
                    setSelectedExternalWallet(e.target.value);
                    if (errors.wallet) {
                      setErrors(prev => ({ ...prev, wallet: "" }));
                    }
                  }}
                  aria-describedby={errors.wallet ? "wallet-error" : undefined}
                >
                  <option value="">Select external wallet</option>
                  {savedExternalWalletsArray.map((item, index) => (
                    <option key={index} value={item.wallet_address}>
                      {item.wallet_address.slice(0, 8)}...{item.wallet_address.slice(-8)}
                    </option>
                  ))}
                </select>
                <div className={styles.selectIcon}>
                  <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                    <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
              {errors.wallet && (
                <span id="wallet-error" className={styles.errorMessage} role="alert">
                  {errors.wallet}
                </span>
              )}
            </div>
            <div className={styles.formGroup}>
              <label className={styles.label} htmlFor="amount-input">
                Amount (USDT) <span className={styles.required}>*</span>
              </label>
              <div className={styles.inputWrapper}>
                <input
                  id="amount-input"
                  type="number"
                  placeholder="Enter amount"
                  value={amount}
                  onChange={handleSetAmount}
                  className={`${styles.input} ${errors.amount ? styles.inputError : ''}`}
                  min="0"
                  step="0.01"
                  aria-describedby={errors.amount ? "amount-error" : undefined}
                />
                <div className={styles.inputIcon}>💰</div>
              </div>
              {errors.amount && (
                <span id="amount-error" className={styles.errorMessage} role="alert">
                  {errors.amount}
                </span>
              )}
            </div>

            <div className={styles.formGroup}>
              <label className={styles.label} htmlFor="verification-input">
                2FA Verification Code <span className={styles.required}>*</span>
              </label>
              <div className={styles.inputWrapper}>
                <input
                  id="verification-input"
                  type="text"
                  placeholder="Enter 6-digit code"
                  value={verificationCode}
                  onChange={handle2faVerificationCode}
                  className={`${styles.input} ${errors.verification ? styles.inputError : ''}`}
                  maxLength="6"
                  pattern="[0-9]{6}"
                  aria-describedby={errors.verification ? "verification-error" : "verification-help"}
                />
                <div className={styles.inputIcon}>🔐</div>
              </div>
              {errors.verification && (
                <span id="verification-error" className={styles.errorMessage} role="alert">
                  {errors.verification}
                </span>
              )}
              <div className={styles.helpText}>
                <span id="verification-help">Don't have 2FA setup?</span>
                <button
                  type="button"
                  className={styles.linkButton}
                  onClick={() => router.push("/pages/profile")}
                >
                  Setup now
                </button>
              </div>
            </div>

            <div className={styles.formActions}>
              <button
                type="button"
                className={styles.submitButton}
                onClick={handleCreateWithdrawlReq}
                disabled={isLoading}
                aria-describedby="submit-help"
              >
                {isLoading ? (
                  <>
                    <div className={styles.spinner}></div>
                    Processing...
                  </>
                ) : (
                  <>
                    <span className={styles.buttonIcon}>🚀</span>
                    Request Withdrawal
                  </>
                )}
              </button>
              <p id="submit-help" className={styles.submitHelp}>
                Please review all details before submitting your withdrawal request
              </p>
            </div>
          </div>
        </div>
      )}
      {showHistoryModal && (
        <div className={styles.historyContainer}>
          <div className={styles.historyHeader}>
            <h2 className={styles.historyTitle}>Withdrawal History</h2>
            <p className={styles.historyDescription}>Track your withdrawal requests and their status</p>
          </div>

          <div className={styles.historyContent}>
            {withdrawRequestArray && withdrawRequestArray.length > 0 ? (
              <div className={styles.historyList}>
                {withdrawRequestArray.map((item, index) => (
                  <WithdrawlHistory
                    key={item.id || index}
                    id={item.id}
                    amount={item.amount}
                    address={item.external_wallet}
                    status={item.status}
                    setEditSuccess={setEditSuccess}
                  />
                ))}
              </div>
            ) : (
              <div className={styles.emptyState}>
                <div className={styles.emptyIcon}>📭</div>
                <h3 className={styles.emptyTitle}>No withdrawal history</h3>
                <p className={styles.emptyDescription}>
                  You haven't made any withdrawal requests yet. Create your first withdrawal request to see it here.
                </p>
                <button
                  className={styles.emptyAction}
                  onClick={handleShowRequestModal}
                >
                  Make First Withdrawal
                </button>
              </div>
            )}
          </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default requestWithdrawRemfunds;
