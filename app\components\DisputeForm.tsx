import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { showToastSuccess, showToastError } from "../hooks/toast";
import customFetchWithToken from "../app/utils/axiosInterceptor";
import { Ionicons } from "@expo/vector-icons";
import * as ImagePicker from "expo-image-picker";

interface DisputeFormProps {
  title: string;
  selectedId: number;
}

interface OrderData {
  order_number: string;
}

interface FileUpload {
  uri: string;
  type: string;
  name: string;
}

const DisputeForm: React.FC<DisputeFormProps> = ({ title, selectedId }) => {
  const [uploadEvidence, setUploadEvidence] = useState<string>("");
  const [comment, setComment] = useState<string>("");
  const [selectedOrderNumber, setSelectedOrderNumber] = useState<string>("");
  const [loadDisputeDrop, setLoadDisputeDrop] = useState<OrderData[]>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  console.log("disputeTitle", selectedId, title);
  console.log("uploadEvidence", uploadEvidence);
  console.log("order Number", selectedOrderNumber);
  console.log("Comments", comment);

  const validateFields = () => {
    if (!selectedOrderNumber || !uploadEvidence || !comment) {
      showToastError("All fields are required");
      return false;
    }
    if (comment.length > 200) {
      showToastError("Comment must not exceed 200 characters");
      return false;
    }
    return true;
  };

  const handleSubmit = async () => {
    if (!validateFields() || isSubmitting) return;

    setIsSubmitting(true);
    const formData = new FormData();

    try {
      // Add all required fields exactly as shown in the curl command
      formData.append("disput_title", selectedId.toString());
      formData.append("comments", comment);
      formData.append("order_number", selectedOrderNumber);
      if (uploadEvidence && typeof uploadEvidence === "string") {
        const evidenceFile = {
          uri: uploadEvidence,
          type: "image/jpeg",
          name: "evidence.jpg",
        } as unknown as File;
        formData.append("upload_evidence", evidenceFile);
      }

      const response = await customFetchWithToken.post(
        "/dispute-list/",
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (response.data) {
        showToastSuccess(
          response.data.message || "Dispute submitted successfully"
        );
        // Reset form
        setUploadEvidence("");
        setComment("");
        setSelectedOrderNumber("");
      }
    } catch (error: any) {
      console.error("Full error details:", {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status,
        data: error.response?.data,
      });

      const errorMessage =
        error.response?.data?.message || "Failed to submit dispute";
      showToastError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchOrderNumbers = async () => {
    try {
      const response = await customFetchWithToken.get("/order-dispute-list/");
      setLoadDisputeDrop(response.data.data || []);
    } catch (error) {
      console.error("Error fetching order numbers:", error);
      showToastError("Failed to load order numbers");
    }
  };

  const handleImageSelection = async () => {
    try {
      const permissionResult =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (!permissionResult.granted) {
        alert("Permission to access photos is required!");
        return;
      }

      // Open the image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ["images"],
        allowsEditing: true, // Allow users to edit the image
        aspect: [1, 1], // Crop the image to a square aspect ratio
        quality: 1, // High-quality image
      });

      console.log(result);
      // Ensure the result is valid
      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.error("Image selection was canceled or no assets found.");
        return;
      }

      const selectedImageUri = result.assets[0].uri;

      setUploadEvidence(selectedImageUri);
      console.log("Selected image:", selectedImageUri); // Debug log
    } catch (error) {
      console.error("Image selection error:", error);
      showToastError("Failed to select image");
    }
  };

  useEffect(() => {
    fetchOrderNumbers();
  }, []);

  return (
    <ScrollView className="w-full p-4 bg-gray-100">
      <View className="mb-4">
        <Text className="text-lg font-semibold text-blue-600">{title}</Text>
      </View>

      <View className="mb-4">
        <Text className="text-sm font-medium">Order Number</Text>
        <Picker
          selectedValue={selectedOrderNumber}
          onValueChange={setSelectedOrderNumber}
        >
          <Picker.Item label="Select Order Number" value="" />
          {loadDisputeDrop?.map((order, index) => (
            <Picker.Item
              key={index}
              label={order.order_number}
              value={order.order_number}
            />
          ))}
        </Picker>
      </View>

      <View className="mb-4">
        <Text className="text-sm font-medium">Evidence Upload</Text>
        <TouchableOpacity
          className="p-4 border border-gray-300 rounded mt-2 flex-row items-center justify-center"
          onPress={handleImageSelection}
          disabled={isSubmitting}
        >
          {uploadEvidence ? (
            <>
              <Ionicons name="checkmark-circle" size={24} color="green" />
              <Text className="ml-2 font-medium">Image Selected</Text>
            </>
          ) : (
            <>
              <Ionicons name="cloud-upload" size={24} color="gray" />
              <Text className="ml-2 font-medium">Upload Evidence</Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      <View className="mb-4">
        <Text className="text-sm font-medium">Comments</Text>
        <TextInput
          className="p-4 border border-gray-300 rounded mt-2"
          placeholder="Enter your comments here (max 200 characters)"
          maxLength={200}
          multiline
          value={comment}
          onChangeText={setComment}
          editable={!isSubmitting}
        />
        <Text className="text-right text-xs text-gray-500">
          {comment.length}/200
        </Text>
      </View>

      <TouchableOpacity
        className={`p-4 rounded items-center mt-4 mb-10 ${
          isSubmitting ? "bg-gray-400" : "bg-blue-600"
        }`}
        onPress={handleSubmit}
        disabled={isSubmitting}
      >
        <Text className="text-white font-medium">
          {isSubmitting ? "Submitting..." : "Submit Dispute"}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default DisputeForm;
