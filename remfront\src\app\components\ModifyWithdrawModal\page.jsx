"use client";
import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import styles from "./modify.module.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { toast } from "react-toastify";

const ModifyModal = ({
  id,
  showModal,
  setShowModal,
  amount,
  address,
  setEditSuccess,
}) => {
  const [amount1, setAmount1] = useState(amount || "");
  const [address1, setAddress1] = useState(address || "");
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  // Update local state when props change
  useEffect(() => {
    setAmount1(amount || "");
    setAddress1(address || "");
  }, [amount, address]);

  // Handle ESC key and body scroll
  useEffect(() => {
    if (showModal) {
      const handleEscKey = (event) => {
        if (event.key === 'Escape') {
          handleCloseModal();
        }
      };

      document.addEventListener('keydown', handleEscKey);
      document.body.style.overflow = 'hidden';

      return () => {
        document.removeEventListener('keydown', handleEscKey);
        document.body.style.overflow = 'unset';
      };
    }
  }, [showModal]);

  const validateForm = () => {
    const newErrors = {};

    if (!amount1 || parseFloat(amount1) <= 0) {
      newErrors.amount = "Please enter a valid amount";
    }
    if (!address1 || address1.length < 10) {
      newErrors.address = "Please enter a valid wallet address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleModify = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      const res = await customFetchWithToken.put(
        `/edit-withdraw-request/${id}`,
        {
          amount: parseFloat(amount1),
          external_wallet: address1,
        }
      );

      toast.success(res.data.message);
      setEditSuccess(true);
      setShowModal(false);
      // Reset form
      setAmount1("");
      setAddress1("");
      setErrors({});
    } catch (error) {
      console.log(error);
      toast.error(error.response?.data?.message || "Failed to update withdrawal request");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setErrors({});
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      handleCloseModal();
    }
  };

  const handleSetAmount1 = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");

    if (inputValue.length <= 20) {
      setAmount1(inputValue);
    }

    if (errors.amount) {
      setErrors(prev => ({ ...prev, amount: "" }));
    }
  };

  const handleWalletAddress1 = (e) => {
    const value = e.target.value;

    if (value.length <= 100) {
      setAddress1(value);
    }

    if (errors.address) {
      setErrors(prev => ({ ...prev, address: "" }));
    }
  };

  // Don't render if modal is not shown
  if (!showModal) {
    return null;
  }

  // Create modal content
  const modalContent = (
    <div className={styles.modalOverlay} onClick={handleOverlayClick}>
      <div className={styles.modalContainer}>
        {/* Modal Header */}
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>Edit Withdrawal Request</h2>
          <button
            className={styles.closeButton}
            onClick={handleCloseModal}
            aria-label="Close modal"
            type="button"
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        {/* Modal Body */}
        <div className={styles.modalBody}>
          <div className={styles.formGroup}>
            <label className={styles.label} htmlFor="amount-input">
              Amount (USDT) <span className={styles.required}>*</span>
            </label>
            <div className={styles.inputWrapper}>
              <input
                id="amount-input"
                className={`${styles.input} ${errors.amount ? styles.inputError : ''}`}
                type="number"
                placeholder="Enter amount"
                value={amount1}
                onChange={handleSetAmount1}
                min="0"
                step="0.01"
                aria-describedby={errors.amount ? "amount-error" : undefined}
              />
              <div className={styles.inputIcon}>💰</div>
            </div>
            {errors.amount && (
              <span id="amount-error" className={styles.errorMessage} role="alert">
                {errors.amount}
              </span>
            )}
          </div>

          <div className={styles.formGroup}>
            <label className={styles.label} htmlFor="address-input">
              Wallet Address <span className={styles.required}>*</span>
            </label>
            <div className={styles.inputWrapper}>
              <input
                id="address-input"
                className={`${styles.input} ${errors.address ? styles.inputError : ''}`}
                type="text"
                placeholder="Enter wallet address"
                value={address1}
                onChange={handleWalletAddress1}
                maxLength={100}
                aria-describedby={errors.address ? "address-error" : undefined}
              />
              <div className={styles.inputIcon}>🔗</div>
            </div>
            {errors.address && (
              <span id="address-error" className={styles.errorMessage} role="alert">
                {errors.address}
              </span>
            )}
          </div>
        </div>

        {/* Modal Footer */}
        <div className={styles.modalFooter}>
          <button
            className={styles.cancelButton}
            onClick={handleCloseModal}
            type="button"
          >
            Cancel
          </button>
          <button
            className={styles.saveButton}
            onClick={handleModify}
            disabled={isLoading}
            type="button"
          >
            {isLoading ? (
              <>
                <div className={styles.spinner}></div>
                Updating...
              </>
            ) : (
              <>
                <span className={styles.buttonIcon}>💾</span>
                Save Changes
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );

  // Use portal to render modal at document body level
  return createPortal(modalContent, document.body);
};

export default ModifyModal;
