/* Base styles for mobile view container */
.history_container {
    display: block;
    visibility: visible;
    opacity: 1;
}

@media screen and (max-width: 576px) {
    .history_container {
        width: 98%;
        margin: 0 auto 16px auto;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #e2e8f0;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        box-sizing: border-box;
        max-width: 100%;
    }

    .history_container:hover {
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }

    .history_container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-radius: 16px 16px 0 0;
    }

    /* Status Header Section */
    .statusHeader {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #f1f5f9;
    }

    .orderInfo {
        display: flex;
        flex-direction: column;
        gap: 4px;
        flex: 1;
    }

    .orderLabel {
        font-family: 'Poppins', sans-serif;
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
    }

    .dateLabel {
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        color: #64748b;
        font-weight: 500;
    }

    .statusBadge {
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-weight: 600;
        color: white;
        padding: 6px 12px;
        border-radius: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        flex-shrink: 0;
    }

    /* Main Content Section */
    .mainContent {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 20px;
    }

    .tradeAmountHighlight {
        font-family: 'Poppins', sans-serif;
        font-size: 18px;
        font-weight: 700;
        color: #0f172a;
        text-align: center;
        background: linear-gradient(135deg, #fef3c7, #fde68a);
        padding: 12px 16px;
        border-radius: 12px;
        border: 2px solid #f59e0b;
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .exchangeRate {
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #1e293b;
        text-align: center;
        background: #f8fafc;
        padding: 8px 12px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    /* Trade Details */
    .tradeDetails {
        display: flex;
        flex-direction: column;
        gap: 8px;
        background: #f8fafc;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        width: 100%;
        box-sizing: border-box;
    }

    .detailRow {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        color: #1e293b;
    }

    .detailLabel {
        color: #64748b;
        font-weight: 500;
    }

    .detailValue {
        font-weight: 600;
        color: #1e293b;
    }

    /* Payment Methods */
    .paymentMethods {
        display: flex;
        flex-direction: column;
        gap: 12px;
        background: #f8fafc;
        padding: 16px;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        width: 100%;
        box-sizing: border-box;
    }

    .paymentMethod {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-family: 'Poppins', sans-serif;
        font-size: 13px;
        font-weight: 500;
        color: #64748b;
        padding: 10px 12px;
        background: white;
        border-radius: 8px;
        border: 1px solid #d1d5db;
        width: 100%;
        box-sizing: border-box;
    }

    .methodLabel {
        color: #64748b;
    }

    .methodValue {
        font-weight: 600;
        color: #1e293b;
        flex: 1;
        text-align: center;
    }

    .methodIndicator {
        width: 6px;
        height: 12px;
        background: linear-gradient(135deg, #10b981, #059669);
        border-radius: 3px;
        flex-shrink: 0;
    }

    /* Peer Info */
    .peerInfo {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-family: 'Poppins', sans-serif;
        font-size: 13px;
        background: #f1f5f9;
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    .peerLabel {
        color: #64748b;
        font-weight: 500;
    }

    .peerValue {
        font-weight: 600;
        color: #1e293b;
    }

    /* Action Section */
    .actionSection {
        width: 100%;
        margin-top: 8px;
    }

    .actionButton {
        width: 100%;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        padding: 14px 20px;
        border-radius: 12px;
        font-family: 'Poppins', sans-serif;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }

    .actionButton:hover {
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
    }

    /* Status Badge Colors */
    .pending {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
    }

    .expired {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3);
    }

    .rejected {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
    }

    .ongoing {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    }

    .completed {
        background: linear-gradient(135deg, #10b981, #059669);
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    }

    .notified {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
    }

    .cancelled {
        background: linear-gradient(135deg, #9ca3af, #6b7280);
        box-shadow: 0 2px 8px rgba(156, 163, 175, 0.3);
    }

    /* Pending Actions Section */
    .pendingActions {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        width: 100%;
        margin-top: 8px;
    }

    .pendingMessage {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #374151;
        text-align: center;
        background: #f8fafc;
        padding: 12px;
        border-radius: 12px;
        border: 1px solid #e2e8f0;
        width: 100%;
        box-sizing: border-box;
    }

    .messageIcon {
        font-size: 16px;
    }

    .buttonGroup {
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }

    .acceptButton,
    .rejectButton {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 14px 20px;
        border-radius: 12px;
        font-family: 'Poppins', sans-serif;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .acceptButton {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        border: none;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    .acceptButton:hover:not(:disabled) {
        background: linear-gradient(135deg, #059669, #047857);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
    }

    .acceptButton:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .rejectButton {
        background: white;
        color: #ef4444;
        border: 2px solid #ef4444;
        box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
    }

    .rejectButton:hover:not(:disabled) {
        background: #fef2f2;
        color: #dc2626;
        border-color: #dc2626;
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(239, 68, 68, 0.2);
    }

    .rejectButton:disabled {
        background: #f9fafb;
        color: #9ca3af;
        border-color: #e5e7eb;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .buttonIcon {
        font-size: 16px;
    }

    /* Completed Actions */
    .completedActions {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        width: 100%;
        margin-top: 8px;
    }

    .completedMessage {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #065f46;
        text-align: center;
        background: #f0fdf4;
        padding: 12px;
        border-radius: 12px;
        border: 1px solid #10b981;
        width: 100%;
        box-sizing: border-box;
    }

    /* Inactive Actions */
    .inactiveActions {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        margin-top: 8px;
    }

    .inactiveMessage {
        display: flex;
        align-items: center;
        gap: 8px;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-weight: 600;
        color: #6b7280;
        text-align: center;
        background: #f9fafb;
        padding: 12px;
        border-radius: 12px;
        border: 1px solid #e5e7eb;
        width: 100%;
        box-sizing: border-box;
    }

    /* Secondary Button */
    .secondaryButton {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        padding: 14px 20px;
        border-radius: 12px;
        font-family: 'Poppins', sans-serif;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        background: white;
        color: #374151;
        border: 2px solid #e5e7eb;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .secondaryButton:hover:not(:disabled) {
        background: #f9fafb;
        border-color: #d1d5db;
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .secondaryButton:disabled {
        background: #f9fafb;
        color: #9ca3af;
        border-color: #e5e7eb;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
}

/* Additional Mobile Responsive Styles */
@media screen and (max-width: 480px) {
    .history_container {
        width: 100%;
        padding: 16px;
        margin-bottom: 12px;
    }

    .orderLabel {
        font-size: 15px;
    }

    .tradeAmountHighlight {
        font-size: 16px;
        padding: 10px 14px;
    }

    .tradeDetails,
    .paymentMethods {
        padding: 14px;
    }

    .actionButton {
        padding: 12px 16px;
        font-size: 15px;
    }
}

@media screen and (max-width: 360px) {
    .history_container {
        width: 95%;
        padding: 14px;
    }

    .orderLabel {
        font-size: 14px;
    }

    .tradeAmountHighlight {
        font-size: 15px;
        padding: 8px 12px;
    }

    .detailRow,
    .paymentMethod {
        font-size: 12px;
    }

    .actionButton {
        font-size: 14px;
    }
}

/* Accessibility and Animation Preferences */
@media (prefers-reduced-motion: reduce) {
    .history_container,
    .actionButton {
        transition: none;
        animation: none;
    }

    .history_container:hover,
    .actionButton:hover {
        transform: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .history_container {
        border: 2px solid #000;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }

    .history_container::before {
        background: #000;
    }

    .orderLabel {
        color: #000;
    }

    .statusBadge {
        border: 1px solid #000;
    }

    .actionButton {
        background: #000;
        border: 1px solid #000;
    }
}

/* Legacy styles - keeping for backward compatibility */
.tradeNavBtn {
    display: none;
}

.allWrapper,
.oneToThreeWrapper,
.oneWrapper,
.twoWrapper,
.threeWrapper,
.fourWrapper,
.cardMid,
.tradeReqDialogue {
    display: none;
}