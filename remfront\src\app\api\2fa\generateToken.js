import axios from "axios";
require("dotenv").config();

const BaseUrl = process.env.NEXT_PUBLIC_Base_URL;
const URL = `${BaseUrl}/generate-2fa-token/`;
const verifyURL = `${BaseUrl}/submit-2fa-token/`;
const verifyURLAccess = `${BaseUrl}/verify-2fa-token/`;
// const URL = "https://dev.remflow.net/remflow/generate-2fa-token/";
// const verifyURL = "https://dev.remflow.net/remflow/verify-2fa-token/";

export const generateQRToken = async () => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }

  const res = await axios({
    url: URL,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "POST",
    // data: Data,
  });
  return res;
};

export const verifyQRToken = async (OTP) => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }

  const res = await axios({
    url: verifyURL,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    method: "POST",
    data: {
      otp: OTP,
    },
  });
  return res;
};
export const submitQRToken = async (OTP, Email) => {
  if (typeof window !== "undefined") {
    var token = sessionStorage.getItem("user");
  }

  const res = await axios({
    url: verifyURLAccess,
    // headers: {
    //   Authorization: `Bearer ${token}`,
    // },
    method: "POST",
    data: {
      otp: OTP,
      email: Email,
    },
  });
  return res;
};
