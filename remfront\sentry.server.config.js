// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from "@sentry/nextjs";

let lastLoggedTime = 0; // Track last event timestamp
const THROTTLE_TIME = 15000; // 15 seconds in milliseconds

Sentry.init({
  dsn: "https://<EMAIL>/4507785423224832",

  // Adjust this value in production, or use tracesSampler for greater control
  tracesSampleRate: 1,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,

  // Rate limiting for events
  beforeSend(event) {
    const now = Date.now();
    if (now - lastLoggedTime < THROTTLE_TIME) {
      return null; // Skip logging if within 15 seconds
    }
    lastLoggedTime = now;
    return event;
  },

  // Uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: process.env.NODE_ENV === 'development',
});

// // This file configures the initialization of Sentry on the server.
// // The config you add here will be used whenever the server handles a request.
// // https://docs.sentry.io/platforms/javascript/guides/nextjs/

// import * as Sentry from "@sentry/nextjs";

// Sentry.init({
//   dsn: "https://<EMAIL>/4507785423224832",

//   // Adjust this value in production, or use tracesSampler for greater control
//   tracesSampleRate: 1,

//   // Setting this option to true will print useful information to the console while you're setting up Sentry.
//   debug: false,

//   // Uncomment the line below to enable Spotlight (https://spotlightjs.com)
//   // spotlight: process.env.NODE_ENV === 'development',

// });
