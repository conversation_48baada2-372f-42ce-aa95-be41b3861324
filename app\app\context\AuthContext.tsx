import React, {
  createContext,
  useRef,
  useEffect,
  useContext,
  useState,
} from "react";
import useWebSocket, {
  ReadyState,
  Options,
  SendMessage,
} from "react-use-websocket";
import refreshTokenApi from "../api/onboarding/refreshTokenEndpoint";
import { usePathname } from "expo-router";
import * as SecureStore from "expo-secure-store";

interface AppContextType {
  connection: string | null;
  sendMessage: SendMessage;
  messageHistory: unknown[];
  recentMessage: unknown;
  lastJsonMessage: unknown;
  token: string | null;
}

export const AppContext = createContext<AppContextType | undefined>(undefined);

interface ContextStoreProps {
  children: React.ReactNode;
}

export const ContextStore: React.FC<ContextStoreProps> = ({ children }) => {
  const pathname = usePathname();
  const connection = useRef<string | null>(null);
  const [shouldReconnect, setShouldReconnect] = useState(true);
  const [reconnectAttempts, setReconnectAttempts] = useState(0);
  const [recentMessage, setRecentMessage] = useState<unknown>(null);
  const [messageHistory, setMessageHistory] = useState<unknown[]>([]);
  const [token, setToken] = useState<string | null>(null);

  const shouldConnectWebSocket = pathname !== "/" && pathname !== "/register";

  const options: Options = {
    onOpen: () => {
      console.log("Connected!!");
      setShouldReconnect(true);
    },
    onClose: () => {
      console.log("Disconnected!");
      setShouldReconnect(true);
    },
    // onError: (error) => {
    //   console.error("WebSocket error:", error);
    //   setShouldReconnect(true);
    // },
    onError: (error: any) => {
      console.error("WebSocket error:", error);
      console.error("WebSocket error details:", {
        isTrusted: error.isTrusted,
        message: error.message,
        type: error.type,
        target: error.target,
      });
      setShouldReconnect(true);
    },
    shouldReconnect: () => shouldReconnect,
    onMessage: (message) => {
      setRecentMessage(message);
    },
  };

  useEffect(() => {
    const getToken = async () => {
      const userToken = SecureStore.getItem("user");
      setToken(userToken);
      console.log("userToken-logged", userToken);
    };
    getToken();
  }, []);

  const { sendMessage, lastMessage, readyState, lastJsonMessage } =
    useWebSocket(
      shouldConnectWebSocket && token
        ? `wss://dev.remflow.net/ws/transaction/?token=${token}&app=true`
        : null,
      options
    );
  console.log("lastJsonMessageA", lastJsonMessage);
  console.log("lastMessageA", lastMessage);
  useEffect(() => {
    if (lastJsonMessage !== null) {
      setMessageHistory((prev) => [...prev, lastJsonMessage]);
    }
  }, [lastJsonMessage]);

  useEffect(() => {
    if (
      (readyState === ReadyState.CLOSED || readyState === ReadyState.CLOSING) &&
      shouldConnectWebSocket
    ) {
      console.log("Attempting to reconnect...");

      if (reconnectAttempts >= 3) {
        console.log("Max reconnection attempts reached. Stopping reconnection.");
        setShouldReconnect(false);
        return;
      }

      const timeout = Math.min(1000 * 2 ** reconnectAttempts, 30000);
      const timeoutId = setTimeout(() => {
        if (shouldConnectWebSocket) {
          console.log("Reconnecting now...");
          setShouldReconnect(true);
          setReconnectAttempts((prev) => prev + 1);
        }
      }, timeout);

      return () => clearTimeout(timeoutId);
    }
  }, [readyState, reconnectAttempts, shouldConnectWebSocket]);

  useEffect(() => {
    if (readyState === ReadyState.OPEN) {
      setReconnectAttempts(0);
    }
  }, [readyState]);

  connection.current =
    {
      [ReadyState.CONNECTING]: "Connecting",
      [ReadyState.OPEN]: "Open",
      [ReadyState.CLOSING]: "Closing",
      [ReadyState.CLOSED]: "Closed",
      [ReadyState.UNINSTANTIATED]: "Uninstantiated",
    }[readyState] || null;

  return (
    <AppContext.Provider
      value={{
        connection: connection.current,
        sendMessage,
        messageHistory,
        recentMessage,
        lastJsonMessage,
        token
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

export const useWebsocketContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useWebsocketContext must be used within a ContextStore");
  }
  return context;
};
