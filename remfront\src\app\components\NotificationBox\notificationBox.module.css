.rightContainerBell {
    margin-right: 50px;
    display: flex;
    justify-content: center;
    position: relative;
}

@media screen and (max-width: 576px) {
    .rightContainerBell {
        margin-top: 4px;
        margin-right: 9px;
    }
}

.icon_button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    color: #333333;
    background-color: #f0f0f0;
    border: none;
    outline: none;
    border-radius: 50%;
    cursor: pointer;
    transition: background-color 0.2s ease;
    @media screen and (max-width: 576px) {
        width: 35px;
        height: 35px;
    }
}

.icon_button:hover {
    background-color: #e0e0e0;
}

.icon_button__badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 22px;
    height: 22px;
    background: #dc3545;
    color: #ffffff;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    font-size: 12px;
    font-weight: 500;
}

.notificationLists {
    position: absolute;
    max-height: 450px;
    width: 380px;
    background-color: #f8f9fa;
    top: 60px;
    right: 0px;
    border-radius: 12px;
    z-index: 1000;
    opacity: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

@media screen and (max-width: 576px) {
    .notificationLists {
        z-index: 99999;
        width: 90vw;
        right: -16px;
    }
}

/* width */
.notificationLists::-webkit-scrollbar {
    width: 6px;
}

/* Track */
.notificationLists::-webkit-scrollbar-track {
    background: transparent;
    margin: 8px 0;
}

/* Handle */
.notificationLists::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 6px;
}

/* Handle on hover */
.notificationLists::-webkit-scrollbar-thumb:hover {
    background: #aaa;
}

.clearNotificationsCont {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 4px 8px 12px;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 8px;
}

.clearNotificationsBtn {
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    color: #6c5ce7;
    background-color: transparent;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.clearNotificationsBtn:hover {
    background-color: #e0e0e0;
}

.noNotifications {
    text-align: center;
    padding: 40px 20px;
    color: #888;
}

.remove {
    display: none;
}