import { useState, useEffect } from "react";
import {
  View,
  Text,
  Alert,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Pressable,
  TextInput,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { styled } from "nativewind";
import Layout from "@/components/Layout";
import RequestWithdrawModal from "../components/RequestWithdrawModal";
import AddWalletModal from "../components/AddWalletModal";
import TransactionHistoryModal from "../components/TransactionHistoryModal";
import customFetchWithToken from "./utils/axiosInterceptor";
import SavedWalletModal from "../components/savedWalletModal";
import { AxiosError } from "axios";
import { showToastError, showToastSuccess } from "@/hooks/toast";
import * as Clipboard from "expo-clipboard";
import Feather from "@expo/vector-icons/Feather";

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

interface walletBalance {
  asset: string;
  free: string;
  locked: string;
}

export default function RemflowFunds() {
  const wallet = [
    {
      wallet_address: "******************************************",
      network: "USDT",
    },
    {
      wallet_address: "******************************************",
      network: "USDT",
    },
    {
      wallet_address: "******************************************",
      network: "USDT",
    },
  ];

  const [copiedText, setCopiedText] = useState("");
  const [isModalVisible, setModalVisible] = useState(false);
  const [isSavedWalletModalVisible, setIsSavedWalletModalVisible] =
    useState(false);
  const [isAddWalletModalVisible, setIsAddWalletModalVisible] = useState(false);
  const [isTransactionVisible, setIsTransactionVisible] = useState(false);

  const [showTranArray, setShowTranArray] = useState("");
  const [walletAddressTRX, setWalletAddressTRX] = useState("");
  const [walletAddressMATIC, setWalletAddressMATIC] = useState("");
  const [balance, setBalance] = useState<walletBalance[]>([]);

  const copyToClipboard = async (network: string) => {
    if (network === "MATIC") {
      await Clipboard.setStringAsync(walletAddressMATIC);
      showToastSuccess("wallet address copied to clipboard");
    }
    if (network === "TRX") {
      await Clipboard.setStringAsync(walletAddressTRX);
      showToastSuccess("wallet address copied to clipboard");
    }
  };

  const toggleModal = () => {
    setModalVisible(!isModalVisible);
  };
  const toggleSavedWalletModal = () => {
    setModalVisible(false);
    setIsSavedWalletModalVisible(!isSavedWalletModalVisible);
  };

  // const handleAddWallet = (network: string, walletAddress: string) => {
  //   console.log("Network:", network);
  //   console.log("Wallet Address:", walletAddress);
  //   // Perform further actions like API calls to save wallet details
  // };

  const ActionButton = ({
    title,
    bgColor,
    onPress,
  }: {
    title: string;
    bgColor: string;
    onPress(): void;
  }) => (
    <StyledTouchableOpacity
      className={`w-full p-4 mb-2 rounded-md ${bgColor}`}
      onPress={onPress}
    >
      <StyledText className="text-center font-medium">{title}</StyledText>
    </StyledTouchableOpacity>
  );

  const fetchTransactionHistory = async () => {
    try {
      const res = await customFetchWithToken.get("/deposit-history/");
      console.log("history", res);

      setShowTranArray(res.data.data.wallet_address);
    } catch (error: any) {
      setShowTranArray(error.response.data.message);
      console.log(error);
    }
  };

  const createWalletAddress = async (network: string) => {
    const X_MBX_APIKEY = process.env.EXPO_PUBLIC_XMBXAPIKEY;
    const headers = {
      "X-MBX-APIKEY": X_MBX_APIKEY,
    };
    try {
      const res = await customFetchWithToken.get(
        `/create-wallet/?coin=USDT&network=${network}`,
        { headers }
      );
      console.log("wallet", res.data.message);
      showToastSuccess(res.data.message);
    } catch (error: any) {
      console.log(error);
    }
  };

  const fetchWalletAddress = async () => {
    try {
      const res = await customFetchWithToken.get("/get-binace-walletlist/");
      console.log("wallet", res.data.data[1]);

      setWalletAddressMATIC(res.data.data[0][0].address);
      setWalletAddressTRX(res.data.data[1][0].address);
    } catch (error) {
      console.log(error);
    }
  };

  const getWalletBalance = async () => {
    try {
      const res = await customFetchWithToken.get("/get-wallet-balance/");
      console.log("bal", res.data.data);
      setBalance(res.data.data);
    } catch (error: any) {
      console.log(error);
      showToastError(error.response.data.message);
    }
  };
  useEffect(() => {
    fetchTransactionHistory();

    fetchWalletAddress();
    getWalletBalance();
  }, []);

  return (
    <Layout>
      <ScrollView className="flex-1 bg-white p-4">
        <StyledView className="space-y-2">
          {/* Action Buttons */}
          <ActionButton
            title="Request Withdraw"
            bgColor="bg-green-100"
            onPress={() => setModalVisible(true)}
          />
          <ActionButton
            title="Transaction History"
            bgColor="bg-green-100"
            onPress={() => setIsTransactionVisible(true)}
          />
          <ActionButton
            title="Saved Wallet"
            bgColor="bg-yellow-100"
            onPress={() => setIsSavedWalletModalVisible(true)}
          />
          <ActionButton
            title="Add Wallet"
            bgColor="bg-yellow-100"
            onPress={() => setIsAddWalletModalVisible(true)}
          />

          {/* Modal */}
          <RequestWithdrawModal
            isVisible={isModalVisible}
            onClose={toggleModal}
          />
          <SavedWalletModal
            isVisible={isSavedWalletModalVisible}
            onClose={toggleSavedWalletModal}
          />
          {/* <SavedWalletModal
            isVisible={isSavedWalletModalVisible}
            onClose={toggleSavedWalletModal}
            savedWallets={wallet} // Ensure an appropriate array is passed
          /> */}
          <AddWalletModal
            isVisible={isAddWalletModalVisible}
            onClose={() => setIsAddWalletModalVisible(false)}
          />
          <TransactionHistoryModal
            isVisible={isTransactionVisible}
            onClose={() => setIsTransactionVisible(false)}
            data={showTranArray}
          />

          {/* Balance Section */}
          <StyledView className="mt-8 mb-6">
            <StyledText className="text-2xl font-bold text-center mb-4">
              Balance
            </StyledText>

            <StyledView className="space-y-2">
              <StyledText className="text-center text-lg">
                USDT: {balance ? Number(balance?.[0]?.free).toFixed(2) : "0.00"}
              </StyledText>
              {/* <StyledText className="text-center text-lg">
                TRX: {balance ? balance?.[1]?.free : "0.00000000"}
              </StyledText> */}
            </StyledView>
          </StyledView>

          {/* Info Text */}
          <StyledText className="text-center text-sm mb-6 px-4">
            In order to trade on remflow you need to make a deposit equivalent
            to 100% of the trade value. We will deduct from your Remflow wallet
            balance when the trade is completed
          </StyledText>

          {/* Network Selection */}
          <StyledView className="">
            <StyledText className="text-center font-medium mb-2 text-lg">
              ERC Wallet Address
            </StyledText>

            <View>
              {walletAddressMATIC.length > 0 ? (
                <Text className="text-center text-md">
                  {walletAddressMATIC}{" "}
                  <Pressable onPress={() => copyToClipboard("MATIC")}>
                    <Feather name="copy" size={24} color="black" />
                  </Pressable>
                </Text>
              ) : (
                <TouchableOpacity
                  className="flex justify-center items-center bg-blue-700 py-4 px-3 rounded-lg"
                  onPress={() => createWalletAddress("MATIC")}
                >
                  <Text className="text-white font-medium text-lg">
                    Get ERC wallet address
                  </Text>
                </TouchableOpacity>
              )}
            </View>

            {/* <StyledView className="border border-yellow-400 rounded-md overflow-hidden">
              <Picker
                selectedValue={selectedNetwork}
                onValueChange={(itemValue) => setSelectedNetwork(itemValue)}
                className="bg-white"
              >
                <Picker.Item label="Select Network" value="" />
                {Array.isArray(networkList) &&
                  networkList.map((el: Networklist, index: number) => (
                    <Picker.Item
                      key={index}
                      label={el.network}
                      value={el.network}
                    />
                  ))}
              
              </Picker>
            </StyledView> */}
          </StyledView>
          <StyledView className="mt-6">
            <StyledText className="text-center font-medium mb-2 text-lg">
              Tron Wallet Address
            </StyledText>

            <View>
              {walletAddressTRX.length > 0 ? (
                <Text className="text-center text-md">
                  {walletAddressTRX}{" "}
                  <Pressable onPress={() => copyToClipboard("TRX")}>
                    <Feather name="copy" size={24} color="black" />
                  </Pressable>
                </Text>
              ) : (
                <TouchableOpacity
                  className="flex justify-center items-center bg-blue-700 py-4 px-3 rounded-lg"
                  onPress={() => createWalletAddress("TRX")}
                >
                  <Text className="text-white font-medium text-lg">
                    Get TRC wallet address
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </StyledView>

          {/* Create Wallet Button */}
          {/* <StyledTouchableOpacity
            className="bg-blue-600 p-4 rounded-md mt-4"
            onPress={createWalletAddress}
          >
            <StyledText className="text-white text-center font-medium">
              Create Wallet Address
            </StyledText>
          </StyledTouchableOpacity> */}
        </StyledView>
      </ScrollView>
    </Layout>
  );
}
