"use client";
import Image from "next/image";
import { useEffect, useState } from "react";
import styles from "./reportTrade.module.css";
import tick from "../../../../public/assets/tick.png";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

const Page = ({ title, orderNumber, onClose, isOpen }) => {
  const [uploadMark, setUploadMark] = useState("");
  const [comment, setComment] = useState("");
  const [uploadEvidence, setUploadEvidence] = useState("");
  const [queryTitle, setQueryTitle] = useState("-1");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleUploadMark = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error("File size should be less than 5MB");
        return;
      }
      setUploadEvidence(file);
      setUploadMark(file);
    }
  };

  const validateFields = () => {
    if (queryTitle === "-1") {
      toast.error("Please select a reason");
      return false;
    }
    if (!uploadEvidence) {
      toast.error("Please upload evidence");
      return false;
    }
    if (!comment.trim()) {
      toast.error("Please add a comment");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateFields()) return;

    setIsSubmitting(true);
    try {
      const formData = new FormData();
      formData.append("disput_title", queryTitle);
      formData.append("upload_evidence", uploadEvidence);
      formData.append("comments", comment.trim());
      formData.append("order_number", orderNumber);

      const response = await customFetchWithToken.post("/dispute-list/", formData);
      toast.success("Dispute submitted successfully");
      onClose();
      
      // Reset form
      setUploadMark("");
      setComment("");
      setUploadEvidence("");
      setQueryTitle("-1");
    } catch (err) {
      console.error(err);
      toast.error("Failed to submit dispute");
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape" && isOpen) onClose();
    };
    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div 
      className={styles.modalOverlay}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="modal-title"
    >
      <div 
        className={styles.modalContainer}
        onClick={e => e.stopPropagation()}
      >
        <div className={styles.modalHeader}>
          <h2 id="modal-title" className={styles.disputeActionHearder}>
            {title}
          </h2>
          <button
            className={styles.closeButton}
            onClick={onClose}
            aria-label="Close"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit} className={styles.modalContent}>
          <div className={styles.formGroup}>
            <label htmlFor="order">Order number</label>
            <input
              id="order"
              value={orderNumber}
              readOnly
              aria-label="Order number"
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="reason">Reason</label>
            <select
              id="reason"
              className={styles.selectInput}
              value={queryTitle}
              onChange={(e) => setQueryTitle(e.target.value)}
              required
            >
              <option value="-1">Select reason for dispute</option>
              <option value="1">Funds not received on time</option>
              <option value="2">Third Party payment</option>
              <option value="3">Adjust amount (underpaid)</option>
              <option value="4">Suspicious behaviour</option>
              <option value="5">Other</option>
            </select>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="evidence">Evidence</label>
            <div className={styles.uploadContainer}>
              <input
                type="file"
                id="evidence"
                onChange={handleUploadMark}
                className={styles.fileInput}
                accept="image/*,.pdf"
                required
              />
              <div className={styles.uploadStatus} aria-live="polite">
                {uploadMark ? (
                  <>
                    <Image
                      src={tick}
                      width={14}
                      height={14}
                      alt=""
                      role="presentation"
                    />
                    <span>File uploaded</span>
                  </>
                ) : (
                  "Choose file"
                )}
              </div>
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="comments">Comments</label>
            <textarea
              id="comments"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              className={styles.textarea}
              placeholder="Describe your issue..."
              maxLength={200}
              required
            />
          </div>

          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit Report"}
          </button>
        </form>
      </div>
    </div>
  );
};

export default Page;
