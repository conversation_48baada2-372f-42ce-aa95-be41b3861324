@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}


/* 
  here disputes */
.diputeBoxContainer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin-top: 20px;
}

.diputeBoxContainerForCard {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    margin-top: 20px;
}

.disputeHeader {
    color: #000;
    font-family: Poppins;
    font-size: 20px;
    font-style: normal;
    font-weight: 400;
    display: flex;
    justify-content: flex-end;


    /* 55% */
}

.disputeShowBtn {
    margin: 0px 10px;
    width: 130px;
    padding: 12px 0px;
    cursor: pointer;
    font-weight: 600;
    font-family: poppins;
    background-color: #e2cfea;
    border: none;
    border-radius: 5px;
}

.addDisputeBtn {
    margin: 0px 10px;
    width: 150px;
    padding: 12px 0px;
    cursor: pointer;
    font-weight: 600;
    font-family: poppins;
    background-color: #471ca8;
    border: none;
    border-radius: 5px;
    color: #ebebeb;
}

.disputeShowBtn:active {
    scale: 0.9;
}

.addDisputeBtn:active {
    scale: 0.9;
}

.disputeBoxWrapper {
    display: flex;
    width: 100%;
    padding: 25px 5px;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 20px;
    border-radius: 15px;
    background: #FFF;

    @media screen and (max-width : 576px) {
        width: 94%;
    }
}

.disputeBoxWrapper1 {
    display: flex;
    margin: auto;
    width: 60%;
    padding: 25px 20px;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
    border-radius: 15px;
    background: #FFF;

    @media screen and (max-width : 576px) {
        width: 94%;
        padding: 25px 0px;
    }
}

.disputeBox1 {
    display: flex;
    width: 100%;
    height: auto;
    padding: 25px 20px;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    border-radius: 15px;
    border: 1px solid #D9D9D9;
    background: #FFF;

    @media screen and (max-width : 576px) {
        padding: 5px
    }
}

.svgRotate {
    transition: 1s linear;
    rotate: 180;
}

.disputeReason {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;

}

/* dropdown */


.disputeActionDrop {
    width: 100%;
    height: auto;
    background-color: #9b9c90;
    margin: 10px 0px;
    padding: 11px 15px;
    border-radius: 5px;
    background: #F9F9F9;
    transition: all 1s linear;
    outline: none;

    @media screen and (max-width : 576px) {
        margin: 10px 0px;
    }
}

/* dropdown */


.disputeAction {
    width: 95%;
    height: auto;
    background-color: #9b9c90;
    margin: 10px 0px;
    padding: 11px 15px;
    border-radius: 5px;
    background: #F9F9F9;
    transition: all 1s linear;

    @media screen and (max-width : 576px) {
        margin: 10px 0px;
    }
}

.disputeActionHearderArea {
    width: 94%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: auto;
}

.disputeActionHearder {
    color: #4153ED;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;

}

.disputeActionSymbol {}


.disputeActionBox {
    width: 95%;
    background-color: #fff;
    height: 90%;
    margin: auto;
    transition: all 1s linear;
}

.disputeActionBoxWrapper {
    padding: 20px;
    transition: all 1s linear;
}

.orderDetails {
    display: flex;
    flex-direction: column;
}

.orderDetails label {
    color: #000;
    font-family: Montserrat;
    font-size: 12px;
    font-weight: 400;
    margin-bottom: 10px;
}

.orderDetails input {
    height: 30px;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #EFEFEF;
}

.uploadDetails {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.uploadDetailsCage {
    width: 96%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #EFEFEF;
}

.uploadDetails label {
    color: #000;
    font-family: Montserrat;
    font-size: 12px;
    font-weight: 400;
    margin: 10px 0px;
}


.orderDetails input::placeholder {
    color: #969696;
    font-family: Poppins;
    font-size: 10px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
}

.uploadInput {
    display: flex;
    justify-content: space-between;
}

.uploadBtn {
    padding: 19px 10px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #000;
    text-align: center;
    font-family: Montserrat;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    border-radius: 0px 5px 5px 0px;
    border-left: 1px solid #EBEBEB;
}

.messageSupportBtn {
    margin: 15px 0px;
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    border: 1px solid #000;
    background: #FFF;
    color: #000;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;

}

.commentArea {}

.commentTextArea {
    width: 95%;
    padding: 10px 15px;
    border-radius: 5px;
    border: 1px solid #EFEFEF;
    background: #FFF;
}

.commentTextArea::placeholder {
    color: #969696;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
}


.disputeBox2 {
    display: flex;
    width: 94%;
    padding: 11px 15px;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px;
    background: #F9F9F9;
}

.disputeBox3 {
    display: flex;
    width: 94%;
    padding: 11px 15px;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px;
    background: #F9F9F9;
}

/* 
  here disputes */
.firstformWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.secondformWrapper {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    margin-top: 30px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.thirdformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 30px;

    @media screen and (max-width: 576px) {
        flex-direction: column;
    }
}

.firstName {
    width: 46%;
    height: 40px;

    @media screen and (max-width: 576px) {
        width: 100%;
        margin: 40px 0px;
    }
}

.firstNameLabel {
    font-size: 12px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
        margin-left: 17px;
    }
}

.sec_firstName {
    width: 29%;
    height: 40px;

    @media screen and (max-width: 576px) {
        width: 100%;
        margin-bottom: 50px;
    }
}

.firstNameLabel {
    font-size: 12px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
    }
}

.firstNameInput {
    display: flex;

    @media screen and (max-width: 576px) {
        padding: 15px;
    }
}

.firstNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
    height: 40px;

    @media screen and (max-width: 576px) {
        margin-bottom: 5px;
        padding: none;
    }
}

.firstNameInput select {
    border: none;
    background-color: #f9f9f9;
    width: 100%;
    padding-left: 10px;
    height: 40px;
    padding-right: 10px;
}

.emailBtn {
    border-radius: 0px 2px 2px 0px;
    background: #f5f5f5;
    border: 1px solid #ebebeb;
    border-left: 2px solid #c4c3c3;
    font-size: 10px;
    font-weight: 600;
    width: 57px;
}

.addressName {
    width: 100%;
    margin-left: 23px;
}

.addressNameInput input {
    border: none;
    background-color: #f9f9f9;
    width: 96%;

    height: 40px;
    padding-left: 10px;
    padding-right: 10px;

    @media screen and (max-width: 576px) {
        width: 87%;

    }
}

.fourthformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 55px;
}

.paymentBoxContainer {
    width: 60%;
    height: auto;
    border-radius: 15px;
    border: 1px solid #d9d9d9;
    background: #fff;
    margin: auto;

}

.paymentBox {
    background-color: #4f535a;
    width: 100%;
    /* padding: 25px 20px; */

}

.paymentHeader {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding: 0px 15px;
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
}

.paymentDirection {
    padding: 0px 15px;
    /* background-color: #4153ed; */
    color: #000;
    text-align: center;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    font-weight: 300;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payFrom {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 50%;
    border-bottom: 1px solid black;
}

.payTo {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    width: 50%;
    border-bottom: 1px solid #efefef;
}

.fifthformWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-x: auto;
    margin-bottom: 20px;
    /* margin-top: 55px; */
}

.paymentGateways {
    width: 100px;
    height: 100px;
    background-color: #F9F9F9;
    margin: 20px 10px 10px 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.BitcoinpaymentGateways {
    width: 100px;
    height: 100px;
    background-color: #F9F9F9;
    margin: 20px 10px 10px 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.listing_BtnCont {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}

.listing_Btn {
    width: 365px;
    height: 50px;
    border-radius: 5px;
    border: 2px solid #4153ED;
    background: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #4153ED;
    font-family: Poppins;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;

    @media screen and (max-width: 576px) {
        margin-bottom: 30px;

    }
}

.otherSection {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.otherSection textarea {
    width: 400px;
    margin: auto;
    height: 120px;
}

.otherSection button {
    margin-top: 10px;
    margin: 10px 5px;
    color: #f5f5f5;
    background-color: #00010a;
    width: 200px;
    height: 60px;
    border-radius: 5px;
    border: none;
    outline: none;
    font: bold;
    font-family: poppins;
    cursor: pointer;
}

.otherSection button:active {
    scale: 0.9;
}


.disputeDropdown {
    display: flex;
    width: 100%;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;

    .disputeAction {
        width: 95%;
        height: auto;
        background-color: #9b9c90;
        margin: 10px 0px;
        padding: 11px 15px;
        border-radius: 5px;
        background: #F9F9F9;
        transition: all 1s linear;

        @media screen and (max-width : 576px) {
            margin: 10px 0px;
        }
    }

    .disputeActionHearderArea {
        width: 94%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: auto;
    }

    .disputeActionHearder {
        color: #4153ED;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;

    }

    .disputeActionSymbol {}


    .disputeActionBox {
        width: 95%;
        background-color: #fff;
        height: 90%;
        margin: auto;
        transition: all 1s linear;
    }

    .disputeActionBoxWrapper {
        padding: 20px;
        transition: all 1s linear;
        font-family: poppins;
    }

    .orderDetails {
        display: flex;
        flex-direction: column;
    }

    .orderDetails label {
        color: #000;
        font-family: poppins;
        font-size: 12px;
        font-weight: 400;
        margin-bottom: 10px;
    }

    .orderDetails select {
        height: 40px;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #EFEFEF;
    }

    .orderDetails input {
        height: 30px;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #EFEFEF;
    }

    .uploadDetails {
        width: 100%;
        display: flex;
        flex-direction: column;
        font-family: poppins;
    }

    .uploadDetailsCage {
        width: 96%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 30px;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #EFEFEF;
        margin-bottom: 15px;
    }

    .uploadDetails label {
        color: #000;
        font-family: poppins;
        font-size: 12px;
        font-weight: 400;
        margin: 10px 0px;
    }


    .orderDetails input::placeholder {
        color: #969696;
        font-family: Poppins;
        font-size: 10px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
    }

    .uploadInput {
        display: flex;
        justify-content: space-between;
    }

    .uploadBtn {
        padding: 19px 10px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: #000;
        text-align: center;
        font-family: poppins;
        font-size: 12px;
        font-style: bold;
        font-weight: 400;
        border-radius: 0px 5px 5px 0px;
        border-left: 1px solid #EBEBEB;
        cursor: pointer;
    }

    .uploadBtn:active {
        scale: 1.2;
    }

    .messageSupportBtn {
        margin: 15px 0px;
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        border: 1px solid #000;
        background: #FFF;
        color: #000;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        cursor: pointer;

    }

    .messageSupportBtn:active {
        scale: 0.9;
    }

    .messageSubmitBtn:active {
        scale: 0.9;
    }

    .messageSubmitBtn {
        margin: 15px 0px;
        width: 100%;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 5px;
        border: 1px solid #000;
        background: #292929;
        color: #ffffff;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        cursor: pointer;
    }

    .commentArea {}

    .commentTextArea {
        width: 95%;
        padding: 10px 15px;
        border-radius: 5px;
        border: 1px solid #EFEFEF;
        background: #FFF;
    }

    .commentTextArea::placeholder {
        color: #969696;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
    }


    .disputeBox2 {
        display: flex;
        width: 94%;
        padding: 11px 15px;
        justify-content: space-between;
        align-items: center;
        border-radius: 5px;
        background: #F9F9F9;
    }

    .disputeBox3 {
        display: flex;
        width: 94%;
        padding: 11px 15px;
        justify-content: space-between;
        align-items: center;
        border-radius: 5px;
        background: #F9F9F9;
    }

    /* 
      here disputes */
    .firstformWrapper {
        width: 100%;
        display: flex;
        justify-content: space-around;
        margin-bottom: 20px;

        @media screen and (max-width: 576px) {
            flex-direction: column;
        }
    }

    .secondformWrapper {
        width: 100%;
        display: flex;
        justify-content: space-around;
        margin-bottom: 20px;
        margin-top: 30px;

        @media screen and (max-width: 576px) {
            flex-direction: column;
        }
    }

    .thirdformWrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        margin-top: 30px;

        @media screen and (max-width: 576px) {
            flex-direction: column;
        }
    }

    .firstName {
        width: 46%;
        height: 40px;

        @media screen and (max-width: 576px) {
            width: 100%;
            margin: 40px 0px;
        }
    }

    .firstNameLabel {
        font-size: 12px;

        @media screen and (max-width: 576px) {
            margin-bottom: 5px;
            margin-left: 17px;
        }
    }

    .sec_firstName {
        width: 29%;
        height: 40px;

        @media screen and (max-width: 576px) {
            width: 100%;
            margin-bottom: 50px;
        }
    }

    .firstNameLabel {
        font-size: 12px;

        @media screen and (max-width: 576px) {
            margin-bottom: 5px;
        }
    }

    .firstNameInput {
        display: flex;

        @media screen and (max-width: 576px) {
            padding: 15px;
        }
    }

    .firstNameInput input {
        border: none;
        background-color: #f9f9f9;
        width: 100%;
        padding-left: 10px;
        padding-right: 10px;
        height: 40px;

        @media screen and (max-width: 576px) {
            margin-bottom: 5px;
            padding: none;
        }
    }

    .firstNameInput select {
        border: none;
        background-color: #f9f9f9;
        width: 100%;
        padding-left: 10px;
        height: 40px;
        padding-right: 10px;
    }

    .emailBtn {
        border-radius: 0px 2px 2px 0px;
        background: #f5f5f5;
        border: 1px solid #ebebeb;
        border-left: 2px solid #c4c3c3;
        font-size: 10px;
        font-weight: 600;
        width: 57px;
    }

    .addressName {
        width: 100%;
        margin-left: 23px;
    }

    .addressNameInput input {
        border: none;
        background-color: #f9f9f9;
        width: 96%;

        height: 40px;
        padding-left: 10px;
        padding-right: 10px;

        @media screen and (max-width: 576px) {
            width: 87%;

        }
    }

    .fourthformWrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 55px;
    }

    .paymentBoxContainer {
        width: 60%;
        height: auto;
        border-radius: 15px;
        border: 1px solid #d9d9d9;
        background: #fff;
        margin: auto;

    }

    .paymentBox {
        background-color: #4f535a;
        width: 100%;
        /* padding: 25px 20px; */

    }

    .paymentHeader {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        padding: 0px 15px;
        color: #000;
        font-family: Poppins;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
    }

    .paymentDirection {
        padding: 0px 15px;
        /* background-color: #4153ed; */
        color: #000;
        text-align: center;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 300;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .payFrom {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        width: 50%;
        border-bottom: 1px solid black;
    }

    .payTo {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 30px;
        width: 50%;
        border-bottom: 1px solid #efefef;
    }

    .fifthformWrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow-x: auto;
        margin-bottom: 20px;
        /* margin-top: 55px; */
    }

    .paymentGateways {
        width: 100px;
        height: 100px;
        background-color: #F9F9F9;
        margin: 20px 10px 10px 10px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .BitcoinpaymentGateways {
        width: 100px;
        height: 100px;
        background-color: #F9F9F9;
        margin: 20px 10px 10px 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .listing_BtnCont {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 30px;
    }

    .listing_Btn {
        width: 365px;
        height: 50px;
        border-radius: 5px;
        border: 2px solid #4153ED;
        background: #FFF;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #4153ED;
        font-family: Poppins;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        cursor: pointer;

        @media screen and (max-width: 576px) {
            margin-bottom: 30px;

        }
    }
}

.disputBtns {
    margin: 10px 5px;
    color: #f5f5f5;
    background-color: #4153ED;
    width: 200px;
    height: 60px;
    border-radius: 5px;
    border: none;
    outline: none;
    font: bold;
    font-family: poppins;
    cursor: pointer;
}

.disputBtns:active {
    scale: 0.9;

}

.payMthodSelect {
    height: 40px;
    font-family: poppins;
}

.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  z-index: 1000;
}

.modalContainer {
  background: #ffffff;
  border-radius: 8px;
  width: 100%;
  max-width: 420px;
  max-height: calc(100vh - 32px);
  position: relative;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  animation: modalFadeIn 0.2s ease-out;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  flex-shrink: 0;
}

.disputeActionHearder {
  color: #1a1a1a;
  font-family: Poppins, sans-serif;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.closeButton {
  background: transparent;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666666;
  font-size: 20px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background-color: #f5f5f5;
  color: #1a1a1a;
}

.modalContent {
  padding: 16px;
  overflow-y: auto;
}

.formGroup {
  margin-bottom: 16px;
}

.formGroup:last-child {
  margin-bottom: 0;
}

.formGroup label {
  display: block;
  margin-bottom: 4px;
  color: #1a1a1a;
  font-family: Poppins, sans-serif;
  font-size: 13px;
  font-weight: 500;
}

.formGroup input,
.formGroup select,
.formGroup textarea,
.fileInput {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-family: Poppins, sans-serif;
  font-size: 13px;
  color: #1a1a1a;
  background-color: #ffffff;
  transition: all 0.2s ease;
  height: 36px;
  box-sizing: border-box;
}

.formGroup textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-family: Poppins, sans-serif;
  font-size: 13px;
  color: #1a1a1a;
  background-color: #ffffff;
  transition: all 0.2s ease;
  height: 80px;
  resize: none;
}

.formGroup input:hover,
.formGroup select:hover,
.formGroup textarea:hover,
.fileInput:hover {
  border-color: #b0b0b0;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus,
.fileInput:focus {
  outline: none;
  border-color: #4153ED;
  box-shadow: 0 0 0 2px rgba(65, 83, 237, 0.12);
}

.selectInput {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23666666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 14px;
  padding-right: 36px !important;
}

.uploadContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.fileInput {
  border-style: dashed;
  background-color: #fafafa;
  cursor: pointer;
}

.fileInput:hover {
  border-color: #4153ED;
  background-color: #f8f9ff;
}

.uploadStatus {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
  font-size: 13px;
  color: #666666;
}

.uploadStatus img {
  width: 14px;
  height: 14px;
  object-fit: contain;
}

.submitButton {
  width: 100%;
  padding: 8px 16px;
  background-color: #4153ED;
  color: #ffffff;
  border: none;
  border-radius: 6px;
  font-family: Poppins, sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  height: 36px;
  margin-top: 16px;
}

.submitButton:hover:not(:disabled) {
  background-color: #3444d9;
}

.submitButton:disabled {
  background-color: #e0e0e0;
  cursor: not-allowed;
}

@media screen and (max-width: 576px) {
  .modalContainer {
    max-height: calc(100vh - 24px);
  }

  .modalHeader {
    padding: 12px;
  }

  .modalContent {
    padding: 12px;
  }

  .disputeActionHearder {
    font-size: 15px;
  }

  .formGroup {
    margin-bottom: 12px;
  }
}

/* Accessibility enhancements */
@media (prefers-reduced-motion: reduce) {
  .modalContainer,
  .submitButton,
  .formGroup input,
  .formGroup select,
  .formGroup textarea {
    transition: none;
  }
}

/* High contrast mode support */
@media (forced-colors: active) {
  .submitButton {
    border: 2px solid ButtonText;
  }
  
  .submitButton:disabled {
    opacity: 0.5;
  }
}