@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

.mainContainer {
  width: 100%;
  height: 100vh;
  display: flex;

  @media screen and (max-width: 576px) {
    flex-direction: column-reverse;
    height: auto;
  }
}

.leftContainer {
  width: 120%;
  display: flex;

  @media screen and (max-width: 577px) {
    width: 100%;
  }
}

.topContainer {
  @media screen and (max-width: 576px) {
    width: 100%;
    display: block;
    position: relative;
  }

  @media screen and (min-width: 577px) {
    display: none;
  }
}

.leftWrapper {
  padding: 30px;
  width: 100%;

  display: flex;
  flex-direction: column;
}

.backBtn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 50px;
  margin-bottom: 20px;
}

.backimg {
  margin-right: 10px;
  cursor: pointer;
}

.mainButtonsWrapper {
  display: flex;
  width: 100%;
  margin-bottom: 25px;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.personalBtn {
  color: #4153ed;
  border: 1px solid #4153ed;
  border-radius: 5px;
  padding: 8px 10px 8px 10px;
  margin-right: 8px;
}

.employBtn {
  color: #000;
  border: 1px solid black;
  border-radius: 5px;
  padding: 8px 10px 8px 10px;
}

.heading {
  color: #4153ed;
  font-family: Poppins;
  font-size: 22px;
  font-style: normal;
  font-weight: 600;
  margin-bottom: 30px;
  line-height: 11px;
}

.Subheading {
  color: #000;
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 16px;
  /* 114.286% */
  margin-bottom: 30px;
}

.formWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  @media screen and (max-width: 576px) {
    display: flex;
    flex-direction: column;
    margin-bottom: 0px;
  }
}

.firstName {
  width: 92%;

  @media screen and (max-width: 576px) {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
  }
}

.firstNameContactWrapper {
  width: 100%;
}

.addressName {
  width: 100%;
}

.lastName {
  width: 92%;
}

.firstNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width: 576px) {
    width: 90%;
    height: 40px;
    display: flex;
    flex-direction: column;
  }
}

.firstNameInputC input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width: 576px) {
    width: 100%;
    display: flex;
    flex-direction: column;
  }
}

.firstNameInput select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.lastNameInput input {
  border: none;
}

.firstNameInput {
  width: 92%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;

  @media screen and (max-width: 576px) {
    width: 100%;


  }
}

.firstNameInputC {
  width: 96%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;

  @media screen and (max-width: 576px) {
    width: 100%;
    margin-bottom: 10px;

  }
}

.lastNameInput {
  width: 92%;
  border: 1px solid black;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
}

.emailBtn {
  border-radius: 0px 2px 2px 0px;
  background: #f5f5f5;
  border: 1px solid #ebebeb;
  border-left: 2px solid #c4c3c3;
}

.calender {
  display: flex;
  justify-content: center;
  align-items: center;
}

.addressNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 94%;
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
}

/* submitBtn */

.submitBtnCont {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-bottom: 20px;
}

.twoFactorContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0px 0px 0px;
  flex-direction: column;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    width: 100%;

  }
}

.submitBtn {
  margin-top: 20px;
  background-color: #fff;
  padding: 15px 50px;
  gap: 10px;
  color: #4153ed;
  border-color: #4153ed;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
}

/* submitBtn */

/* walletBoxes */

.walletAddressContainer {
  width: 91%;
  margin: 30px 0;
  padding: 20px 20px;
  border-radius: 10px;
  background: #f8f9fb;
}

.walletHeading {
  color: #000;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  margin-bottom: 10px;
}

.walletWrapper {
  display: flex;
  justify-content: space-between;
}

.walletBalance {
  width: 31%;
  height: 45px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.balHeading {
  color: #877d7d;
  font-family: Poppins;
  font-size: 10px;
  font-style: normal;
  font-weight: 300;
  margin-left: 10px;
}

.bal {
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  margin-left: 10px;
}

/* address */
.wAddHeading {
  color: #877d7d;
  font-family: Poppins;
  font-size: 10px;
  font-style: normal;
  font-weight: 300;
  margin-left: 10px;
}

.wAddress {
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
  margin-left: 10px;
}

/* address */
.walletAdd {
  width: 62%;
  height: 45px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.dialogue {
  color: #000;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 300;
}

/* walletBoxes */

/* buttons */

.buttonsContainer {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
}

.despositBtn {
  padding: 15px 30px;
  margin-bottom: 15px;
  border-radius: 5px;
  border: 1px solid #4153ed;
  background: #fff;
  color: #4153ed;
  font-family: Poppins;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  cursor: pointer;
}

.verificationBtn {
  padding: 15px 83px;
  margin-bottom: 15px;
  border-radius: 5px;
  background: #4153ed;
  color: #fff;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  cursor: pointer;
}

.qrContainet {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* buttons */

.rightContainer {
  width: 80%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fb;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.verifyInput {
  padding: 5px 10px;
  margin-right: 10px;
  font-family: poppins;
  margin-bottom: 10px;

  @media screen and (max-width: 576px) {
    width: 100%;
    margin-bottom: 20px;
    margin-right: 0px
  }
}

.cancelBtn {
  padding: 5px 18px;
  font-family: poppins;
  font-weight: 600;
  border-radius: 2px;
  border: none;
  background-color: #a6a7ad;
  color: #ebebeb;
  cursor: pointer;
  margin: 0px 5px;

  @media screen and (max-width: 576px) {

    width: 100%;
  }
}

.verifyBtn {
  padding: 5px 18px;
  font-family: poppins;
  font-weight: 600;
  border-radius: 2px;
  border: none;
  background-color: #4153ed;
  color: #ebebeb;
  cursor: pointer;
  margin: 0px 5px;

  @media screen and (max-width: 576px) {

    width: 100%;
  }
}