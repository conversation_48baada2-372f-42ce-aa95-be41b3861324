import axios from "axios";
require("dotenv").config();

const smtpURL = process.env.NEXT_PUBLIC_SMTPURL;
const ConfirmEmailOtpUrl = process.env.NEXT_PUBLIC_CONFIRM_EMAIL_OTP;

export const smtpApi = async (Data) => {
  const response = await axios({
    url: smtpURL,
    method: "POST",
    data: {
      email: Data,
    },
  });
  return response;
};

//confirmEmail

export const ConfirmEmailOtp = async (Data, OTP) => {
  const response = await axios({
    url: ConfirmEmailOtpUrl,
    method: "POST",
    data: {
      email: Data,
      otp: Number(OTP),
    },
  });
  return response;
};
