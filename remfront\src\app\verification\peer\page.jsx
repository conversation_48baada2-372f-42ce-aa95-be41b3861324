"use client";
import { useState } from "react";
import Image from "next/image";
import styles from "./peer.module.css";
import leftArrow from "../../../../public/assets/personalDPage/back.png";
import calender from "../../../../public/assets/personalDPage/calender.png";
import rightImg from "../../../../public/mainRight.png";
import mobileAsset from "../../../../public/assets/mobile_main.png";
import Login from "@/app/sign/login/page";
import { userVerificationPeerApi } from "@/app/api/UserRegistration/userRegistartion";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import QRCodeSVG from "qrcode.react";
import { generateQRToken, verifyQRToken } from "@/app/api/2fa/generateToken";

const PeerRegistration = () => {
  const router = useRouter();
  const [secOtp, setSecOtp] = useState("");
  const [secret, setSecret] = useState("");
  const [contact, setContact] = useState("");
  const [telegramId, setTelegramId] = useState("");
  const [whatsappId, setWhatsappId] = useState("");
  const [weChatId, setWeChatId] = useState("");
  const [verifyOtp, setVerifyOtp] = useState("");
  const [otherMesesgingId, setOtherMesesgingId] = useState("");
  const [qrBtn, setQrBtn] = useState("2FA authentication setup");
  const issuer = "remflow";

  let token;
  let userEmail;
  if (typeof window !== "undefined") {
    userEmail = localStorage.getItem("userEmail");
    token = sessionStorage.getItem("user");
  }

  if (!token) {
    router.push("/sign/login");
  }

  const generateQRTokenFn = async (e) => {
    e.preventDefault();
    try {
      const res = await generateQRToken();
      setSecret(res.data.base32);
      // setQrBtn("Regenerate QR Code");
    } catch (error) {
      console.error(error);
    }
  };
  const cancel2fa = () => {
    setSecret("");
  };

  const otpAuthUrl = `otpauth://totp/${issuer}:${encodeURIComponent(
    userEmail
  )}?secret=${secret}&issuer=${encodeURIComponent(issuer)}`;

  const verifyQRTokenFn = async (e) => {
    e.preventDefault();
    try {
      const res = await verifyQRToken(verifyOtp);

      if (res.status === 200) {
        toast.success("2FA authencation successfull");
      }
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    }
  };

  //another 👇

  const Data = {
    telegram_id: telegramId,
    whatsapp_id: whatsappId,
    wechat_id: weChatId,
    any_other_id: otherMesesgingId,
  };

  const handleVerification = async () => {
    if (secret && !verifyOtp) {
      toast.error("VerifyOTP first");
      return;
    }
    toast.warn("Sending");

    try {
      const res = await userVerificationPeerApi(Data);

      toast.success(res.data.message);
      setTimeout(function () {
        router.push("/pages/searchads");
      }, 1500);
    } catch (error) {
      toast.error(error.response.data.message);
      console.error(error);
      setTimeout(function () {
        router.push("/pages/searchads");
      }, 1500);
    }
  };

  return (
    <>
      <div className={styles.mainContainer}>
        <div className={styles.topContainer}>
          <Image className={styles.backimg} src={mobileAsset} alt="backPNG" />
        </div>
        <div className={styles.leftContainer}>
          <div className={styles.leftWrapper}>
            <div
              className={styles.backBtn}
              onClick={() => router.push("/verification/status")}
            >
              <Image
                className={styles.backimg}
                src={leftArrow}
                width={26}
                height={26}
                alt="backPNG"
              />
              <div>Back</div>
            </div>
            <div className={styles.mainButtonsWrapper}></div>
            <div className={styles.heading}>Peer Registration</div>
            <div className={styles.Subheading}>
              Two Factor authentication required
            </div>
            <div className={styles.formContainer}>
              <form action="">
                {/* names */}
                <div className={styles.formWrapper}>
                  <div className={styles.firstName}>
                    <div className={styles.firstNameLabel}>Telegram ID</div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="text"
                        id="firstname"
                        maxLength={260}
                        onChange={(e) => setTelegramId(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className={styles.firstName}>
                    <div className={styles.firstNameLabel}>Whatsapp ID</div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="text"
                        id="lastname"
                        maxLength={260}
                        onChange={(e) => setWhatsappId(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
                {/* names */}
                {/* names */}
                <div className={styles.formWrapper}>
                  <div className={styles.firstName}>
                    <div className={styles.firstNameLabel}>WeChat ID</div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="text"
                        id="firstname"
                        maxLength={260}
                        onChange={(e) => setWeChatId(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className={styles.firstName}>
                    <div className={styles.firstNameLabel}>
                      Any other messaging app ID
                    </div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="text"
                        id="lastname"
                        maxLength={260}
                        onChange={(e) => setOtherMesesgingId(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
                {/* names */}

                <div className={styles.submitBtnCont}>
                  {!secret ? (
                    <button
                      className={styles.submitBtn}
                      onClick={generateQRTokenFn}
                    >
                      {qrBtn}
                    </button>
                  ) : (
                    ""
                  )}
                  <p>
                    {secret
                      ? "Scan the code with your authencator app to setup 2FA"
                      : ""}
                  </p>
                  <div></div>

                  {secret.length > 1 ? (
                    <div className={styles.qrContainet}>
                      <QRCodeSVG value={otpAuthUrl} size={100} />
                      <div className={styles.twoFactorContainer}>
                        <input
                          type="text"
                          maxLength={260}
                          className={styles.verifyInput}
                          placeholder="Enter Genearted Code"
                          onChange={(e) => setVerifyOtp(e.target.value)}
                        />
                        <div>
                          <button
                            className={styles.cancelBtn}
                            onClick={cancel2fa}
                          >
                            Cancel
                          </button>
                          <button
                            className={styles.verifyBtn}
                            onClick={verifyQRTokenFn}
                          >
                            Verify code
                          </button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    ""
                  )}
                </div>
              </form>
              {/* <div className={styles.walletAddressContainer}>
                <div className={styles.walletHeading}>Wallet:</div>
                <div className={styles.walletWrapper}>
                  <div className={styles.walletBalance}>
                    <div className={styles.balHeading}>Total Balance:</div>
                    <div className={styles.bal}>100 USDT</div>
                  </div>
                  <div className={styles.walletAdd}>
                    <div className={styles.wAddHeading}>
                      Remflow user wallet address:
                    </div>
                    <div className={styles.wAddress}>0x5A21232d</div>
                  </div>
                </div>
                <div>
                  <p className={styles.dialogue}>
                    Deposit 1% of AD max liquidity (i.e. peer has prepaid his TX
                    fees to Remflow)
                  </p>
                </div>
              </div> */}
            </div>
            <div className={styles.buttonsContainer}>
              {/* <div className={styles.despositBtn}>
                Deposit Crypto to wallet balance
              </div> */}
              <div
                className={styles.verificationBtn}
                onClick={handleVerification}
              >
                Next
              </div>
            </div>
          </div>
        </div>
        <div className={styles.rightContainer}>
          <Image
            className={styles.backimg}
            src={rightImg}
            width={650}
            height={472}
            alt="backPNG"
          />
        </div>
      </div>
      <ToastContainer />
    </>
  );
};

export default PeerRegistration;
