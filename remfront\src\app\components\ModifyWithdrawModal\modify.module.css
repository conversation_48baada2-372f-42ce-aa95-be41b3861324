@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Modal Overlay */
.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Modal Container */
.modalContainer {
    width: 100%;
    max-width: 500px;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    animation: slideIn 0.3s ease-out;
    position: relative;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modalContainer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

/* Modal Header */
.modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0;
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    margin-bottom: 24px;
}

.modalTitle {
    font-size: 20px;
    font-weight: 700;
    color: #1e293b;
    margin: 0;
}

.closeButton {
    width: 36px;
    height: 36px;
    border: none;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #64748b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.closeButton:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

/* Modal Body */
.modalBody {
    padding: 0 24px 24px;
}

.formGroup {
    margin-bottom: 20px;
}

.label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    line-height: 1.4;
}

.required {
    color: #ef4444;
    margin-left: 2px;
}

.inputWrapper {
    position: relative;
}

.input {
    width: 100%;
    padding: 16px 48px 16px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
}

.input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background: white;
}

.input:hover {
    border-color: #cbd5e1;
}

.inputError {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.inputIcon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    pointer-events: none;
}

.errorMessage {
    display: block;
    color: #ef4444;
    font-size: 13px;
    margin-top: 6px;
    font-weight: 500;
}

/* Modal Footer */
.modalFooter {
    display: flex;
    gap: 12px;
    padding: 24px;
    background: rgba(248, 250, 252, 0.5);
    border-top: 1px solid rgba(226, 232, 240, 0.8);
}

.cancelButton {
    flex: 1;
    padding: 14px 24px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: white;
    color: #64748b;
    font-size: 16px;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cancelButton:hover {
    border-color: #cbd5e1;
    color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.saveButton {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 14px 24px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 700;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.saveButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.saveButton:hover::before {
    left: 100%;
}

.saveButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 25px rgba(79, 70, 229, 0.4);
}

.saveButton:active {
    transform: translateY(0);
}

.saveButton:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.saveButton:disabled:hover {
    transform: none;
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.3);
}

.buttonIcon {
    font-size: 16px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .modalOverlay {
        padding: 16px;
    }

    .modalContainer {
        max-width: 100%;
        border-radius: 16px;
    }

    .modalHeader {
        padding: 20px 20px 0;
    }

    .modalTitle {
        font-size: 18px;
    }

    .closeButton {
        width: 32px;
        height: 32px;
    }

    .modalBody {
        padding: 0 20px 20px;
    }

    .modalFooter {
        padding: 20px;
        flex-direction: column;
    }

    .input {
        padding: 14px 40px 14px 14px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .modalOverlay {
        padding: 12px;
    }

    .modalContainer {
        border-radius: 12px;
    }

    .modalHeader {
        padding: 16px 16px 0;
    }

    .modalTitle {
        font-size: 16px;
    }

    .modalBody {
        padding: 0 16px 16px;
    }

    .modalFooter {
        padding: 16px;
    }
}

/* Focus Styles for Accessibility */
.closeButton:focus-visible,
.cancelButton:focus-visible,
.saveButton:focus-visible,
.input:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .modalOverlay {
        background: rgba(0, 0, 0, 0.9);
    }

    .modalContainer {
        background: white;
        border: 3px solid #000;
    }

    .closeButton,
    .cancelButton,
    .input {
        border: 2px solid #000;
    }

    .saveButton {
        background: #000;
        border: 2px solid #000;
    }
}