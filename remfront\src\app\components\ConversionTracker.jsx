import { useEffect } from "react";

const ConversionTracker = () => {
  useEffect(() => {
    const runGtagEvent = () => {
      if (window.gtag) {
        window.gtag("event", "conversion", {
          send_to: "AW-921959853/Xuv8CKPh0GYQrfvPtwM",
        });
      } else {
        console.error("gtag is not defined");
      }
    };

    runGtagEvent();
  }, []);

  return null;
};

export default ConversionTracker;
