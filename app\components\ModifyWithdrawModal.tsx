import { useState } from "react";
import { View, Text, TextInput, TouchableOpacity, Modal } from "react-native";

import customFetchWithToken from "@/app/utils/axiosInterceptor";
import Toast from "react-native-toast-message";
import { toastConfig, showToastError, showToastSuccess } from "@/hooks/toast";

interface ModifyModalProps {
  id: string;
  showModal: boolean;
  setShowModal: (value: boolean) => void;
  amount: number;
  address: string;
  setEditSuccess: (value: boolean) => void;
}

const ModifyModal = ({
  id,
  showModal,
  setShowModal,
  amount,
  address,
  setEditSuccess,
}: ModifyModalProps) => {
  const [amount1, setAmount1] = useState(amount ? amount : "");
  const [address1, setAddress1] = useState(address ? address : "");

  const handleModify = async () => {
    try {
      const res = await customFetchWithToken.put(
        `/edit-withdraw-request/${id}`,
        {
          amount: amount1,
          external_wallet: address1,
        }
      );
      console.log("rest", res);
      Toast.show({
        type: "success",
        text1: res.data.message,
      });
      setEditSuccess(true);
      setShowModal(false);
    } catch (error) {
      console.log(error);
    }
  };

  const handleCloseModal = () => {
    setShowModal(false);
  };

  return (
    <Modal visible={showModal} transparent={false} animationType="slide">
      <View className="bg-white p-6 rounded-lg w-11/12">
        <Text className="text-xl font-bold text-center mb-6">
          Edit Withdrawal Request
        </Text>

        <View className="mb-4">
          <Text className="text-gray-700 mb-2">Amount</Text>
          <TextInput
            className="border border-gray-300 rounded-lg px-4 py-2"
            placeholder="Enter amount"
            value={amount1.toString()}
            onChangeText={setAmount1}
            keyboardType="numeric"
          />
        </View>

        <View className="mb-6">
          <Text className="text-gray-700 mb-2">Address</Text>
          <TextInput
            className="border border-gray-300 rounded-lg px-4 py-2"
            placeholder="Enter wallet address"
            value={address1}
            onChangeText={setAddress1}
          />
        </View>

        <View className="flex-row justify-between space-x-4">
          <TouchableOpacity
            className="flex-1 bg-blue-500 py-3 rounded-lg"
            onPress={handleModify}
          >
            <Text className="text-white text-center font-bold">Modify</Text>
          </TouchableOpacity>

          <TouchableOpacity
            className="flex-1 bg-gray-500 py-3 rounded-lg"
            onPress={handleCloseModal}
          >
            <Text className="text-white text-center font-bold">Close</Text>
          </TouchableOpacity>
        </View>
      </View>
      <Toast config={toastConfig} />
    </Modal>
  );
};

export default ModifyModal;
