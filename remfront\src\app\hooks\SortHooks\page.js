"use client";
import { useState } from "react";

const page = ({ searchResults, order }) => {
  const [sortedResults, setSortedResults] = useState(null);

  const sortByPrice = () => {
    const sorted = [...searchResults?.results].sort((a, b) => {
      const priceA = a.indicative_fx_rate;
      const priceB = b.indicative_fx_rate;

      if (order === "asc") {
        return priceA - priceB;
      } else {
        return priceB - priceA;
      }
    });

    setSortedResults(sorted);
  };

  const resetSorting = () => {
    setSortedResults(null);
  };

  return (
    <div>
      <button onClick={() => sortByPrice("asc")}>Sort Low to High</button>
      <button onClick={() => sortByPrice("desc")}>Sort High to Low</button>
      <button onClick={resetSorting}>Reset Sorting</button>

      <h2>Results:</h2>
      <ul>
      {(sortedResults || (searchResults && searchResults.results) || []).map((result) => (
        <li key={result.id}>
           ID: {result.id}, Price: {result.indicative_fx_rate}
        </li>
      ))}
      </ul>
    </div>
  );
};

export default page;
