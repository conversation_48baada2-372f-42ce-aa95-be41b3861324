import { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Modal,
  ActivityIndicator,
} from "react-native";
import { styled } from "nativewind";
import customFetchWithToken from "@/app/utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);

const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "#10B981", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{
        fontSize: 14,
        fontWeight: "600",
        marginLeft: 20,
      }}
    />
  ),
  error: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "#EF4444", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{
        fontSize: 14,
        fontWeight: "600",
        marginLeft: 20,
      }}
    />
  ),
};

const showToast = (type: "success" | "error", message: string) => {
  Toast.show({
    type,
    text1: message,
    position: "top",
    visibilityTime: 3000,
  });
};

interface ExternalWallet {
  network: string;
  wallet_address: string;
}

const SavedWalletModal = ({
  isVisible,
  onClose,
}: {
  isVisible: boolean;
  onClose: () => void;
}) => {
  const [savedWallets, setSavedWallets] = useState<ExternalWallet[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const shortenAddress = (address: string) =>
    `${address.slice(0, 6)}...${address.slice(-6)}`;

  const getExternalWallets = async () => {
    setIsLoading(true);
    try {
      const res = await customFetchWithToken.get("/get-external-wallet/");
      setSavedWallets(res.data.data);
    } catch (error) {
      showToast("error", "Failed to fetch wallets");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSavedWalletDelete = async (wallet_address: string) => {
    setIsDeleting(wallet_address);
    try {
      const res = await customFetchWithToken.delete(
        "/delete-external-wallet/",
        {
          data: { wallet_address },
        }
      );
      showToast("success", res.data.message);
      setSavedWallets((prev) =>
        prev.filter((wallet) => wallet.wallet_address !== wallet_address)
      );
    } catch (error: any) {
      showToast(
        "error",
        error.response?.data?.message || "Failed to delete wallet"
      );
    } finally {
      setIsDeleting(null);
    }
  };

  useEffect(() => {
    if (isVisible) {
      getExternalWallets();
    }
  }, [isVisible]);

  return (
    <Modal visible={isVisible} transparent animationType="slide">
      <StyledView className="flex-1 bg-black/50">
        <StyledView className="flex-1 justify-end">
          <StyledView className="bg-white rounded-t-3xl p-6 h-4/5">
            {/* Header */}
            <StyledView className="flex-row justify-between items-center mb-6">
              <StyledText className="text-2xl font-bold text-gray-800">
                Saved Wallets
              </StyledText>
              <TouchableOpacity
                onPress={onClose}
                className="p-2 rounded-full bg-gray-100"
              >
                <StyledText className="text-gray-600 font-semibold">
                  ✕
                </StyledText>
              </TouchableOpacity>
            </StyledView>

            {/* Content */}
            {isLoading ? (
              <StyledView className="flex-1 justify-center items-center">
                <ActivityIndicator size="large" color="#6366F1" />
              </StyledView>
            ) : savedWallets.length > 0 ? (
              <FlatList
                data={savedWallets}
                keyExtractor={(item) => item.wallet_address}
                className="flex-1"
                contentContainerStyle={{ paddingBottom: 20 }}
                renderItem={({ item }) => (
                  <StyledView className="bg-gray-50 rounded-xl p-4 mb-3">
                    <StyledView className="flex-row justify-between items-start">
                      <StyledView className="flex-1">
                        <StyledText className="text-sm text-gray-500 mb-1">
                          Wallet Address
                        </StyledText>
                        <StyledText className="text-base font-semibold text-gray-800 mb-2">
                          {shortenAddress(item.wallet_address)}
                        </StyledText>
                        <StyledView className="flex-row items-center">
                          <StyledView className="bg-indigo-100 rounded-full px-3 py-1">
                            <StyledText className="text-sm text-indigo-700 font-medium">
                              {item.network}
                            </StyledText>
                          </StyledView>
                        </StyledView>
                      </StyledView>
                      <StyledTouchableOpacity
                        onPress={() =>
                          handleSavedWalletDelete(item.wallet_address)
                        }
                        disabled={isDeleting === item.wallet_address}
                        className="ml-4"
                      >
                        {isDeleting === item.wallet_address ? (
                          <ActivityIndicator size="small" color="#EF4444" />
                        ) : (
                          <StyledView className="bg-red-100 p-2 rounded-full">
                            <StyledText className="text-red-600">🗑</StyledText>
                          </StyledView>
                        )}
                      </StyledTouchableOpacity>
                    </StyledView>
                  </StyledView>
                )}
              />
            ) : (
              <StyledView className="flex-1 justify-center items-center">
                <StyledText className="text-xl font-semibold text-gray-400 mb-2">
                  No Wallets Found
                </StyledText>
                <StyledText className="text-gray-400 text-center">
                  You haven't saved any wallets yet.
                </StyledText>
              </StyledView>
            )}
          </StyledView>
        </StyledView>
      </StyledView>
      <Toast config={toastConfig} />
    </Modal>
  );
};

export default SavedWalletModal;
