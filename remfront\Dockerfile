# Stage 1: Dependencies
FROM node:16-alpine AS deps
WORKDIR /app

# Install dependencies for node-gyp
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json package-lock.json ./

# Install dependencies
RUN npm ci

# Stage 2: Builder
FROM node:16-alpine AS builder
WORKDIR /app

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Set build-time arguments
ARG NEXT_PUBLIC_Base_URL
ARG NEXT_PUBLIC_RESETPASS_URL
ARG NEXT_PUBLIC_XMBXAPIKEY
ARG NEXT_PUBLIC_CONFIRM_EMAIL_OTP
ARG NEXT_PUBLIC_SMTPURL
ARG NEXT_PUBLIC_PHN_OTP_URL
ARG NEXT_PUBLIC_PHN_OTP_VERIFY__URL
ARG NEXT_PUBLIC_USER_REGISTRATION_URL
ARG NEXT_PUBLIC_ADD_LISTING_URL
ARG NEXT_PUBLIC_ClearDil_URL
ARG NEXT_PUBLIC_USER_STATUS_URL
ARG NEXT_PUBLIC_SITE_KEY

# Set environment variables
ENV NEXT_PUBLIC_Base_URL=$NEXT_PUBLIC_Base_URL
ENV NEXT_PUBLIC_RESETPASS_URL=$NEXT_PUBLIC_RESETPASS_URL
ENV NEXT_PUBLIC_XMBXAPIKEY=$NEXT_PUBLIC_XMBXAPIKEY
ENV NEXT_PUBLIC_CONFIRM_EMAIL_OTP=$NEXT_PUBLIC_CONFIRM_EMAIL_OTP
ENV NEXT_PUBLIC_SMTPURL=$NEXT_PUBLIC_SMTPURL
ENV NEXT_PUBLIC_PHN_OTP_URL=$NEXT_PUBLIC_PHN_OTP_URL
ENV NEXT_PUBLIC_PHN_OTP_VERIFY__URL=$NEXT_PUBLIC_PHN_OTP_VERIFY__URL
ENV NEXT_PUBLIC_USER_REGISTRATION_URL=$NEXT_PUBLIC_USER_REGISTRATION_URL
ENV NEXT_PUBLIC_ADD_LISTING_URL=$NEXT_PUBLIC_ADD_LISTING_URL
ENV NEXT_PUBLIC_ClearDil_URL=$NEXT_PUBLIC_ClearDil_URL
ENV NEXT_PUBLIC_USER_STATUS_URL=$NEXT_PUBLIC_USER_STATUS_URL
ENV NEXT_PUBLIC_SITE_KEY=$NEXT_PUBLIC_SITE_KEY

# Build application
RUN npm run build

# Stage 3: Runner
FROM node:16-alpine AS runner
WORKDIR /app

# Set to production
ENV NODE_ENV production

# Create non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy necessary files from builder
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/package.json ./package.json
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

# Set runtime environment variables
ENV NEXT_PUBLIC_Base_URL=$NEXT_PUBLIC_Base_URL
ENV NEXT_PUBLIC_RESETPASS_URL=$NEXT_PUBLIC_RESETPASS_URL
ENV NEXT_PUBLIC_XMBXAPIKEY=$NEXT_PUBLIC_XMBXAPIKEY
ENV NEXT_PUBLIC_CONFIRM_EMAIL_OTP=$NEXT_PUBLIC_CONFIRM_EMAIL_OTP
ENV NEXT_PUBLIC_SMTPURL=$NEXT_PUBLIC_SMTPURL
ENV NEXT_PUBLIC_PHN_OTP_URL=$NEXT_PUBLIC_PHN_OTP_URL
ENV NEXT_PUBLIC_PHN_OTP_VERIFY__URL=$NEXT_PUBLIC_PHN_OTP_VERIFY__URL
ENV NEXT_PUBLIC_USER_REGISTRATION_URL=$NEXT_PUBLIC_USER_REGISTRATION_URL
ENV NEXT_PUBLIC_ADD_LISTING_URL=$NEXT_PUBLIC_ADD_LISTING_URL
ENV NEXT_PUBLIC_ClearDil_URL=$NEXT_PUBLIC_ClearDil_URL
ENV NEXT_PUBLIC_USER_STATUS_URL=$NEXT_PUBLIC_USER_STATUS_URL
ENV NEXT_PUBLIC_SITE_KEY=$NEXT_PUBLIC_SITE_KEY

# Set user
USER nextjs

# Expose port
EXPOSE 3000

# Set environment variables
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Start the application
CMD ["node", "server.js"]
