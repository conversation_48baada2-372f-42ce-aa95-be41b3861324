@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

.main {
  background: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
  height: 103vh;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: 'Poppins';
}

.wrapper {
  display: flex;
  width: 100%;
  height: 90vh;
  padding: 30px 10px;
  margin: auto;
  justify-content: space-between;
}

.leftContainerWrapper {
  width: 20%;
  position: relative;
}

.leftContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  /* border: 0.5px solid black; */
}

/* need to remove later */

.logoArea {
  height: 20%;
  width: 100%;
  background-color: #4153ed;
  border-radius: 15px 15px 15px 0px;
  display: flex;
  flex-direction: column;

}

.logo {
  margin-top: 5px;

}

.profileBar {
  margin-top: 25px;
}

.profileBarContainer {
  background-color: #4f535a;
  width: 80%;
  height: 35px;
  margin: auto;
  border-radius: 100px;
  background: rgba(255, 255, 255, 0.17);
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.profileImg {
  margin-top: 4px;
  margin-left: 15px;
  margin-right: 5px;
}

.profileName {
  color: #fff;
  font-family: Poppins;
  font-size: 11px;
  font-weight: 500;
}

.profileDropDown {
  margin-left: auto;
  margin-right: 10px;
}

.logoContainer {
  /* width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: end; */
}

.logo {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.profileBar {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50%;
}

.sidebar {
  width: 100%;
  height: auto;
  border-radius: 15px;
  background: #fff;
}

.pages {
  margin-top: 30px;

  width: 100%;
  height: 100%;
}

.dashboardContainer {
  width: 100%;
  height: 47px;
  /* background-color: aqua; */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #4153ed;
  border-bottom: 1px solid #ececec;
}

.historyContainer {
  width: 100%;
  height: 47px;
  /* background-color: aqua; */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #4153ed;
}

.logoutContainer {
  /* margin-bottom: auto; */
  width: 100%;
  height: 47px;
  /* background-color: aqua; */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #4153ed;
  /* border-bottom: 1px solid #ECECEC; */
}

.sideIcons {
  margin-left: 20px;
  margin-right: 10px;
  color: #4153ed;
}

.dashboard {
  color: #4153ed;
}

.rightContainer {
  width: 78%;
  height: auto;
  display: flex;

  height: 100vh;
}

.rightContainerWrapper {
  width: 100%;
  color: #000;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
}

.rightContainerHeader {
  width: 100%;
  height: 94px;
  color: #000;
  font-family: Poppins;
  font-size: 24px;
  font-style: normal;
  font-weight: 500;
}

.rightContainerBody {
  height: 100%;
  padding: 30px;
  border-radius: 20px;
  background: #fff;
}

.body {
  height: 100%;
  background-color: #fff;
  width: 100%;
}

.firstformWrapper {
  width: 100%;
  display: flex;
  justify-content: space-around;
  margin-bottom: 20px;

  @media screen and (max-width: 576px) {
    flex-direction: column;
  }
}

.secondformWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  margin-top: 40px;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    margin-top: 0px;
    margin-bottom: 0px;
    justify-content: none;
  }
}

.thirdformWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  margin-top: 40px;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    margin-top: 0px;
    margin-bottom: 0px;
    justify-content: none;
  }
}

.firstName {
  width: 24%;
  height: 40px;

  @media screen and (max-width: 576px) {
    width: 100%;
  }
}

.firstNameLabel {
  font-size: 12px;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
  }
}

.sec_firstName {
  width: 49%;
  height: 40px;

  @media screen and (max-width: 576px) {
    width: 100%;
    height: 80px;
  }
}

.sec_firstName1 {
  width: 100%;
  height: 40px;

  @media screen and (max-width: 576px) {
    width: 100%;
    height: 80px;
  }
}

.modalHeaderCont .firstNameLabel {
  font-size: 12px;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInput {
  display: flex;
}

.firstNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
  height: 40px;

  @media screen and (max-width: 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInput select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  height: 40px;
  padding-right: 10px;
}

.emailBtn {
  border-radius: 0px 2px 2px 0px;
  background: #f5f5f5;
  border: 1px solid #ebebeb;
  border-left: 2px solid #c4c3c3;
  font-size: 10px;
  font-weight: 600;
  width: 57px;
}

.addressName {
  width: 100%;
  margin-left: 23px;
}

.addressNameInput input {
  border: none;
  background-color: #f9f9f9;
  width: 96%;

  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
}

.fourthformWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 55px;
}

.paymentBoxContainer {
  width: 60%;
  height: auto;
  border-radius: 15px;
  border: 1px solid #d9d9d9;
  background: #fff;
  margin: auto;
  margin-top: 20px !important;
}

.paymentBox {
  background-color: #4f535a;
  width: 100%;
  /* padding: 25px 20px; */
}

.paymentHeader {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding: 0px 15px;
  color: #000;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.payFrom {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 50%;
  border-bottom: 1px solid black;
}

.payTo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 30px;
  width: 50%;
  border-bottom: 1px solid #efefef;
}

.fifthformWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-x: auto;
  margin-bottom: 20px;
  /* margin-top: 55px; */
}

.paymentGateways {
  width: 188.204px;
  padding: 10px;
  height: 100px;
  background-color: #f9f9f9;
  margin: 20px 10px 10px 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  display: flex;
  flex-direction: column;
}

.info {
  color: #000;
  text-align: center;
  font-family: poppins;
  text-align: center;
  padding: 0px 40px;
  font-size: 10px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}

.BitcoinpaymentGateways {
  width: 100px;
  height: 100px;
  background-color: #f9f9f9;
  margin: 20px 10px 10px 10px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.listing_BtnCont {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 90px;

  @media screen and (max-width: 576px) {
    margin-top: 10px;
  }
}

.listing_Btn {
  width: 365px;
  height: 50px;
  border-radius: 5px;
  border: 2px solid #4153ed;
  background: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #4153ed;
  font-family: Poppins;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  cursor: pointer;
}

/* recommend box */
.recommend {
  margin-top: 300px;
}