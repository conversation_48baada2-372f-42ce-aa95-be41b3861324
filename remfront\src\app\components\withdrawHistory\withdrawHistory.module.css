@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Withdrawal History Card */
.withdrawHistoryCard {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 16px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    position: relative;
    overflow: hidden;
    width: 100%;
}

.withdrawHistoryCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

.withdrawHistoryCard:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: rgba(79, 70, 229, 0.3);
}

/* Card Header */
.cardHeader {
    margin-bottom: 20px;
}

.transactionId {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.idLabel {
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.actionButtons {
    display: flex;
    gap: 8px;
}

/* Action Buttons */
.editButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.editButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
}

.deleteButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.deleteButton:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

.deleteButton:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Spinner */
.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Card Body */
.cardBody {
    margin-top: 16px;
}

.infoGrid {
    display: grid;
    gap: 20px;
}

.infoItem {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.infoLabel {
    font-size: 12px;
    font-weight: 600;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.infoValue {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    color: #1e293b;
}

/* Amount Styling */
.amountValue {
    font-size: 20px;
    font-weight: 700;
    color: #059669;
}

.currency {
    font-size: 14px;
    font-weight: 600;
    color: #64748b;
    background: rgba(16, 185, 129, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
}

/* Status Badge */
.statusBadge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    text-transform: capitalize;
}

.statusSuccess {
    background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
    color: #166534;
    border: 1px solid #22c55e;
}

.statusPending {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border: 1px solid #f59e0b;
}

.statusFailed {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border: 1px solid #ef4444;
}

.statusIcon {
    font-size: 14px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Address Container */
.addressContainer {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(248, 250, 252, 0.8);
    padding: 8px 12px;
    border-radius: 10px;
    border: 1px solid rgba(226, 232, 240, 0.8);
}

.addressText {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    color: #475569;
    font-weight: 500;
}

.copyButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
}

.copyButton:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(99, 102, 241, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .withdrawHistoryCard {
        padding: 20px 16px;
        border-radius: 12px;
    }

    .transactionId {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .actionButtons {
        align-self: flex-end;
    }

    .infoGrid {
        gap: 16px;
    }

    .amountValue {
        font-size: 18px;
    }

    .addressContainer {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .copyButton {
        align-self: flex-end;
    }
}

@media (max-width: 480px) {
    .withdrawHistoryCard {
        padding: 16px 12px;
        margin-bottom: 12px;
    }

    .idLabel {
        font-size: 12px;
    }

    .editButton,
    .deleteButton {
        width: 32px;
        height: 32px;
    }

    .amountValue {
        font-size: 16px;
    }

    .statusBadge {
        font-size: 12px;
        padding: 4px 8px;
    }
}

/* Focus Styles for Accessibility */
.editButton:focus-visible,
.deleteButton:focus-visible,
.copyButton:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .withdrawHistoryCard {
        background: white;
        border: 2px solid #000;
    }

    .editButton,
    .deleteButton,
    .copyButton {
        border: 2px solid #000;
    }

    .statusSuccess,
    .statusPending,
    .statusFailed {
        border: 2px solid #000;
    }
}