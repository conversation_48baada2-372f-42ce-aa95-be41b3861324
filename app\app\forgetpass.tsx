import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import Toast, { BaseToast } from "react-native-toast-message";
import { useNavigation } from "@react-navigation/native";
import customFetchWithToken from "./utils/axiosInterceptor";

const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "green", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{
        fontSize: 14,
        fontWeight: "bold",
        marginLeft: 20,
      }}
    />
  ),
};

const showToastSuccess = (message: string) => {
  Toast.show({
    type: "success",
    text1: message,
    position: "top",
    visibilityTime: 4000,
  });
};

const showToastError = (message: string) => {
  Toast.show({
    type: "error",
    text1: message,
    position: "top",
    visibilityTime: 4000,
  });
};

const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

const URL = `${BaseURL}/password-reset/`;

export default function ForgotPassword() {
  const navigation = useNavigation();
  const [email, setEmail] = useState("");
  const Data = {
    email: email,
  };
  const handleForgotPassword = async () => {
    try {
      // Replace with your API call logic
      const response = await customFetchWithToken.post(
        "/password-reset/",
        Data
      );

      if (response.status == 200) {
        showToastSuccess("Password reset link sent successfully.");
      } else {
        showToastError("Failed to send password reset link.");
      }
    } catch (error) {
      showToastError("An error occurred. Please try again.");
    }
  };

  const fakeApiCall = (email: string) =>
    new Promise<{ success: boolean }>((resolve) =>
      setTimeout(() => resolve({ success: true }), 1000)
    );

  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1 }} className="bg-white">
      <View className="flex-1 justify-center items-center px-6">
        <Text className="text-4xl font-bold text-gray-800 mb-4">Remflow</Text>
        <Text className="text-2xl font-semibold text-gray-600 mb-4">
          Forgot Password
        </Text>
        <Text className="text-center text-gray-500 mb-6">
          Please enter your registered email address to reset your password.
        </Text>
        <TextInput
          className="border border-gray-300 rounded-lg px-4 py-2 w-full text-gray-800 mb-4"
          placeholder="Email Address"
          keyboardType="email-address"
          autoCapitalize="none"
          value={email}
          onChangeText={setEmail}
        />
        <TouchableOpacity
          onPress={handleForgotPassword}
          className="bg-blue-500 rounded-lg px-6 py-3 w-full"
        >
          <Text className="text-white text-center font-semibold">Next</Text>
        </TouchableOpacity>
        <Text className="text-center text-gray-500 mt-4">
          You will receive a link in your email to set a new password.
        </Text>
      </View>
      <Toast config={toastConfig} />
    </ScrollView>
  );
}
