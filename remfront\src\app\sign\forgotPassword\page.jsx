"use client";
import { useEffect, useState } from "react";
import Image from "next/image";
import styles from "./forgotpass.module.css";
import line from "../../../../public/line.svg";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { forgotPassApi } from "@/app/api/onboarding/forgotPassword";
import RightSideAnimation from "../../components/RightSideAnimation/RightSideAnimation";
require("dotenv").config();

const login = () => {
  const router = useRouter();

  const [email, setEmail] = useState("");

  const Data = {
    email: email,
  };

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

  const URL = `${BaseURL}/password-reset/`;

  const handleEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@.]/g, "");
    setEmail(inputValue);
  };

  const onSubmit = async (e) => {
    e.preventDefault();

    try {
      const res = await forgotPassApi(URL, Data);

      if (res.status === 200) {
        toast.success(res.data.message);
      } else {
        toast.error("Login failed.");
      }
    } catch (error) {
      toast.error("Incorrect Email ID or Password");
    }
  };

  return (
    <main className={styles.main}>
      <div className={styles.leftContainer}>
        <div className={styles.leftBody}>
          <div className={styles.logo}>Remflow</div>
          <h1 className={styles.heading}>Forgot Password</h1>
          <div className={styles.subHeading}>
            Please enter your registered email address to reset your password.
          </div>

          <form action="" onSubmit={onSubmit}>
            <div className={styles.emailContainer}>
              <div className={styles.email}>
                <label className={styles.nameLabel} htmlFor="email">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  maxLength={260}
                  value={email}
                  onChange={handleEmail}
                  required
                />
                <div className={styles.emailLink}>
                  You will receive a link in your email to set a new password.
                </div>
              </div>
            </div>

            <div className={styles.loginBtnContainer}>
              <button type="submit" className={styles.loginBtn}>
                Next
              </button>
            </div>
          </form>
          <ToastContainer />
        </div>
      </div>
      <RightSideAnimation />

    </main>
  );
};

export default login;
