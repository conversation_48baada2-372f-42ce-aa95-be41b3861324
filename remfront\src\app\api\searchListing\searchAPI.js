import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}
const searchApi = async (URL, Data) => {
  const response = await axios({
    url: URL,
    method: "GET",
    data: Data,
  });
  return response;
};

export default searchApi;

export const exchangeRateCurrencyApi = async (
  payIn,
  PayOut,
  amount,
  listing_id
) => {
  const res = await axios({
    url: `${Base_url}/current-currency-rate/?from_currency=${payIn}&to_currency=${PayOut}&amount=${amount}&listing_id=${listing_id}`,
    method: "GET",
  });
  return res;
};
export const exchangeRateCurrencyApi1 = async (
  payIn,
  PayOut,
  to_amount,
  listing_id
) => {
  const res = await axios({
    url: `${Base_url}/current-currency-rate/?from_currency=${payIn}&to_currency=${PayOut}&to_amount=${to_amount}&listing_id=${listing_id}`,
    method: "GET",
  });
  return res;
};

export const getRecipientAccount = async (payoutOption) => {
  const res = await axios({
    url: `${Base_url}/get-recipient-account/?payment_option=${payoutOption}`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
