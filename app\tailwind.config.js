/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./app/**/*.{js,jsx,ts,tsx}", "./components/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    extend: {
      fontFamily: {
        pblack: ["Poppins-Black", "sans-serif"],
        pbold: ["Poppins-Bold", "sans-serif"],
        pextraBold: ["Poppins-ExtraBold", "sans-serif"],
        plight: ["Poppins-Light", "sans-serif"],
        pmedium: ["Poppins-Medium", "sans-serif"],
        pregular: ["Poppins-Regular", "sans-serif"],
        psemitBold: ["Poppins-SemiBold", "sans-serif"],
        pthin: ["Poppins-Thin", "sans-serif"],
      },
    },
  },
  plugins: [],
};