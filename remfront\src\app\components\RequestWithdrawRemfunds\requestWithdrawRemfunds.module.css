@font-face {
    font-family: 'Poppins';
    font-weight: 300;
    src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

/* Modal Overlay and Container */
.modalOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(8px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.modalContainer {
    width: 100%;
    max-width: 800px;
    max-height: 90vh;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 24px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    overflow-y: auto;
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    animation: slideIn 0.3s ease-out;
    position: relative;
}

@keyframes slideIn {
    from {
        transform: translateY(-20px) scale(0.95);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.modalContainer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    border-radius: 24px 24px 0 0;
}

/* Modal Header */
.modalHeader {
    text-align: center;
    padding: 32px 32px 24px;
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
    position: relative;
}

.closeButton {
    position: absolute;
    top: 24px;
    right: 24px;
    width: 40px;
    height: 40px;
    border: none;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #64748b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(226, 232, 240, 0.6);
}

.closeButton:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

.closeButton:active {
    transform: scale(0.95);
}

.closeButton svg {
    width: 20px;
    height: 20px;
    stroke-width: 2.5;
}

.modalTitle {
    font-size: 28px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.modalSubtitle {
    font-size: 16px;
    color: #64748b;
    margin: 0;
    font-weight: 400;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .modalOverlay {
        padding: 16px;
    }

    .modalContainer {
        max-height: 95vh;
        border-radius: 20px;
    }

    .modalHeader {
        padding: 24px 24px 20px;
    }

    .closeButton {
        top: 20px;
        right: 20px;
        width: 36px;
        height: 36px;
    }

    .closeButton svg {
        width: 18px;
        height: 18px;
    }

    .modalTitle {
        font-size: 24px;
    }

    .modalSubtitle {
        font-size: 14px;
    }
}

/* Tab Navigation */
.tabContainer {
    display: flex;
    gap: 8px;
    padding: 24px 32px;
    background: rgba(248, 250, 252, 0.8);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

.tabActive {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-family: inherit;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    color: white;
    box-shadow: 0 8px 25px rgba(79, 70, 229, 0.4);
    transform: translateY(-2px);
}

.tabActive::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.tabActive:hover::before {
    left: 100%;
}

.tabInactive {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px 24px;
    border: none;
    border-radius: 12px;
    font-family: inherit;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.6);
    color: #64748b;
    border: 1px solid rgba(148, 163, 184, 0.2);
}

.tabInactive:hover {
    background: rgba(255, 255, 255, 0.8);
    color: #475569;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tabIcon {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

@media (max-width: 768px) {
    .tabContainer {
        flex-direction: column;
        gap: 12px;
        padding: 20px 24px;
    }
}

/* Form Container */
.formContainer {
    padding: 32px;
}

.formCard {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 32px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.formCard::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
}

.formHeader {
    text-align: center;
    margin-bottom: 32px;
}

.formTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.formDescription {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .formContainer {
        padding: 24px;
    }

    .formCard {
        padding: 24px 20px;
        border-radius: 16px;
    }

    .formTitle {
        font-size: 20px;
    }

    .formDescription {
        font-size: 13px;
    }
}

/* Form Elements */
.formGroup {
    margin-bottom: 24px;
}

.label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    line-height: 1.4;
}

.required {
    color: #ef4444;
    margin-left: 2px;
}

/* Select Styling */
.selectWrapper {
    position: relative;
    width: 100%;
}

.select {
    width: 100%;
    padding: 16px 48px 16px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    appearance: none;
    cursor: pointer;
    box-sizing: border-box;
}

.select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background: white;
}

.select:hover {
    border-color: #cbd5e1;
}

.selectError {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.selectIcon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    pointer-events: none;
    transition: transform 0.2s ease;
}

.select:focus + .selectIcon {
    transform: translateY(-50%) rotate(180deg);
}

/* Input Styling */
.inputWrapper {
    position: relative;
    width: 100%;
}

.input {
    width: 100%;
    padding: 16px 48px 16px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 16px;
    font-family: inherit;
    background: rgba(255, 255, 255, 0.9);
    color: #1e293b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
}

.input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
    background: white;
}

.input:hover {
    border-color: #cbd5e1;
}

.inputError {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.inputIcon {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    pointer-events: none;
}

/* Fee Notice */
.feeNotice {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border: 1px solid #f59e0b;
    border-radius: 12px;
    margin-bottom: 24px;
}

.feeIcon {
    font-size: 20px;
    flex-shrink: 0;
}

.feeText {
    font-size: 14px;
    color: #92400e;
    line-height: 1.4;
}

/* Error Messages */
.errorMessage {
    display: block;
    color: #ef4444;
    font-size: 13px;
    margin-top: 6px;
    font-weight: 500;
}

/* Help Text */
.helpText {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    font-size: 13px;
    color: #64748b;
}

.linkButton {
    background: none;
    border: none;
    color: #4f46e5;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
    font-family: inherit;
    transition: color 0.2s ease;
}

.linkButton:hover {
    color: #3730a3;
}

/* Form Actions */
.formActions {
    margin-top: 32px;
    text-align: center;
}

.submitButton {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 18px 36px;
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    color: white;
    border: none;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 700;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 200px;
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
}

.submitButton::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.submitButton:hover::before {
    left: 100%;
}

.submitButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(79, 70, 229, 0.4);
}

.submitButton:active {
    transform: translateY(0);
}

.submitButton:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.submitButton:disabled:hover {
    transform: none;
    box-shadow: 0 10px 25px rgba(79, 70, 229, 0.3);
}

.buttonIcon {
    font-size: 18px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.spinner {
    width: 18px;
    height: 18px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.submitHelp {
    font-size: 13px;
    color: #64748b;
    margin: 12px 0 0 0;
    line-height: 1.4;
}

/* History Container */
.historyContainer {
    padding: 32px;
}

.historyHeader {
    text-align: center;
    margin-bottom: 24px;
    padding: 24px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
}

.historyTitle {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin: 0 0 8px 0;
}

.historyDescription {
    font-size: 14px;
    color: #64748b;
    margin: 0;
    line-height: 1.5;
}

.historyContent {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 16px;
    padding: 24px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
}

.historyList {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Empty State */
.emptyState {
    text-align: center;
    padding: 48px 24px;
}

.emptyIcon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.emptyTitle {
    font-size: 20px;
    font-weight: 600;
    color: #374151;
    margin: 0 0 8px 0;
}

.emptyDescription {
    font-size: 14px;
    color: #6b7280;
    margin: 0 0 24px 0;
    line-height: 1.5;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.emptyAction {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.emptyAction:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4);
}

@media (max-width: 768px) {
    .historyHeader,
    .historyContent {
        padding: 20px 16px;
        border-radius: 16px;
    }

    .historyTitle {
        font-size: 20px;
    }

    .emptyState {
        padding: 32px 16px;
    }

    .emptyIcon {
        font-size: 48px;
    }
}

/* Responsive Design */
@media (max-width: 480px) {
    .modalOverlay {
        padding: 12px;
    }

    .modalContainer {
        border-radius: 16px;
    }

    .modalTitle {
        font-size: 20px;
    }

    .modalSubtitle {
        font-size: 13px;
    }

    .modalHeader {
        padding: 20px 20px 16px;
    }

    .formContainer,
    .historyContainer {
        padding: 20px;
    }

    .formCard,
    .historyHeader,
    .historyContent {
        padding: 20px 16px;
        border-radius: 12px;
    }

    .formTitle,
    .historyTitle {
        font-size: 18px;
    }

    .tabActive,
    .tabInactive {
        padding: 14px 20px;
        font-size: 14px;
    }

    .tabIcon {
        font-size: 16px;
    }

    .select,
    .input {
        padding: 14px 40px 14px 14px;
        font-size: 15px;
    }

    .submitButton {
        padding: 16px 28px;
        font-size: 15px;
        min-width: 180px;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .modalOverlay,
    .modalContainer,
    .tabActive,
    .tabInactive,
    .select,
    .input,
    .submitButton,
    .spinner {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Better Accessibility */
.closeButton:focus-visible,
.tabActive:focus-visible,
.tabInactive:focus-visible,
.select:focus-visible,
.input:focus-visible,
.submitButton:focus-visible,
.linkButton:focus-visible,
.emptyAction:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .modalOverlay {
        background: rgba(0, 0, 0, 0.9);
    }

    .modalContainer {
        background: white;
        border: 3px solid #000;
    }

    .formCard,
    .historyHeader,
    .historyContent {
        background: white;
        border: 2px solid #000;
    }

    .closeButton,
    .tabActive,
    .tabInactive,
    .select,
    .input {
        border: 2px solid #000;
    }

    .closeButton:hover,
    .submitButton {
        background: #000;
        border: 2px solid #000;
    }
}