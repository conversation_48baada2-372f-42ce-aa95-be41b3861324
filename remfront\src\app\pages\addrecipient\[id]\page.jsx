"use client";
import { useState, useEffect, useRef } from "react";
import styles from "../addrecipient.module.css";
// import styles from "./addrecipient.module.css";
import Layout from "../../../components/Layout/page";
import Login from "@/app/sign/login/page";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import {
  getCountryTOapi,
  getCurrencyListByCountry,
} from "../../../api/getCountries/getCountriesApi";
import { editRecipientApi } from "@/app/api/addRecipientApi's/addRecipientapi";
import { useSearchParams } from "next/navigation";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useParams } from "next/navigation";

const page = () => {
  const [selectedOption, setSelectedOption] = useState("");
  const [citizenship, setCitizenship] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [dob, setDob] = useState("");
  const [dateOfIncorporation, setDateOfIncorporation] = useState("");
  const [shareHolderName, setShareHolderName] = useState("");
  const [email, setEmail] = useState("");
  const [shareHolderEmail, setShareHolderEmail] = useState("");
  const [countryTo, setCountryTo] = useState([]);
  const [currencyPayout, setCurrencyPayout] = useState("");
  const [dataPayout, setDataPayout] = useState([]);
  const [payMethodPayout, setPayMethodPayout] = useState("");
  const [label, setlabel] = useState("Account Number/ Upi Id");
  const [payData, setPayData] = useState([]);
  const [AccUpiID, setAccUpiID] = useState([]);

  const authTokenRef = useRef(null);
  const searchParams = useSearchParams();
  const params = useParams();
  if (typeof window !== "undefined") {
    const token = sessionStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }
  }
  console.log("resu1", citizenship);
  console.log("resu2", countryTo[0]);
  const passedId = params.id;

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 18 }, (_, index) => currentYear - index);

  const BaseURL = process.env.NEXT_PUBLIC_Base_URL;
  const handleDropdownChange = (event) => {
    setSelectedOption(event.target.value);
  };

  //get Edit fetch Details

  const handleFetchPutDetails = async () => {
    try {
      const res = await customFetchWithToken(`/get-recipient/${passedId}`);
      console.log("resu", res.data.data[0].data);

      // setFirstName(res.data.data[0].recipient_id__firstname);
      setSelectedOption(res.data.data[0].recipient_id__type);
      setCitizenship(res.data.data[0].recipient_id__country__country_name);
      setFirstName(res.data.data[0].recipient_id__firstname);
      setLastName(res.data.data[0].recipient_id__lastname);
      setDob(res.data.data[0].recipient_id__dob);
      setDateOfIncorporation(
        res.data.data[0].recipient_id__ubo_shareholder_date_of_incorporation
      );
      setShareHolderName(res.data.data[0].recipient_id__ubo_shareholder_name);
      setEmail(res.data.data[0].recipient_id__email);
      setShareHolderEmail(res.data.data[0].recipient_id__email);
      setCountryTo([res.data.data[0].recipient_id__country__country_name]);
      setCurrencyPayout(
        res.data.data[0].recipient_id__currency_payout_id__currency_name
      );
      setPayMethodPayout(
        res.data.data[0].recipient_id__payout_option_id__payment_method
      );

      setlabel(
        ` ${res.data.data[0].recipient_id__payout_option_id__payment_method}`
      );
      setPayData(res.data.data[0].data);
    } catch (error) {
      console.log(error);
    }
  };

  //get Edit fetch Details

  const handleFirstName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setFirstName(inputValue);
  };

  const handleLastName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setLastName(inputValue);
  };
  const handleEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@.]/g, "");
    setEmail(inputValue);
  };

  const handleShareHolderEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@.]/g, "");
    setShareHolderEmail(inputValue);
  };

  const handleShareHolderName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setShareHolderName(inputValue);
  };

  const handleEmailChange = (e) => {
    const inputValue = e.target.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!emailRegex.test(inputValue)) {
      console.log("Invalid email format");
      setEmail("");
    } else {
      setEmail(inputValue);
    }
  };

  const loadCountryTo = async () => {
    const res = await getCountryTOapi();

    setCountryTo(res.data.data);
    loadCurrencyToResult();
  };

  // paymentMethod code 👇

  const fetchPaymentMethodspayout = async () => {
    try {
      const resCurrency = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyPayout}`
      );

      const data = await resCurrency.json();
      setDataPayout(data.data);
      setMessagePayout(data.message);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const handlePayMethodPayout = (event) => {
    setPayMethodPayout(event.target.value);
  };

  const paymentOption = [
    {
      key: payMethodPayout,
      value: AccUpiID,
    },
  ];

  const addRecipientDataBusiness = {
    type: selectedOption,
    country: citizenship, //
    email: shareHolderEmail,
    currency_payout: currencyPayout,
    payout_option: payMethodPayout,
    ubo_shareholder_name: shareHolderName,
    ubo_shareholder_date_of_incorporation: dateOfIncorporation,
    data: payData,
    account_num_upi_id: "undefined",
    bank_account_holder_name: "undefined",
    ifsc_code: "undefined",
  };

  const addRecipientDataPerson = {
    type: selectedOption,
    country: citizenship, //
    firstname: firstName,
    lastname: lastName,
    dob: dob,
    email: email,
    currency_payout: currencyPayout,
    payout_option: payMethodPayout,
    account_num_upi_id: "undefined",
    bank_account_holder_name: "undefined",
    ifsc_code: "undefined",
    data: payData,
  };

  const submitEditRecipientHandler = async (e) => {
    e.preventDefault();

    const requiredLabel = {
      citizenship: "Country Name",
      firstName: "First Name",
      lastName: "Last Name",
      dob: "Date of Birth",
      email: "Contact Email",
      currencyPayout: "Currency of payout",
      payMethodPayout: "Select Payment Method (TO)",
      AccUpiID: `${label}`,
      bankAccHolderName: "Bank Accound Holder's Name",
      ifscCode: "IFSC Code",
      shareHolderName: "UBO Shareholder Name",
      shareHolderEmail: "Contact Email",
      dateOfIncorporation: "UBO Shareholder Date of Incorporation",
    };

    const handleInputLogic = () => {
      const requiredFields =
        selectedOption === "Person"
          ? {
              citizenship: requiredLabel.citizenship,
              firstName: requiredLabel.firstName,
              lastName: requiredLabel.lastName,
              dob: requiredLabel.dob,
              email: requiredLabel.email,
              currencyPayout: requiredLabel.currencyPayout,
              payMethodPayout: requiredLabel.payMethodPayout,
              AccUpiID: requiredLabel.AccUpiID,
            }
          : {
              citizenship: requiredLabel.citizenship,
              shareHolderEmail: requiredLabel.shareHolderEmail,
              shareHolderName: requiredLabel.shareHolderName,
              dateOfIncorporation: requiredLabel.dateOfIncorporation,
              currencyPayout: requiredLabel.currencyPayout,
              payMethodPayout: requiredLabel.payMethodPayout,
              AccUpiID: requiredLabel.AccUpiID,
            };

      for (const [field, label] of Object.entries(requiredFields)) {
        if (!eval(field)) {
          toast.error(`Please enter ${label} to proceed`);
          return false;
        }
      }

      return true;
    };

    handleInputLogic();

    try {
      if (selectedOption === "Business") {
        const res = await editRecipientApi(addRecipientDataBusiness, passedId);

        toast.success(res.data.message);
      } else if (selectedOption === "Person") {
        const res = await editRecipientApi(addRecipientDataPerson, passedId);

        toast.success(res.data.message);
      }
      // setSelectedOption(null);
      setCitizenship("");
      setFirstName("");
      setLastName("");
      setDob("");
      setDateOfIncorporation("");
      setShareHolderName("");
      setEmail("");
      setShareHolderEmail("");
      setCurrencyPayout("");
      setMessagePayout("");
      setDataPayout([]);
      setPayMethodPayout("");
      setAccUpiID("");
      setBankAccHolderName("");
      setIfscCode("");
    } catch (error) {
      // toast.error(error.data.message);
      console.log(error);
    }
  };

  const handleDynamicInputChange = (index, value) => {
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    const updatedPayData = [...payData];
    updatedPayData[index].value = inputValue;
    setPayData(updatedPayData);
  };

  const loadCurrencyToResult = async () => {
    try {
      const res = await getCurrencyListByCountry(citizenship);

      setCurrencyPayout(res.data.data.currency__currency_code);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    loadCurrencyToResult();
  }, [citizenship]);

  useEffect(() => {
    fetchPaymentMethodspayout();
  }, [currencyPayout]);

  useEffect(() => {
    loadCountryTo();
    if (passedId) {
      handleFetchPutDetails();
    }
  }, []);
  // paymentMethod code 👆
  return (
    <>
      <div>
        <Layout title="Edit Recipient">
          <div className={styles.rightContainerBody}>
            <div className={styles.body}>
              <div className={styles.secondformWrapper}>
                <div className={styles.sec_firstName}>
                  <div className={styles.firstNameLabel}>Type</div>
                  <div className={styles.firstNameInput}>
                    <select
                      value={selectedOption}
                      onChange={handleDropdownChange}
                    >
                      <option value="select">select </option>
                      <option value="Person">Person</option>
                      <option value="Business">Business</option>
                    </select>
                  </div>
                </div>
                {selectedOption === "Person" && (
                  <div className={styles.sec_firstName}>
                    <div className={styles.firstNameLabel}>Country name</div>
                    <div className={styles.firstNameInput}>
                      <select
                        name="citizenship"
                        value={countryTo[0]}
                        onChange={(e) => setCitizenship(e.target.value)}
                        id="citizenship"
                        required
                      >
                        <option value="-1">Please select a Country</option>
                        {countryTo.map((country) => (
                          <option key={country.id} value={country.country_name}>
                            {country.country_name}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}
              </div>
              {/* names */}
              {selectedOption === "Person" && (
                <div className={styles.thirdformWrapper}>
                  <div className={styles.sec_firstName}>
                    <div className={styles.firstNameLabel}>First Name</div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="text"
                        value={firstName}
                        id="firstName"
                        maxLength={260}
                        onChange={handleFirstName}
                        required
                      />
                    </div>
                  </div>

                  <div className={styles.sec_firstName}>
                    <div className={styles.firstNameLabel}>Last Name</div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="text"
                        id="lastName"
                        value={lastName}
                        maxLength={260}
                        onChange={handleLastName}
                        required
                      />
                    </div>
                  </div>
                </div>
              )}
              {/* names */}

              {selectedOption === "Person" && (
                <div className={styles.thirdformWrapper}>
                  <div className={styles.sec_firstName}>
                    <div className={styles.firstNameLabel}>Date of Birth</div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="date"
                        id="date"
                        pattern="\d{4}-\d{2}-\d{2}"
                        onChange={(e) => setDob(e.target.value)}
                        value={dob}
                        max={`${currentYear - 18}-01-01`} // Set the maximum allowed date 18 years ago
                        required
                      />
                    </div>
                  </div>

                  <div className={styles.sec_firstName}>
                    <div className={styles.firstNameLabel}>Contact Email</div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="email"
                        id="contact_email"
                        value={email}
                        onChange={handleEmail}
                        aria-required
                      />
                    </div>
                  </div>
                </div>
              )}
              {selectedOption === "Business" && (
                <div>
                  {" "}
                  <div className={styles.secondformWrapper}>
                    <div className={styles.sec_firstName}>
                      <div className={styles.firstNameLabel}>Country name</div>
                      <div className={styles.firstNameInput}>
                        <select
                          name="citizenship"
                          value={citizenship}
                          onChange={(e) => setCitizenship(e.target.value)}
                          id="citizenship"
                          required
                        >
                          {" "}
                          <option value="-1">Please select a Country</option>
                          {countryTo.map((country) => (
                            <option
                              key={country.id}
                              value={country.country_name}
                            >
                              {country.country_name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div className={styles.sec_firstName}>
                      <div className={styles.firstNameLabel}>Contact Email</div>
                      <div className={styles.firstNameInput}>
                        <input
                          type="email"
                          value={shareHolderEmail}
                          id="shareHolderemail"
                          onChange={handleShareHolderEmail}
                          aria-required
                        />
                      </div>
                    </div>
                  </div>
                  <div className={styles.secondformWrapper}>
                    <div className={styles.sec_firstName}>
                      <div
                        htmlFor="shareHolderName"
                        className={styles.firstNameLabel}
                      >
                        UBO Shareholder Name
                      </div>
                      <div className={styles.firstNameInput}>
                        <input
                          type="text"
                          id="shareHolderName"
                          value={shareHolderName}
                          maxLength={260}
                          onChange={handleShareHolderName}
                          required
                        />
                      </div>
                    </div>

                    <div className={styles.sec_firstName}>
                      <div className={styles.firstNameLabel}>
                        UBO Shareholder Date of Incorporation
                      </div>
                      <div className={styles.firstNameInput}>
                        <input
                          type="date"
                          id="date"
                          pattern="\d{4}-\d{2}-\d{2}"
                          onChange={(e) =>
                            setDateOfIncorporation(e.target.value)
                          }
                          value={dateOfIncorporation}
                          placeholder="YYYY-MM-DD"
                          required
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {selectedOption === "Business" || selectedOption === "Person" ? (
                <div className={styles.secondformWrapper}>
                  <div className={styles.sec_firstName}>
                    <div className={styles.firstNameLabel}>
                      <label htmlFor="currencyPayout">Currency of payout</label>
                    </div>
                    <div className={styles.firstNameInput}>
                      <input
                        name="currencyPayout"
                        id="currencyPayout"
                        value={
                          currencyPayout
                            ? currencyPayout
                            : "Please select a country first"
                        }
                        readOnly
                        // onChange={handleCurrencyChangePayout}
                      />
                    </div>
                  </div>
                  <div className={styles.sec_firstName}>
                    <div className={styles.firstNameLabel}>
                      <label htmlFor="currencyPayout">
                        Select Payment Method (TO)
                      </label>
                    </div>
                    <div className={styles.firstNameInput}>
                      <select
                        name="currencyPayout"
                        id="currencyPayout"
                        value={payMethodPayout}
                        onChange={handlePayMethodPayout}
                      >
                        <option value="-1">
                          Please select a currency first
                        </option>

                        {dataPayout !== undefined
                          ? dataPayout.map((PayMethodName) => (
                              <option key={PayMethodName.id}>
                                {PayMethodName.payment_method}
                              </option>
                            ))
                          : ""}
                      </select>
                    </div>
                  </div>
                </div>
              ) : (
                ""
              )}

              {payData.map((el, index) => (
                <div key={index} className={styles.secondformWrapper}>
                  <div className={styles.sec_firstName}>
                    <div className={styles.firstNameLabel}>
                      <label htmlFor={`dynamic-${index}`}>{el.key}</label>
                    </div>
                    <div className={styles.firstNameInput}>
                      <input
                        type="text"
                        id={`dynamic-${index}`}
                        value={el.value}
                        maxLength={260}
                        onChange={(e) =>
                          handleDynamicInputChange(index, e.target.value)
                        }
                        required
                      />
                    </div>
                  </div>
                </div>
              ))}
              <div className={styles.listing_BtnCont}>
                <button
                  className={styles.listing_Btn}
                  onClick={submitEditRecipientHandler}
                >
                  Save Recipient
                </button>
              </div>
            </div>
          </div>
          <ToastContainer />
        </Layout>
      </div>
    </>
  );
};

export default page;
