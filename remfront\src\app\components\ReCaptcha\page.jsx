
"use client";
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import Re<PERSON>P<PERSON><PERSON> from 'react-google-recaptcha';


const ReCaptcha = forwardRef((props, parentRef) => {
  const recaptchaInstance = useRef(null);  // Local ref for ReCAPTCHA

  useImperativeHandle(parentRef, () => ({
    reset: () => recaptchaInstance.current?.reset(),  // Expose the reset method
  }));

  const onRecaptchaChange = (value) => {
    props.onChange(value);
  };

  const asyncScriptOnLoad = () => {
    console.log('Google recaptcha loaded just fine')
  }

  return (
    <ReCAPTCHA
      ref={recaptchaInstance}
      sitekey={process.env.NEXT_PUBLIC_SITE_KEY}// Replace with your actual site key
      onChange={onRecaptchaChange}
      asyncScriptOnLoad={asyncScriptOnLoad}
    />
  );
});

export default ReCaptcha;

