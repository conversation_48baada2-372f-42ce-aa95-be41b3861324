.cont {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-radius: 24px;
    padding: 32px;
    position: fixed;
    bottom: 24px;
    right: 24px;
    max-width: 400px;
    animation: slideIn 0.3s ease-out;
    z-index: 9999;
}

@keyframes slideIn {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.cont::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e93434 0%, #ff5757 100%);
    border-radius: 24px 24px 0 0;
}

.heading {
    color: #374151;
    font-size: 18px;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    margin-bottom: 8px;
}

.boxBody {
    color: #6b7280;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.timer {
    font-size: 32px;
    font-weight: 800;
    color: #e93434;
    text-align: center;
    font-family: 'Poppins', sans-serif;
}

.btnContainer {
    width: 100%;
    display: flex;
    gap: 12px;
    margin-top: 8px;
}

.btnGreen {
    flex: 1;
    padding: 12px;
    border-radius: 12px;
    outline: none;
    border: none;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: #ffffff;
    font-weight: 600;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btnGreen:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.btnRed {
    flex: 1;
    padding: 12px;
    border-radius: 12px;
    outline: none;
    border: none;
    background: linear-gradient(135deg, #e93434 0%, #ff5757 100%);
    color: #ffffff;
    font-weight: 600;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.btnRed:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(233, 52, 52, 0.3);
}

@media (max-width: 576px) {
    .cont {
        bottom: 16px;
        right: 16px;
        left: 16px;
        max-width: none;
        padding: 24px;
    }

    .btnContainer {
        flex-direction: column;
    }
}