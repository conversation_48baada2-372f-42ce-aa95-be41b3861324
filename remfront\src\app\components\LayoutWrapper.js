"use client";
import { useTimer } from "../context/TimerContext";
import { useEffect } from "react";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import SessionModal from "../components/SessionRefreshModal/page";

export default function LayoutWrapper({ children }) {
  const { showModal, handleSessionContinue, handleSessionEnd, startTimer } =
    useTimer();

  return (
    <>
      {children}

      {!!showModal && (
        <SessionModal
          onContinue={handleSessionContinue}
          onCancel={handleSessionEnd}
        />
      )}
      
      {/* Global ToastContainer - only one needed for entire app */}
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </>
  );
}
