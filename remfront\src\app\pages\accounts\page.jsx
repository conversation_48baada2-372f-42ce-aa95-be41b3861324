"use client";
import { useEffect, useState, useRef } from "react";
import styles from "./accounts.module.css";
import Layout from "../../components/Layout/page";
import AccountsCard from "../../components/AccountsCard/page";
import AccountsCardUser from "../../components/AccountsCardUser/page";
import AccountsAddPay from "../../components/AccountsAddPay/page";
import AccountsAddRecipient from "../../components/AccountsAddRecipient/page";
import LoadingSpinner from "../../components/LoadingSpinner/page";
import { toast } from "react-toastify";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useRouter } from "next/navigation";

const Page = () => {
  const router = useRouter();
  const authTokenRef = useRef(null);
  const userIdRef = useRef(null);
  const [userPayDataAll, setUserPayDataAll] = useState([]);
  const [userPayData, setUserPayData] = useState([]);
  const [displayPayment, setDisplayPayment] = useState("");
  const [loading, setLoading] = useState(false);
  const [showAddPaymentModal, setShowAddPaymentModal] = useState(false);
  const [showAddPaymentComp, setShowAddPaymentComp] = useState(false);
  const [showAddRecipientComp, setShowAddRecipientComp] = useState(false);
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const [loadCurrencyTo, setLoadCurrencyTo] = useState([]);
  const [currencyTo, setCurrencyTo] = useState("");
  const [currencyFrom, setCurrencyFrom] = useState("");``
  const [filterLoading, setFilterLoading] = useState(false);
  let userIdNumber;
  let token;
  if (typeof window !== "undefined") {
    userIdNumber = localStorage.getItem("userID");
    token = localStorage.getItem("user");
    if (token) {
      authTokenRef.current = token;
    }

    if (userIdNumber) {
      userIdRef.current = userIdNumber;
    }
  }

  if (!token) {
    router.push("/sign/login");
  }
  const handleAddPayModal = () => {
    setShowAddPaymentModal(!showAddPaymentModal);
    setShowAddPaymentComp(true); // Show AccountsAddPay component by default
  };

  const handleShowAddPayoutComp = () => {
    setShowAddPaymentComp(true);
    setShowAddRecipientComp(false);
  };

  const handleShowAddRecipientComp = () => {
    setShowAddPaymentComp(false);
    setShowAddRecipientComp(true);
  };

  const userPaymentData = async () => {
    setShowAddPaymentModal(false);
    setShowAddPaymentComp(false);
    setShowAddRecipientComp(false);
    setLoading(true);
    try {
      const res = await customFetchWithToken.get("/user-payment-fields-data/");

      setUserPayDataAll(res.data.data);
      setUserPayData(res.data.data);
      setDisplayPayment("Saved Payin Accounts");
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  const userRecipientPaymentData = async () => {
    setShowAddPaymentModal(false);
    setShowAddPaymentComp(false);
    setShowAddRecipientComp(false);
    setLoading(true);
    try {
      const res = await customFetchWithToken.get("/get-all-recipient");

      const returnArray = res.data.data;

      const filteredDataByUser = returnArray.filter(
        (data) => data.recipient_id__user_id === Number(userIdNumber)
      );

      setUserPayData(filteredDataByUser);
      setDisplayPayment("Recipient Accounts");
    } catch (error) {
      console.error(error);
      toast.error(error.response.data.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchCurrencyDataFrom = async () => {
    try {
      const resCurrency = await customFetchWithToken(`/currency-list/`);

      setLoadCurrencyFrom(resCurrency.data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchCurrencyDataTo = async () => {
    try {
      const resCurrency = await customFetchWithToken.get(
        `/payment-list/?currency=${currencyFrom}`
      );

      setLoadCurrencyTo(resCurrency.data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    }
  };

  const fetchListingsByCurrency = async () => {
    setFilterLoading(true);
    try {
      const filteredUserData = userPayDataAll.filter(
        (el) => el.payment_method === currencyTo
      );

      setUserPayData(filteredUserData);
    } catch (error) {
      console.error("Error filtering listings:", error);
      toast.error("Error filtering accounts");
    } finally {
      setFilterLoading(false);
    }
  };

  const handleCurrencyFrom = (event) => {
    setCurrencyFrom(event.target.value);
  };

  const handleCurrencyTo = (event) => {
    setCurrencyTo(event.target.value);
  };

  const handleListingid = (e) => {
    const value = e.target.value;
    setListingId(value);
  };

  useEffect(() => {
    fetchCurrencyDataFrom();
  }, []);
  useEffect(() => {
    if (currencyFrom) {
      fetchCurrencyDataTo();
    }
  }, [currencyFrom]);

  useEffect(() => {
    userPaymentData();
  }, []);
  // useEffect(() => {
  //   userRecipientPaymentData();
  // }, []);

  const accountsTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Saved Payment Methods</h1>
      <p className={styles.pageSubtitle}>
        Manage your saved payment accounts and recipients
      </p>
    </div>
  );

  return (
    <>
      {/* {authTokenRef.current ? ( */}
      <div style={{ overflowX: "hidden", width: "100%" }}>
        <Layout title={accountsTitle}>
          {/* Header Section - Hidden on desktop, shown only on mobile */}
          <div className={styles.mobileHeader}>
            <div className={styles.headerContent}>
              <h1 className={styles.pageTitle}>Saved Payment Methods</h1>
              <p className={styles.pageSubtitle}>
                Manage your saved payment accounts and recipients
              </p>
            </div>
          </div>
          <div className={styles.AccWrapper}>
            <div className={styles.pageTitle}>Saved Payment Methods</div>

            <div className={styles.filterBtnContainer}>
              <button
                className={styles.savedAddPayMethods}
                onClick={handleAddPayModal}
              >
                <span className={styles.buttonIcon}>+</span>
                Add Accounts
              </button>
              <button
                className={styles.savedUserPayMethods}
                onClick={userPaymentData}
              >
                <span className={styles.buttonIcon}>💳</span>
                Saved Payin Accounts
              </button>
              <button
                className={styles.savedRecipientPayMethods}
                onClick={userRecipientPaymentData}
              >
                <span className={styles.buttonIcon}>👥</span>
                Saved Recipient Accounts
              </button>
            </div>
          </div>
          <div className={styles.rightContainerBody}>
            {showAddPaymentModal ? (
              <div className={styles.addPayModalContainer} onClick={(e) => {
                if (e.target === e.currentTarget) {
                  setShowAddPaymentModal(false);
                  setShowAddPaymentComp(false);
                  setShowAddRecipientComp(false);
                }
              }}>
                <div className={styles.addPayWrapper}>
                  <button
                    className={styles.closeModalBtn}
                    onClick={() => {
                      setShowAddPaymentModal(false);
                      setShowAddPaymentComp(false);
                      setShowAddRecipientComp(false);
                    }}
                  >
                    ✕
                  </button>
                  <div className={styles.modalTitle}>Add Payment Account</div>
                  <div className={styles.topBtnSelector}>
                    <button
                      className={`${styles.payoutPayBtn} ${showAddPaymentComp ? styles.activeModalBtn : ''}`}
                      onClick={handleShowAddPayoutComp}
                    >
                      <span className={styles.buttonIcon}>💳</span>
                      Add Payin Accounts
                    </button>
                    <button
                      className={`${styles.payoutPayBtn} ${showAddRecipientComp ? styles.activeModalBtn : ''}`}
                      onClick={handleShowAddRecipientComp}
                    >
                      <span className={styles.buttonIcon}>👥</span>
                      Add Recipient Accounts
                    </button>
                  </div>
                  {showAddPaymentComp ? (
                    <div className={styles.addPayMethod}>
                      <AccountsAddPay showToast={false} />
                    </div>
                  ) : null}
                  {showAddRecipientComp ? (
                    <div className={styles.addPayMethod}>
                      <AccountsAddRecipient />
                    </div>
                  ) : null}
                </div>
              </div>
            ) : null}

            <div className={styles.displaypaymentCategory}>
              {displayPayment}
            </div>
            <div className={styles.body}>
              {/* /////////////////// */}

              <div className={styles.wrap}>
                <div className={styles.filter}>
                  <p className={styles.filterdialogue}>
                    <span className={styles.filterIcon}>🔍</span>
                    Filter Saved Accounts
                  </p>
                  <div className={styles.searchBodyCont}>
                    <div className={styles.firstNameInput}>
                      <select
                        name="currencyPayin"
                        id="currencyPayin"
                        onChange={handleCurrencyFrom}
                        className={styles.selectInput}
                      >
                        <option style={{ fontSize: "14px" }} value="">
                          Please select a currency
                        </option>
                        {loadCurrencyFrom?.map((currency, index) => (
                          <option style={{ fontSize: "14px" }} key={index}>
                            {currency.currency_code}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className={styles.firstNameInput}>
                      <select
                        name="currencyPayin"
                        id="currencyPayin"
                        onChange={handleCurrencyTo}
                        className={styles.selectInput}
                      >
                        <option style={{ fontSize: "14px" }} value="">
                          Please select a currency first
                        </option>
                        {loadCurrencyTo?.map((el, index) => (
                          <option style={{ fontSize: "14px" }} key={index}>
                            {el.payment_method}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className={styles.searchBtn}>
                      {currencyFrom && currencyTo ? (
                        <button
                          className={styles.search}
                          onClick={fetchListingsByCurrency}
                          disabled={filterLoading}
                        >
                          <span className={styles.buttonIcon}>
                            {filterLoading ? "⏳" : "🔍"}
                          </span>
                          {filterLoading ? "Filtering..." : "Search"}
                        </button>
                      ) : (
                        <button disabled className={styles.searchDisabled}>
                          <span className={styles.buttonIcon}>🔍</span>
                          Search
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* /////////////////////// */}
              <div className={styles.container}>
                {loading ? (
                  <div className={styles.loaderContainer}>
                    <LoadingSpinner text="Loading data..." />
                  </div>
                ) : filterLoading ? (
                  <div className={styles.loaderContainer}>
                    <LoadingSpinner text="Filtering accounts..." />
                  </div>
                ) : (
                  <div className={styles.payCard}>
                    {displayPayment === "Saved Payin Accounts" && (
                      <>
                        {userPayData && userPayData.length > 0 ? (
                          userPayData.map((item, index) => (
                            <div className={styles.AcCard} key={index}>
                              <AccountsCardUser
                                userPaymentData={userPaymentData}
                                key={item.id}
                                id={item.id}
                                method={item.payment_method}
                                item={item.data}
                              />
                            </div>
                          ))
                        ) : (
                          <div className={styles.noPayments}>
                            <div className={styles.emptyState}>
                              <span className={styles.emptyIcon}>💳</span>
                              <p>No Saved Payments Found</p>
                            </div>
                          </div>
                        )}
                      </>
                    )}

                    {displayPayment === "Recipient Accounts" && (
                      <>
                        {userPayData && userPayData.length > 0 ? (
                          userPayData.map((item, index) => (
                            <div className={styles.AcCard} key={index}>
                              <AccountsCard
                                userPaymentData={userRecipientPaymentData}
                                key={index}
                                id={item.recipient_id__id}
                                PayMethodName={
                                  item.recipient_id__payout_option_id__payment_method
                                }
                                bankAccHolder={item.bank_account_holder_name}
                                Ifsc={item.ifsc_code}
                                countryName={
                                  item.recipient_id__country__country_name
                                }
                                email={item.recipient_id__email}
                                firstName={item.recipient_id__firstname}
                                lastName={item.recipient_id__lastname}
                                type={item.recipient_id__type}
                                ubo_shareholder_date_of_incorporation={
                                  item.recipient_id__ubo_shareholder_date_of_incorporation
                                }
                                ubo_shareholder_name={
                                  item.recipient_id__ubo_shareholder_name
                                }
                                payout_option={item.payout_option}
                                dob={item.recipient_id__dob}
                                currency_payout={
                                  item.recipient_id__currency_payout_id__currency_name
                                }
                                created_date={item.recipient_id__updated_date}
                                AccountnumLabel={item.data[0].key}
                                Accountnum={item.data[0].value}
                              />
                            </div>
                          ))
                        ) : (
                          <div className={styles.noPayments}>
                            <div className={styles.emptyState}>
                              <span className={styles.emptyIcon}>👥</span>
                              <p>No Recipient Accounts Found</p>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                )}
                {!loading && !filterLoading && userPayData.length === 0 ? (
                  <div className={styles.loaderContainer}>
                    <div className={styles.emptyState}>
                      <span className={styles.emptyIcon}>📭</span>
                      <p>No Accounts Available</p>
                    </div>
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        </Layout>
      </div>
      {/* ) : (
        <Login />
      )} */}
    </>
  );
};

export default Page;
