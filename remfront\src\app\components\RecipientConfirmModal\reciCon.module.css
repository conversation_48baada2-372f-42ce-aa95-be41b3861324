/* Modern RecipientConfirmModal Styles */
/* Mobile-first responsive design with modern aesthetics */

/* Modal Container */
.modalContainer {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  width: 100%;
  max-width: 600px;
  position: relative;
  outline: none;
}

/* Modal Header */
.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #ffffff;
}

.modalTitle {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
  line-height: 1.25;
}

.closeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: #f9fafb;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  outline: none;
}

.closeButton:hover {
  background: #f3f4f6;
  color: #374151;
}

.closeButton:focus {
  background: #f3f4f6;
  color: #374151;
  box-shadow: 0 0 0 3px #dbeafe;
}

.closeButton:active {
  transform: scale(0.95);
}

/* Modal Content */
.modalContent {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  min-height: 0;
}

/* Loading State */
.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
  font-weight: 500;
}

/* Recipient Section */
.recipientSection {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sectionHeader {
  text-align: center;
}

.sectionTitle {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.25;
}

.sectionDescription {
  font-size: 14px;
  color: #e21a13;
  margin: 0;
  line-height: 1.5;
}

/* Recipient Grid */
.recipientGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

/* Recipient Card */
.recipientCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  outline: none;
  position: relative;
}

.recipientCard:hover {
  border-color: #d1d5db;
  background: #f9fafb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.recipientCard:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px #dbeafe;
}

.recipientCard:active {
  transform: translateY(0);
}

.recipientCardSelected {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 0 0 1px #3b82f6;
}

.recipientCardSelected:hover {
  background: #dbeafe;
}

.recipientCardContent {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 0;
}

.recipientName {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  line-height: 1.25;
}

.recipientDetail {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.recipientDetailLabel {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.recipientDetailValue {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  word-break: break-all;
}

.selectionIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-left: 12px;
  color: #3b82f6;
  flex-shrink: 0;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.emptyStateIcon {
  color: #d1d5db;
  margin-bottom: 16px;
}

.emptyStateTitle {
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0 0 8px 0;
  line-height: 1.25;
}

.emptyStateDescription {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.5;
  max-width: 320px;
}

.emptyStateAction {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  background: #3b82f6;
  color: #ffffff;
  text-decoration: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s ease-in-out;
  outline: none;
}

.emptyStateAction:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.emptyStateAction:focus {
  background: #2563eb;
  box-shadow: 0 0 0 3px #dbeafe;
}

.emptyStateAction:active {
  transform: translateY(0);
}

/* Modal Footer */
.modalFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
  gap: 40px;
}

.leftActions {
  flex: 0 0 auto;
  margin-right: auto;
}

.rightActions {
  display: flex;
  gap: 12px;
  flex: 0 0 auto;
}

.topActions {
  display: flex;
  gap: 12px;
  flex: 0 0 auto;
}

.bottomActions {
  flex: 0 0 auto;
  margin-right: auto;
}

/* Base button styles */
.primaryButton {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  background-color: #2563eb;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
}

.primaryButton:hover {
  background-color: #1d4ed8;
  transform: translateY(-1px);
}

.primaryButton:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
  transform: none;
}

.secondaryButton {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  background-color: white;
  color: #4b5563;
  border: 1px solid #d1d5db;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.secondaryButton:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.createRecipientBtn {
  background-color: #eab308;
  color: white;
  font-weight: 500;
  padding: 10px 24px;
}

.createRecipientBtn:hover {
  background-color: #ca8a04;
  color: white;
}

/* Mobile styles */
@media screen and (max-width: 640px) {
  .modalContainer {
    width: 95%;
    margin: 0 auto;
    max-width: 100%;
  }

  .modalFooter {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    width: 100%;
    box-sizing: border-box;
  }

  .topActions {
    display: flex;
    gap: 12px;
    width: 100%;
    box-sizing: border-box;
  }

  .topActions .secondaryButton,
  .topActions .primaryButton {
    flex: 1;
    min-width: 0;
    padding: 14px 16px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    justify-content: center;
    display: flex;
    align-items: center;
    height: 48px;
    box-sizing: border-box;
  }

  .topActions .secondaryButton:disabled,
  .topActions .primaryButton:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .bottomActions {
    width: 100%;
    box-sizing: border-box;
  }

  .bottomActions .createRecipientBtn {
    width: 100%;
    text-align: center;
    justify-content: center;
    display: flex;
    align-items: center;
    padding: 14px 16px;
    font-size: 14px;
    font-weight: 500;
    height: 48px;
    box-sizing: border-box;
  }
}

/* Responsive Design - Tablet and Desktop */
@media (min-width: 640px) {
  .modalContainer {
    max-width: 640px;
  }
  
  .recipientGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .recipientDetail {
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }
  
  .recipientDetailLabel::after {
    content: '';
  }
  
  .modalFooter {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
  
  .topActions {
    order: 2;
  }
  
  .bottomActions {
    order: 1;
  }
}

@media (min-width: 768px) {
  .modalContainer {
    max-width: 720px;
  }
  
  .modalHeader {
    padding: 32px;
  }
  
  .modalContent {
    padding: 32px;
  }
  
  .modalFooter {
    padding: 32px;
  }
  
  .modalTitle {
    font-size: 24px;
  }
  
  .sectionTitle {
    font-size: 20px;
  }
  
  .loadingState {
    padding: 64px 32px;
  }
  
  .emptyState {
    padding: 64px 32px;
  }
  
  .recipientGrid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modalContainer {
    border: 2px solid #000000;
  }
  
  .recipientCard {
    border-width: 3px;
  }
  
  .recipientCardSelected {
    border-color: #000000;
    background: #ffffff;
  }
  
  .primaryButton {
    background: #000000;
    border-color: #000000;
  }
  
  .primaryButton:hover {
    background: #333333;
    border-color: #333333;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modalContainer,
  .closeButton,
  .recipientCard,
  .recipientCardSelected,
  .emptyStateAction,
  .secondaryButton,
  .primaryButton {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .loadingSpinner {
    animation: none;
  }
}

/* Focus visible support for better accessibility */
@supports selector(:focus-visible) {
  .closeButton:focus {
    box-shadow: none;
  }
  
  .closeButton:focus-visible {
    box-shadow: 0 0 0 3px #dbeafe;
  }
  
  .recipientCard:focus {
    box-shadow: none;
  }
  
  .recipientCard:focus-visible {
    box-shadow: 0 0 0 3px #dbeafe;
  }
  
  .emptyStateAction:focus {
    box-shadow: none;
  }
  
  .emptyStateAction:focus-visible {
    box-shadow: 0 0 0 3px #dbeafe;
  }
  
  .secondaryButton:focus {
    box-shadow: none;
  }
  
  .secondaryButton:focus-visible {
    box-shadow: 0 0 0 3px #dbeafe;
  }
  
  .primaryButton:focus {
    box-shadow: none;
  }
  
  .primaryButton:focus-visible {
    box-shadow: 0 0 0 3px #dbeafe;
  }
}