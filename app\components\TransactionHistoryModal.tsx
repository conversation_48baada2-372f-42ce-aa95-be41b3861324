import React, { useState } from "react";
import { View, Text, TouchableOpacity, TextInput, Modal } from "react-native";
import { styled } from "nativewind";
import { Picker } from "@react-native-picker/picker";

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTouchableOpacity = styled(TouchableOpacity);
const StyledTextInput = styled(TextInput);

const TransactionHistoryModal = ({
  isVisible,
  onClose,

  data,
}: {
  isVisible: boolean;
  onClose: () => void;

  data: any;
}) => {
  const [selectedNetwork, setSelectedNetwork] = useState("");
  const [walletAddress, setWalletAddress] = useState("");

  const handleAddWallet = () => {
    onClose();
  };

  return (
    <Modal visible={isVisible} transparent={false} animationType="slide">
      <StyledView className="bg-white p-6 rounded-lg w-11/12">
        <StyledText className="text-lg font-bold text-center mb-4">
          Transaction History
        </StyledText>

        {/* Network Selection */}
        <StyledView className="mb-4"></StyledView>

        {/* Wallet Address Input */}
        <StyledView className="mb-4">
          <StyledText className="text-base font-medium mb-2">
            {data && data.length > 0 ? data : "No transactions available"}
          </StyledText>
        </StyledView>

        {/* Add Wallet Button */}
        <StyledTouchableOpacity
          onPress={handleAddWallet}
          className="bg-blue-600 py-3 rounded-md"
        >
          <StyledText className="text-white text-center font-bold">
            close
          </StyledText>
        </StyledTouchableOpacity>
      </StyledView>
    </Modal>
  );
};

export default TransactionHistoryModal;
