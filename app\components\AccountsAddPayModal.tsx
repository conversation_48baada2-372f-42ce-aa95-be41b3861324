import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Pressable,
} from "react-native";

import { Picker } from "@react-native-picker/picker";
import { styled } from "nativewind";
import Toast from "react-native-toast-message";
import customFetchWithToken from "../app/utils/axiosInterceptor";
import { Entypo } from "@expo/vector-icons";

interface Field {
  key: string; // Adjust the type if necessary (e.g., `number` or `string | number`)
}

interface UpiId {
  key: string; // Same as Field.key type
  value: string;
}

interface Currencies {
  currency_code: string;
  currency_name: string;
  id: number;
}
interface PaymentOption {
  id: number;
  payment_method: string;
}

const StyledView = styled(View);
const StyledText = styled(Text);
const StyledTextInput = styled(TextInput);
const StyledTouchableOpacity = styled(TouchableOpacity);

const AddPaymentMethod = ({
  isVisible,
  onClose,
}: {
  isVisible: boolean;
  onClose: () => void;
}) => {
  const [currencyFrom, setCurrencyFrom] = useState("");
  const [paymentMethodName, setPaymentMethodName] = useState("");
  const [totalFields, setTotalFields] = useState<Field[]>([]);
  const [accUpiIDs, setAccUpiIDs] = useState<UpiId[]>([]);
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState<Currencies[]>([]);
  const [dataAccepted, setDataAccepted] = useState<PaymentOption[]>([]);
  const [paymentMethodId, setPaymentMethodId] = useState<string>("");

  const BaseURL = process.env.EXPO_PUBLIC_Base_URL;

  const fetchCurrencyDataFrom = async () => {
    try {
      const response = await fetch(
        `${BaseURL}/get-disable-currency/?currency_from=True&disabled=True`
      );
      const data = await response.json();
      setLoadCurrencyFrom(data.data);
    } catch (error) {
      console.error("Error fetching currencies:", error);
      Toast.show({ type: "error", text1: "Error fetching currencies" });
    }
  };

  const fetchPaymentMethodsFrom = async () => {
    try {
      const response = await fetch(
        `${BaseURL}/payment-list/?currency=${currencyFrom}`
      );
      const data = await response.json();
      setDataAccepted(data.data);
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    }
  };

  const getPaymentFields = async () => {
    try {
      const res = await customFetchWithToken.get(
        `/user-payment-fields/?payment_method=${paymentMethodName}&currency=${currencyFrom}`
      );

      setTotalFields(res.data.data);
    } catch (error) {
      console.error("Error fetching payment fields:", error);
    }
  };

  const handlePaymentMethodChange = (value: string) => {
    setPaymentMethodName(value);

    const filteredPayment = dataAccepted.find(
      (payment) => payment.payment_method === value
    );

    if (filteredPayment) {
      setPaymentMethodId(String(filteredPayment.id));
    } else {
      console.error("Payment method not found in dataAccepted");
      setPaymentMethodId("");
    }
  };

  const handleAddPaymentMethod = async () => {
    if (totalFields.length !== accUpiIDs.length) {
      return Toast.show({
        type: "error",
        text1: "Please fill all fields",
      });
    }
    const Data = {
      payment_list: paymentMethodId,
      data: accUpiIDs,
    };

    try {
      const response = await customFetchWithToken.post(
        "/user-payment-fields-data/",
        Data
      );
      if (response.status === 200 || response.status === 201) {
        Toast.show({ type: "success", text1: "Payment Method Added" });
        setCurrencyFrom("");
        setTotalFields([]);
        setAccUpiIDs([]);
      }
    } catch (error) {
      console.error("Error adding payment method:", error);
    }
  };

  useEffect(() => {}, [totalFields]);

  useEffect(() => {
    fetchCurrencyDataFrom();
  }, []);

  useEffect(() => {
    if (currencyFrom) fetchPaymentMethodsFrom();
  }, [currencyFrom]);

  useEffect(() => {
    if (paymentMethodName) {
      getPaymentFields();
    }
  }, [paymentMethodName]);

  return (
    <Modal visible={isVisible} transparent={true} animationType="slide">
      <StyledView className="flex-1 bg-black/30 justify-center items-center">
        <StyledView className="bg-white rounded-2xl w-11/12 max-w-md p-6 shadow-2xl">
          <StyledView className="flex flex-row justify-between items-center mb-6">
            <StyledText className="text-2xl font-bold text-gray-800">
              Add Payment Method
            </StyledText>
            <TouchableOpacity onPress={onClose}>
              <Entypo name="cross" size={24} color="#6B7280" />
            </TouchableOpacity>
          </StyledView>

          {/* Currency Selection */}
          <StyledView className="mb-5">
            <StyledText className="text-sm font-semibold text-gray-700 mb-2">
              Select Currency
            </StyledText>
            <View className="border border-gray-200 rounded-xl bg-gray-50">
              <Picker
                selectedValue={currencyFrom}
                onValueChange={(value: string) => setCurrencyFrom(value)}
                className="h-12"
              >
                <Picker.Item label="Select Currency" value="" />
                {loadCurrencyFrom.map((currency, index) => (
                  <Picker.Item
                    key={currency.id}
                    label={currency.currency_code}
                    value={currency.currency_code}
                  />
                ))}
              </Picker>
            </View>
          </StyledView>

          {/* Payment Method Selection */}
          <StyledView className="mb-5">
            <StyledText className="text-sm font-semibold text-gray-700 mb-2">
              Payment Method
            </StyledText>
            <View className="border border-gray-200 rounded-xl bg-gray-50">
              <Picker
                selectedValue={paymentMethodName}
                onValueChange={(value) => handlePaymentMethodChange(value)}
                className="h-12"
              >
                <Picker.Item label="Select Payment Method" value="" />
                {dataAccepted.map((method, index) => (
                  <Picker.Item
                    key={method.id}
                    label={method.payment_method}
                    value={method.payment_method}
                  />
                ))}
              </Picker>
            </View>
          </StyledView>

          {/* Dynamic Fields */}
          {totalFields.map((field, index) => (
            <StyledView key={field.key} className="mb-5">
              <StyledText className="text-sm font-semibold text-gray-700 mb-2">
                {field.key}
              </StyledText>
              <StyledTextInput
                placeholder={`Enter ${field.key}`}
                value={
                  accUpiIDs.find((item) => item.key === field.key)?.value || ""
                }
                maxLength={60}
                onChangeText={(value) => {
                  const updatedFields = accUpiIDs.filter(
                    (item) => item.key !== field.key
                  );
                  setAccUpiIDs([...updatedFields, { key: field.key, value }]);
                }}
                className="border border-gray-200 rounded-xl bg-gray-50 p-3 text-gray-800"
                placeholderTextColor="#9CA3AF"
              />
            </StyledView>
          ))}

          <StyledView className="mt-6 space-y-3">
            <StyledTouchableOpacity
              onPress={handleAddPaymentMethod}
              className="bg-blue-600 py-4 rounded-xl shadow-sm active:bg-blue-700"
            >
              <StyledText className="text-white text-center font-bold text-base">
                Add Payment Method
              </StyledText>
            </StyledTouchableOpacity>
          </StyledView>
        </StyledView>
      </StyledView>
      <Toast />
    </Modal>
  );
};

export default AddPaymentMethod;
