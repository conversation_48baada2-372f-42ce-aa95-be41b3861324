{"expo": {"name": "Remflow", "slug": "remflow_app", "version": "1.0.0", "newArchEnabled": true, "jsEngine": "hermes", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "remflow", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "jsEngine": "hermes"}, "android": {"package": "com.remflow.app", "permissions": ["android.permission.READ_MEDIA_IMAGES", "android.permission.RECORD_AUDIO"], "jsEngine": "hermes", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], ["expo-image-picker", {"photosPermission": "The remflow App needs access to your photos to upload images."}], "expo-router", "expo-secure-store"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "14211b39-22b1-4ec4-938d-b72ac48036dd"}}}}