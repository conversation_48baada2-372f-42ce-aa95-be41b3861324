import { useState, useRef } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import {
  ScrollView,
  Text,
  View,
  TextInput,
  Image,
  TouchableOpacity,
} from "react-native";
import { Link, router } from "expo-router";
import "nativewind"; // Ensure that NativeWind is imported
import * as SecureStore from "expo-secure-store";
import axios from "axios";
import Toast, { BaseToast } from "react-native-toast-message";
import { useRouter } from "expo-router";
import registerApi from "./api/onboarding/registerApi";

const Register = () => {
  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green", width: "90%" }} // Adjust width to 100%
        contentContainerStyle={{ paddingHorizontal: 5 }}
        text1Style={{
          fontSize: 14,
          fontWeight: "bold",
          marginLeft: 20,
        }}
        text2Style={{
          fontSize: 29,
        }}
      />
    ),
    // You can add similar customizations for 'error' and 'info' types if needed
  };
  const showToastSuccess = (message: string) => {
    Toast.show({
      type: "success", // can also be 'error' or 'info'
      text1: message,
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };
  const showToastError = (message: string) => {
    Toast.show({
      type: "error", // can also be 'error' or 'info'
      text1: message,
      // text2: "This is a toast message 👋",
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };
  const Base_URL = process.env.EXPO_PUBLIC_Base_URL;
  const URL = `${Base_URL}/register/`;
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [passDialogue, setPassDialogue] = useState("");

  const [btnIsActive, setBtnIsActive] = useState(false);

  const handlePasswordChange = (value: string) => {
    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;
    setPassword(value);
    if (!passwordRegex.test(value)) {
      setPassDialogue(
        "Password must be 8-25 characters, with uppercase, lowercase, digit, and special character."
      );
      setBtnIsActive(false);
    } else {
      setPassDialogue("");
      setBtnIsActive(true);
    }
  };

  const onSubmit = async () => {
    setPassDialogue("");
    setBtnIsActive(true);

    const Data = {
      email: email,
      firstname: firstName,
      lastname: lastName,
      password: password,
    };

    try {
      const res = await registerApi(URL, Data);

      if (res.status == 200) {
        showToastSuccess("Registration successful!");

        await SecureStore.setItemAsync(
          "userID",
          res.data.data.user_id.toString()
        );
        await SecureStore.setItemAsync(
          "userName",
          res.data.data.firstname.toString()
        );
        await SecureStore.setItemAsync(
          "lastname",
          res.data.data.lastname.toString()
        );
        await SecureStore.setItemAsync(
          "user",
          res.data.data.tokens.access.toString()
        );
        await SecureStore.setItemAsync(
          "refreshToken",
          res.data.data.tokens.refresh.toString()
        );
        await SecureStore.setItemAsync(
          "userEmail",
          res.data.data.user_email.toString()
        );
        await SecureStore.setItemAsync(
          "chatToken",
          res.data.data.chat_token.toString()
        );
        setTimeout(() => {
          router.push(
            `/personal?firstname=${firstName}&lastname=${lastName}&email=${email}`
          );
        }, 1000);
      }
    } catch (error: any) {
      console.log(error);
      console.log(error.response.data);

      showToastError(error.response?.data?.message || "Please try again.");
    } finally {
      setBtnIsActive(false);
    }
  };

  // async function save(key: string, value: string) {
  //   await SecureStore.setItemAsync(key, value);
  // }

  const submitHandler = () => {};

  return (
    <SafeAreaView>
      <ScrollView
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
      >
        <View className="px-5">
          <View className="items-center mb-2">
            <Image
              className="w-48 h-auto"
              source={require("../assets/images/remflow.png")}
            />
            {/* <Text className="text-black text-4lg font-pregular text-center">
              Register
            </Text> */}
          </View>
          <Text className="text-center text-black text-sm font-plight mb-5">
            Enter your credentials to create an account
          </Text>
          <View>
            <Text className="text-black text-lg font-pmedium mb-2">
              First Name
            </Text>
            <View className="bg-white border border-gray-300 rounded-md h-14 mb-2 justify-center">
              <TextInput
                className="h-11 px-4 text-black text-sm font-pmedium"
                placeholder="First Name"
                onChangeText={(newText) => setFirstName(newText)}
              />
            </View>
          </View>
          <View>
            <Text className="text-black text-lg font-pmedium mb-2">
              Last Name
            </Text>
            <View className="bg-white border border-gray-300 rounded-md h-14 mb-2 justify-center">
              <TextInput
                className="h-11 px-4 text-black text-sm font-pmedium"
                placeholder="Last Name"
                onChangeText={(newText) => setLastName(newText)}
              />
            </View>
          </View>
          <View>
            <Text className="text-black text-lg font-pmedium mb-2">
              Email Address
            </Text>
            <View className="bg-white border border-gray-300 rounded-md h-14 mb-2 justify-center">
              <TextInput
                className="h-11 px-4 text-black text-sm font-pmedium"
                placeholder="Email Address"
                onChangeText={(newText) => setEmail(newText)}
              />
            </View>
          </View>
          <View>
            <Text className="text-black text-lg font-pmedium mb-2">
              Password
            </Text>
            {passDialogue.length > 1 ? (
              <Text className="text-sm font-pmedium mb-2 text-red-500">
                {passDialogue}
              </Text>
            ) : (
              ""
            )}

            <View className="bg-white border border-gray-300 rounded-md h-14 mb-2 justify-center">
              <TextInput
                className="h-11 px-4 text-black text-sm font-pmedium"
                placeholder="Password"
                secureTextEntry
                onChangeText={handlePasswordChange}
              />
            </View>
          </View>
          <View className="mt-4 flex-row items-center">
            <Text className="text-black">
              I agree to the terms and conditions
            </Text>
          </View>
          <TouchableOpacity
            className="flex items-center justify-center mt-5 bg-[#4153ed] py-3 px-5 rounded-md"
            onPress={onSubmit}
          >
            <Text className="text-white text-lg font-bold">Register</Text>
          </TouchableOpacity>
          <View className="items-center mt-2">
            <Text className="text-black text-sm font-pmedium">
              Already have an account?
            </Text>
            <Link href="/" onPress={submitHandler}>
              {/* <Link href="/personal"> */}
              <Text className="text-[#4153ed] mt-2 text-lg font-pmedium cursor-pointer">
                Login
              </Text>
            </Link>
          </View>
        </View>
      </ScrollView>
      <Toast config={toastConfig} />
    </SafeAreaView>
  );
};

export default Register;
