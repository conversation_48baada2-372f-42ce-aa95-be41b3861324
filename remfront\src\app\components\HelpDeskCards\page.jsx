// "use client";
// import { useState } from "react";
import styles from "./helpCards.module.css";
import Image from "next/image";
import { helpDeskPostApi } from "@/app/api/helpDeskApis/helpDesk";

const page = ({
  time,
  ticketTitle,
  upload_evidence,
  admin_comment,
  is_deleted,
  comments,
  query_title,
}) => {
  const dateString = time;
  const date = new Date(dateString);
  return (
    <div className={styles.appealCardsCont}>
      <div className={styles.appealCards}>
        <div className={styles.cardItemStatus} style={{ fontWeight: "600" }}>
          Status : Pending
        </div>
        <div className={styles.cardItem} style={{ fontWeight: "600" }}>
          Title : {ticketTitle}
        </div>
        <div className={styles.cardItem}>
          <span style={{ fontWeight: "600" }}></span>Ticket raised on :{" "}
          {date.toDateString()}
        </div>

        <div className={styles.cardItemEvidence}>
          <span style={{ fontWeight: "600" }}>Evidence :</span>
          <Image
            src={upload_evidence}
            width={250}
            height={100}
            // alt="Picture of the logo"
          />
        </div>

        <div className={styles.cardItem}>
          <span style={{ fontWeight: "600" }}>Reason:</span> {query_title}
        </div>
        <div className={styles.cardItem}>
          <span style={{ fontWeight: "600" }}>Comments:</span> {comments}
        </div>

        <div className={styles.cardItem}>
          <span style={{ fontWeight: "600" }}>Customer Support Comment:</span>
          {admin_comment
            ? admin_comment
            : "We appreciate your patience,The customer support team will address your ticket soon."}
        </div>
      </div>
    </div>
  );
};

export default page;
