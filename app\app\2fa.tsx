import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
} from "react-native";
import Toast, { BaseToast } from "react-native-toast-message";
import { useNavigation } from "@react-navigation/native";
import customFetchWithToken from "./utils/axiosInterceptor"; // Adjust this import to your setup
import { useRouter } from "expo-router";
import * as SecureStore from "expo-secure-store";

const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "green", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{
        fontSize: 14,
        fontWeight: "bold",
        marginLeft: 20,
      }}
    />
  ),
  error: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "red", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{
        fontSize: 14,
        fontWeight: "bold",
        marginLeft: 20,
      }}
    />
  ),
};

const showToastSuccess = (message: string) => {
  Toast.show({
    type: "success",
    text1: message,
    position: "top",
    visibilityTime: 4000,
  });
};

const showToastError = (message: string) => {
  Toast.show({
    type: "error",
    text1: message,
    position: "top",
    visibilityTime: 4000,
  });
};

export default function TwoFactorAuth() {
  const router = useRouter();
  const navigation = useNavigation();
  const [secOtp, setSecOtp] = useState("");

  // Replace with SecureStore or AsyncStorage for React Native

  async function getValueFor(key: any) {
    let result = await SecureStore.getItemAsync(key);
    if (result) {
      console.log("token", result);
      alert("🔐 Here's your value 🔐 \n" + result);
    } else {
      alert("No values stored under that key.");
    }
  }

  const userEmail = getValueFor("userEmail");

  const handleVerifyQRToken = async () => {
    try {
      const response = await customFetchWithToken.post("/2fa/verify", {
        token: secOtp,
        email: userEmail,
      });

      if (response.status === 200) {
        showToastSuccess("2FA authentication successful.");
        const data = response.data.data;

        // Save user details locally using SecureStore or AsyncStorage
        // Example: await SecureStore.setItemAsync("userID", data.user_id);
        showToastSuccess("User authenticated successfully!");
        // navigation.navigate("/searchads"); // Adjust route as per your navigation

        router.push("/searchads");
      } else {
        showToastError("Authentication failed.");
      }
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.message || "An error occurred. Please try again.";
      showToastError(errorMessage);
    }
  };

  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1 }} className="bg-white">
      <View className="flex-1 justify-center items-center px-6">
        <Text className="text-4xl font-bold text-gray-800 mb-4">Remflow</Text>
        <Text className="text-2xl font-semibold text-gray-600 mb-4">
          2FA Authentication
        </Text>
        <Text className="text-center text-gray-500 mb-6">
          Please enter the authentication code to sign in.
        </Text>
        <TextInput
          className="border border-gray-300 rounded-lg px-4 py-2 w-full text-gray-800 mb-4"
          placeholder="Enter authentication OTP from the app"
          keyboardType="number-pad"
          autoCapitalize="none"
          value={secOtp}
          onChangeText={setSecOtp}
        />
        <TouchableOpacity
          onPress={handleVerifyQRToken}
          className="bg-blue-500 rounded-lg px-6 py-3 w-full"
        >
          <Text className="text-white text-center font-semibold">Submit</Text>
        </TouchableOpacity>
      </View>
      <Toast config={toastConfig} />
    </ScrollView>
  );
}
