import { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Text, View, TextInput, Image, TouchableOpacity } from "react-native";
import { Link } from "expo-router";
import "nativewind"; // Ensure that NativeWind is imported
import * as SecureStore from "expo-secure-store";
import { useRouter } from "expo-router";
import loginApi from "./api/onboarding/loginApi";
import Toast, { BaseToast } from "react-native-toast-message";
import { showToastSuccess } from "@/hooks/toast";
import { Ionicons } from "@expo/vector-icons";

export default function HomeScreen() {
  const toastConfig = {
    success: (props: any) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green", width: "90%" }} // Adjust width to 100%
        contentContainerStyle={{ paddingHorizontal: 5 }}
        text1Style={{
          fontSize: 14,
          fontWeight: "bold",
          marginLeft: 20,
        }}
        text2Style={{
          fontSize: 29,
        }}
      />
    ),
    // You can add similar customizations for 'error' and 'info' types if needed
  };
  const showToast = (message: string) => {
    Toast.show({
      type: "success", // can also be 'error' or 'info'
      text1: message,
      // text2: "This is a toast message 👋",
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };
  const showToastErr = (message: string) => {
    Toast.show({
      type: "error", // can also be 'error' or 'info'
      text1: message,
      // text2: "This is a toast message 👋",
      position: "top", // or 'bottom'
      visibilityTime: 4000, // duration in milliseconds
    });
  };

  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);

  const base_URL = process.env.EXPO_PUBLIC_Base_URL;
  const URL = `${base_URL}/login/`;

  // const submitHandler = async () => {
  //   const Data = {
  //     email: email,
  //     password: password,
  //   };
  //   showToastSuccess("Logging you in...");
  //   try {
  //     const res = await loginApi(URL, Data);
  //     showToast(res.data.message);

  //     const userObject = {
  //       userID: res.data.data.user_id.toString(),
  //       userName: res.data.data.firstname.toString(),
  //       lastname: res.data.data.lastname.toString(),
  //       // accessToken: res.data.data.tokens.access.toString(),
  //       refreshToken: res.data.data.tokens.refresh.toString(),
  //       userEmail: res.data.data.user_email.toString(),
  //       chatToken: res.data.data.chat_token.toString(),
  //     };

  //     await SecureStore.setItemAsync("userData", JSON.stringify(userObject));
  //     await SecureStore.setItemAsync(
  //       "user",
  //       res.data.data.tokens.access.toString()
  //     );
  //     setTimeout(() => {
  //       router.push("/dashboard");
  //     }, 1000);
  //   } catch (error: any) {
  //     console.log(error.response.data);
  //     showToastErr(error.response.data.message);
  //   }
  // };

  const submitHandler = async () => {
    const Data = {
      email: email,
      password: password,
    };
    showToastSuccess("Logging you in...");
    try {
      const res = await loginApi(URL, Data);
      // console.log(res.data.message);
      showToast(res.data.message);
      // localStorage.setItem("userID", user_id);
      // localStorage.setItem("userName", firstname);
      // localStorage.setItem("lastname", lastname);
      // localStorage.setItem("user", access);
      // localStorage.setItem("refreshToken", refresh);
      // localStorage.setItem("userEmail", user_email);
      // localStorage.setItem("chatToken", chat_token);
      // sessionStorage.setItem("user", access);

      await SecureStore.setItemAsync(
        "userID",
        res.data.data.user_id.toString()
      );
      await SecureStore.setItemAsync(
        "userName",
        res.data.data.firstname.toString()
      );
      await SecureStore.setItemAsync(
        "lastname",
        res.data.data.lastname.toString()
      );
      await SecureStore.setItemAsync(
        "user",
        res.data.data.tokens.access.toString()
      );
      await SecureStore.setItemAsync(
        "refreshToken",
        res.data.data.tokens.refresh.toString()
      );
      await SecureStore.setItemAsync(
        "userEmail",
        res.data.data.user_email.toString()
      );
      await SecureStore.setItemAsync(
        "chatToken",
        res.data.data.chat_token.toString()
      );
      setTimeout(() => {
        router.push("/dashboard");
      }, 3000);
    } catch (error: any) {
      console.log(error.response.data);
      showToastErr(error.response.data.message);
    }
  };

  return (
    <SafeAreaView>
      <View className="p-5">
        <View className="items-center mt-8 mb-5">
          <Image source={require("../assets/images/remflow.png")} />
          <Text className="text-black text-4xl font-pregular text-center">
            Login
          </Text>
        </View>
        <Text className="text-center text-black text-lg font-plight mb-12">
          Enter your credentials to access your account
        </Text>
        <Text className="text-center text-black text-lg font-plight mb-5">
          VERSION 33
        </Text>
        <View>
          <Text className="text-black text-xl font-pmedium mb-2">
            Email Address
          </Text>
          <View className="bg-white border border-gray-300 rounded-md h-14 justify-center">
            <TextInput
              onChangeText={(newText) => setEmail(newText)}
              className="h-11 px-4 text-black text-sm font-pmedium"
              placeholder="Email Address"
              maxLength={30}
            />
          </View>
        </View>
        <View className="mt-4">
          <View className="flex flex-row justify-between items-center relative">
            <Text className="text-black text-xl font-pmedium mb-2">
              Password
            </Text>
            <Link href="/forgetpass">
              <Text className="text-blue-600 text-xs font-pmedium mb-2 ">
                Forgot Password ?
              </Text>
            </Link>
          </View>
          <View className="bg-white border border-gray-300 rounded-md h-14 justify-center flex-row items-center">
            <TextInput
              onChangeText={(newText) => setPassword(newText)}
              className="h-11 px-4 text-black text-sm font-pmedium flex-1"
              placeholder="Password"
              secureTextEntry={!showPassword}
            />
            <TouchableOpacity
              onPress={() => setShowPassword(!showPassword)}
              className="px-3"
            >
              <Ionicons
                name={showPassword ? "eye" : "eye-off"}
                size={24}
                color="#4153ed"
              />
            </TouchableOpacity>
          </View>
        </View>
        <View className="mt-4 flex-row items-center">
          <TouchableOpacity
            onPress={() => setTermsAccepted(!termsAccepted)}
            className="mr-2"
          >
            <View className="w-5 h-5 border border-gray-400 rounded flex items-center justify-center bg-white">
              {termsAccepted && (
                <Ionicons name="checkmark" size={16} color="#4153ed" />
              )}
            </View>
          </TouchableOpacity>
          <Text className="text-black">
            I agree to the terms and conditions
          </Text>
        </View>
        <TouchableOpacity
          className="flex items-center justify-center mt-5 bg-[#4153ed] py-3 px-5 rounded-md"
          onPress={submitHandler} // Invoke submitHandler here
        >
          <Text className="text-white text-lg font-bold">Login</Text>
        </TouchableOpacity>

        <View className="items-center mt-5">
          <Text className="text-black text-sm font-pmedium">
            You don't have an account?
          </Text>
          <Link href="/register">
            {/* <Link href="/register"> */}
            <Text className="text-[#4153ed] mt-2 text-lg font-pmedium cursor-pointer">
              Register
            </Text>
          </Link>
          <Link href="/history">
            <Text className="text-[#4153ed] mt-2 text-lg font-pmedium cursor-pointer">
              profile
            </Text>
          </Link>
        </View>
        <Toast config={toastConfig} />
      </View>
    </SafeAreaView>
  );
}
