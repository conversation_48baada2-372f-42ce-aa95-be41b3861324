import React from 'react';
import Image from 'next/image';
import styles from './RightSideAnimation.module.css';
import linkedIn from '../../../../public/assets/socials/linkedin.svg';
// import linkedIn from '../../../public/assets/socials/linkedin.svg';
import facebook from '../../../../public/assets/socials/facebook.svg';

const RightSideAnimation = () => {
  return (
    <div className={styles.rightContainer}>
      <div className={styles.rightBody}>
        {/* Security Badge */}
        <div className={styles.securityBadge}>
          Secured Platform
        </div>
        
        {/* Animated Illustration */}
        <div className={styles.illustrationContainer}>
          <div className={styles.globalNetwork}>
            {/* Network Nodes */}
            <div className={styles.networkNode}></div>
            <div className={styles.networkNode}></div>
            <div className={styles.networkNode}></div>
            <div className={styles.networkNode}></div>
            <div className={styles.networkNode}></div>
            
            {/* Connection Lines */}
            <div className={styles.connectionLine}></div>
            <div className={styles.connectionLine}></div>
            <div className={styles.connectionLine}></div>
            
            {/* Money Symbols */}
            <div className={styles.moneySymbol}>$</div>
            <div className={styles.moneySymbol}>€</div>
            <div className={styles.moneySymbol}>£</div>
            
            {/* Security Shield */}
            <div className={styles.securityShield}>
              <svg viewBox="0 0 24 24">
                <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1ZM10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
              </svg>
            </div>
            
            {/* Floating Currency Symbols */}
            <div className={styles.currencyFloat}>¥</div>
            <div className={styles.currencyFloat}>₹</div>
            <div className={styles.currencyFloat}>₦</div>
          </div>
        </div>
        
        <div className={styles.textContainer}>
          <div>
            <div className={styles.firstLine}>
              Secure Global Money Transfers
            </div>
            <div className={styles.secondLine}>
              Join thousands of users who trust Remflow for fast, secure, and compliant cross-border payments. Our platform connects you with verified traders worldwide.
            </div>
            <div className={styles.lastLine}>
              Experience seamless currency exchange with real-time rates, end-to-end encryption, and 24/7 transaction monitoring.
            </div>
            
            {/* Feature Highlights */}
            <div className={styles.featureHighlights}>
              <div className={styles.featureItem}>
                <div className={styles.featureIcon}>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1ZM10 17L6 13L7.41 11.59L10 14.17L16.59 7.58L18 9L10 17Z"/>
                  </svg>
                </div>
                <span>Bank-level security & encryption</span>
              </div>
              <div className={styles.featureItem}>
                <div className={styles.featureIcon}>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L13.5 2.5L16.17 5.17L10.59 10.75C10.21 10.28 9.69 10 9 10C7.9 10 7 10.9 7 12S7.9 14 9 14C10.1 14 11 13.1 11 12C11 11.31 10.72 10.79 10.25 10.41L15.83 4.83L18.5 7.5L17 9H21ZM12 16C13.1 16 14 16.9 14 18C14 19.1 13.1 20 12 20C10.9 20 10 19.1 10 18C10 16.9 10.9 16 12 16Z"/>
                  </svg>
                </div>
                <span>Global network of verified traders</span>
              </div>
              <div className={styles.featureItem}>
                <div className={styles.featureIcon}>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M13 9H11V7H13M13 17H11V11H13M12 2A10 10 0 0 0 2 12A10 10 0 0 0 12 22A10 10 0 0 0 22 12A10 10 0 0 0 12 2Z"/>
                  </svg>
                </div>
                <span>Real-time transaction tracking</span>
              </div>
              <div className={styles.featureItem}>
                <div className={styles.featureIcon}>
                  <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 3C7.58 3 4 6.58 4 11C4 14.17 6.05 16.9 8.91 17.71L12 22L15.09 17.71C17.95 16.9 20 14.17 20 11C20 6.58 16.42 3 12 3ZM12 13C10.9 13 10 12.1 10 11C10 9.9 10.9 9 12 9C13.1 9 14 9.9 14 11C14 12.1 13.1 13 12 13Z"/>
                  </svg>
                </div>
                <span>Available in 100+ countries</span>
              </div>
            </div>

            {/* Trust Indicators */}
            <div className={styles.trustIndicators}>
              <div className={styles.trustItem}>
                <div className={styles.trustNumber}>99.9%</div>
                <div className={styles.trustLabel}>Uptime</div>
              </div>
              <div className={styles.trustItem}>
                <div className={styles.trustNumber}>$50M+</div>
                <div className={styles.trustLabel}>Processed</div>
              </div>
              <div className={styles.trustItem}>
                <div className={styles.trustNumber}>50K+</div>
                <div className={styles.trustLabel}>Users</div>
              </div>
            </div>
          </div>
          
          {/* bottom section */}
          <div className={styles.bottomSection}>
            <div className={styles.socialLinksCont}>
              <a
                href="https://www.facebook.com/remflw"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  width={20}
                  height={20}
                  src={facebook}
                  alt="Facebook"
                />
              </a>
              <a
                href="https://www.linkedin.com/company/remflow/"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Image
                  width={20}
                  height={20}
                  src={linkedIn}
                  alt="LinkedIn"
                />
              </a>
            </div>
          </div>
          {/* bottom section */}
        </div>
      </div>
    </div>
  );
};

export default RightSideAnimation;
