"use client";
import { useState, useRef, useEffect } from "react";
import styles from "./setings.module.css";
import Layout from "../../components/Layout/page";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { useRouter } from "next/navigation";
import { QRCodeSVG } from "qrcode.react";
import { Tooltip } from "react-tooltip";
import ConfirmationModal from "../../components/profileConfirmationModal/page";
import { CopyToClipboard } from "react-copy-to-clipboard";

const Page = () => {
  const router = useRouter();
  const authTokenRef = useRef(null);

  // Tab state
  const [activeTab, setActiveTab] = useState("profile");

  // Profile editing states
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [mobile, setMobile] = useState("");
  const [profilePic, setProfilePic] = useState("");
  const [preview, setPreview] = useState("");
  const [profileLoading, setProfileLoading] = useState(false);

  // Password change states
  const [oldPass, setOldPass] = useState("");
  const [newPass, setNewPass] = useState("");
  const [confirmPass, setConfirmPass] = useState("");
  const [passwordLoading, setPasswordLoading] = useState(false);

  // Password validation states
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [newPassDialogue, setNewPassDialogue] = useState("");
  const [confirmPassDialogue, setConfirmPassDialogue] = useState("");
  const [isNewPasswordValid, setIsNewPasswordValid] = useState(false);
  const [isPasswordsMatch, setIsPasswordsMatch] = useState(false);

  // 2FA states
  const [secret, setSecret] = useState("");
  const [verifyOtp, setVerifyOtp] = useState("");
  const [twoFALoading, setTwoFALoading] = useState(false);
  const issuer = "remflow";

  // Delete account states
  const [showDeleteProfileModal, setShowDeleteProfileModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [twoFactorStatus, setTwoFactorStatus] = useState(null);

  // User data states
  const [userEmail, setUserEmail] = useState("");

  // Check authentication and redirect if needed - following pattern from other pages
  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");

    if (token) {
      authTokenRef.current = token;
    } else {
      router.push("/sign/login");
    }
  }

  // Use useEffect to set initial state values
  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedTwoFactorEnabled = sessionStorage.getItem("two_factor_enabled");
      const storedUserEmail = localStorage.getItem("userEmail");

      // Convert string to boolean: "true" -> true, "false" -> false, null -> false
      setTwoFactorStatus(storedTwoFactorEnabled === "true");
      setUserEmail(storedUserEmail || "");
    }
  }, []); // Remove router dependency

  // Profile editing handlers
  const handleFirstNameChange = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setFirstName("");
    } else {
      setFirstName(inputValue);
    }
  };

  const handleLastNameChange = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9]/g, "");
    if (inputValue.length > 20) {
      setLastName("");
    } else {
      setLastName(inputValue);
    }
  };

  const handleMobileChange = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9-.]/g, "");
    if (inputValue.length > 20) {
      setMobile("");
    } else {
      setMobile(inputValue);
    }
  };

  const handleImageChange = (event) => {
    const file = event.target.files[0];

    const allowedExtensions = ["image/jpeg", "image/png"];

    if (file && allowedExtensions.includes(file.type)) {
      const previewUrl = URL.createObjectURL(file);
      setPreview(previewUrl);
      setProfilePic(file);
    } else {
      toast.error("Invalid file type. Only JPEG and PNG files are allowed.");
    }
  };



  const validateProfileForm = () => {
    // Basic validation - require at least first name, last name, and mobile
    if (!firstName.trim()) {
      toast.error("Please enter your first name");
      return false;
    }
    if (!lastName.trim()) {
      toast.error("Please enter your last name");
      return false;
    }
    if (!mobile.toString().trim()) {
      toast.error("Please enter your mobile number");
      return false;
    }

    return true;
  };

  const fetchAndCreateFile = async (profilePicUrl) => {
    try {

      const response = await fetch(profilePicUrl);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const blob = await response.blob();

      
      // Extract file extension from URL or use the blob type
      let extension = 'jpg'; // default
      let mimeType = blob.type;
      
      // Try to get extension from URL
      const urlExtension = profilePicUrl.split('.').pop()?.toLowerCase();
      if (urlExtension && ['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(urlExtension)) {
        extension = urlExtension;
        if (urlExtension === 'jpg') extension = 'jpeg';
      } else if (blob.type) {
        // Get extension from mime type
        if (blob.type.includes('jpeg')) extension = 'jpeg';
        else if (blob.type.includes('png')) extension = 'png';
        else if (blob.type.includes('gif')) extension = 'gif';
        else if (blob.type.includes('webp')) extension = 'webp';
      }
      
      // Ensure proper mime type
      if (!mimeType || mimeType === "binary/octet-stream") {
        mimeType = `image/${extension}`;
      }
      
      const filename = `profile_picture.${extension}`;

      
      const file = new File([blob], filename, { type: mimeType });

      return file;
    } catch (error) {
      console.error("Error fetching or creating file:", error);
      throw error;
    }
  };

  const updateProfileFunc = async () => {
    if (!validateProfileForm()) {
      return;
    }

    setProfileLoading(true);

    try {
      let fileToSend;

      // Always include profile picture - either new file or existing image as file
      if (typeof profilePic === "string") {
        fileToSend = await fetchAndCreateFile(profilePic);
      } else if (profilePic) {
        fileToSend = profilePic;
      } else {
        throw new Error("No profile picture available");
      }

      const formData = new FormData();
      formData.append("first_name", firstName);
      formData.append("last_name", lastName);
      formData.append("phoneNumber", mobile.toString());
      formData.append("profile_picture", fileToSend);

      const res = await customFetchWithToken.put("/edit-user-details/", formData);
      toast.success(res.data.message);

      // Clear preview state after successful update
      setPreview("");

      // Reload profile data to get updated image URL
      await getProfileInfo();

    } catch (error) {
      console.error("Profile update error:", error);
      toast.error(error.response?.data?.message || "Failed to update profile");
    } finally {
      setProfileLoading(false);
    }
  };

  const getProfileInfo = async () => {
    try {
      const res = await customFetchWithToken.get("/view-user-details/");
      setFirstName(res.data.data.firstname);
      setLastName(res.data.data.lastname);
      setEmail(res.data.data.email);
      setMobile(Number(res.data.data.mobile));
      setProfilePic(res.data.data.img_logo);
    } catch (error) {
      console.error(error);
      toast.error("Failed to load profile information");
    }
  };

  // Load profile data on component mount
  useEffect(() => {
    getProfileInfo();
  }, []);

  // Password change handlers
  const handleOldPasswordChange = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(
      /[^a-zA-Z0-9!@#$%^&*()_+={}\[\]:;"'<>,.?\/\\|-]/g,
      ""
    );
    if (inputValue.length > 20) {
      setOldPass("");
    } else {
      setOldPass(inputValue);
    }
  };

  const handleNewPasswordChange = (e) => {
    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;

    const newPassword = e.target.value;
    const isValid = passwordRegex.test(newPassword);

    if (!isValid) {
      setNewPassDialogue(
        "Password must be 8-25 characters, with at least one uppercase, one lowercase, one digit, and one special character."
      );
      setIsNewPasswordValid(false);
    } else {
      setNewPassDialogue("");
      setIsNewPasswordValid(true);
    }

    setNewPass(newPassword);

    // Check if passwords match when new password changes
    if (confirmPass && newPassword && newPassword === confirmPass && isValid) {
      setConfirmPassDialogue("Passwords match");
      setIsPasswordsMatch(true);
    } else if (confirmPass && newPassword && newPassword !== confirmPass) {
      setConfirmPassDialogue("Passwords do not match.");
      setIsPasswordsMatch(false);
    }
  };

  const handleConfirmPasswordChange = (e) => {
    const confirmPassword = e.target.value;
    setConfirmPass(confirmPassword);

    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;
    const isNewPasswordValid = passwordRegex.test(newPass);

    if (newPass && confirmPassword && newPass !== confirmPassword) {
      setConfirmPassDialogue("Passwords do not match.");
      setIsPasswordsMatch(false);
    } else if (newPass && confirmPassword && newPass === confirmPassword && isNewPasswordValid) {
      setConfirmPassDialogue("Passwords match");
      setIsPasswordsMatch(true);
    } else {
      setConfirmPassDialogue("");
      setIsPasswordsMatch(false);
    }
  };

  const validatePasswordForm = () => {
    if (!oldPass.trim()) {
      toast.error("Please enter your old password");
      return false;
    }

    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;

    if (!passwordRegex.test(newPass)) {
      setNewPassDialogue(
        "Password must be 8-25 characters, with at least one uppercase, one lowercase, one digit, and one special character."
      );
      return false;
    }

    if (newPass !== confirmPass) {
      setConfirmPassDialogue("Passwords do not match.");
      return false;
    }

    return true;
  };

  const updatePasswordFunc = async () => {
    setNewPassDialogue("");
    setConfirmPassDialogue("");

    if (!validatePasswordForm()) {
      return;
    }

    setPasswordLoading(true);
    const data = {
      old_password: oldPass,
      new_password: newPass,
      confirm_password: confirmPass,
    };

    try {
      const res = await customFetchWithToken.post("/update-user-password/", data);
      toast.success(res.data.message);

      // Clear form and validation states on success
      setOldPass("");
      setNewPass("");
      setConfirmPass("");
      setNewPassDialogue("");
      setConfirmPassDialogue("");
      setIsNewPasswordValid(false);
      setIsPasswordsMatch(false);
    } catch (error) {
      console.error("Password update error:", error);
      toast.error(error.response?.data?.message || "Failed to update password");
    } finally {
      setPasswordLoading(false);
    }
  };

  // 2FA handlers
  const generateQRTokenFn = async (e) => {
    e.preventDefault();
    setTwoFALoading(true);

    try {
      const res = await customFetchWithToken.post("/generate-2fa-token/");
      setSecret(res.data.base32);
      toast.success("QR Code generated successfully");
      // Note: 2FA is not enabled until verification is complete
      // Keep current status unchanged during setup
    } catch (error) {
      console.error(error);
      toast.error(error.response?.data?.message || "Failed to generate QR code");
    } finally {
      setTwoFALoading(false);
    }
  };

  const disable2fa = async () => {
    setTwoFALoading(true);
    try {
      await customFetchWithToken.post("/TwoFAStatus/");
      toast.success("2FA disabled successfully");
      // Update both sessionStorage and local state consistently
      sessionStorage.setItem("two_factor_enabled", "false");
      setTwoFactorStatus(false);
    } catch (error) {
      console.error(error);
      toast.error(error.response?.data?.message || "Failed to disable 2FA");
    } finally {
      setTwoFALoading(false);
    }
  };

  const cancel2fa = () => {
    setSecret("");
    setVerifyOtp("");
  };

  const otpAuthUrl = `otpauth://totp/${issuer}:${encodeURIComponent(
    userEmail
  )}?secret=${secret}&issuer=${encodeURIComponent(issuer)}`;

  const verifyQRTokenFn = async (e) => {
    e.preventDefault();

    if (!verifyOtp.trim()) {
      toast.error("Please enter the verification code");
      return;
    }

    setTwoFALoading(true);
    const bodyTokenOtp = {
      otp: verifyOtp,
    };

    try {
      const res = await customFetchWithToken.post("/submit-2fa-token/", bodyTokenOtp);

      if (res.status === 200) {
        toast.success("2FA authentication successful");
        setSecret("");
        setVerifyOtp("");
        // Update both sessionStorage and local state consistently
        sessionStorage.setItem("two_factor_enabled", "true");
        setTwoFactorStatus(true);
      }
    } catch (error) {
      console.error(error);
      toast.error(error.response?.data?.message || "Failed to verify 2FA code");
    } finally {
      setTwoFALoading(false);
    }
  };

  // Delete account handlers
  const deleteProfileFunc = async () => {
    setShowDeleteProfileModal(true);
  };

  const handleDeleteConfirm = async () => {
    setDeleteLoading(true);
    const data = {
      flag: "delete",
    };
    try {
      const res = await customFetchWithToken.delete("/edit-user-details/", data);
      toast.success(res.data.message);
      setTimeout(() => {
        localStorage.clear();
        router.push("/sign/login");
      }, 1500);
    } catch (error) {
      console.error(error);
      toast.error(error.response?.data?.message || "Failed to delete account");
    } finally {
      setDeleteLoading(false);
      setShowDeleteProfileModal(false);
    }
  };

  const settingsTitle = (
    <div className={styles.headerContent}>
      <h1 className={styles.pageTitle}>Account Settings</h1>
      <p className={styles.pageSubtitle}>
        Manage your security settings and preferences
      </p>
    </div>
  );



  return (
    <>
      <div style={{ overflowX: "hidden", width: "100%" }}>
        <Layout title={settingsTitle}>
          {/* Header Section - Hidden on desktop, shown only on mobile */}
          <div className={styles.mobileHeader}>
            <div className={styles.headerContent}>
              <h1 className={styles.pageTitle}>Account Settings</h1>
              <p className={styles.pageSubtitle}>
                Manage your security settings and preferences
              </p>
            </div>
          </div>

          <div className={styles.rightContainerBody}>
            <div className={styles.settingsContainer}>

              {/* Tab Navigation */}
              <div className={styles.tabContainer}>
                <button
                  className={`${styles.tabButton} ${
                    activeTab === "profile" ? styles.tabButtonActive : ""
                  }`}
                  onClick={() => setActiveTab("profile")}
                >
                  <span className={styles.tabIcon}>👤</span>
                  Profile Settings
                </button>
                <button
                  className={`${styles.tabButton} ${
                    activeTab === "security" ? styles.tabButtonActive : ""
                  }`}
                  onClick={() => setActiveTab("security")}
                >
                  <span className={styles.tabIcon}>🔒</span>
                  Security Settings
                </button>
              </div>

              {/* Profile Tab Content */}
              {activeTab === "profile" && (
                <div className={styles.tabContent}>
                  {/* Combined Profile Information Section */}
                  <div className={styles.settingsCard}>
                    <div className={styles.cardHeader}>
                      <div className={styles.cardIcon}>👤</div>
                      <div className={styles.cardHeaderContent}>
                        <h3 className={styles.cardTitle}>Profile Information</h3>
                        <p className={styles.cardDescription}>
                          Update your profile picture and personal details
                        </p>
                      </div>
                    </div>

                    <div className={styles.cardBody}>
                      {/* Profile Picture and Basic Info Row */}
                      <div className={styles.profileMainSection}>
                        <div className={styles.profilePictureSection}>
                          <div className={styles.profilePictureContainer}>
                            <img
                              src={preview || profilePic || "/default-avatar.png"}
                              alt="Profile Picture"
                              className={styles.profilePicture}
                            />
                          </div>
                          <div className={styles.profilePictureControls}>
                            <label htmlFor="profilePictureInput" className={styles.fileInputLabel}>
                              <span className={styles.buttonIcon}>📁</span>
                              Choose Picture
                            </label>
                            <input
                              type="file"
                              id="profilePictureInput"
                              className={styles.fileInput}
                              onChange={handleImageChange}
                              accept="image/jpeg,image/png"
                            />
                            <p className={styles.fileInputHint}>
                              JPEG or PNG only, max 5MB
                            </p>
                          </div>
                        </div>

                        <div className={styles.profileFormSection}>
                          <div className={styles.formRow}>
                            <div className={styles.formGroup}>
                              <label htmlFor="firstName" className={styles.formLabel}>
                                First Name
                              </label>
                              <input
                                id="firstName"
                                type="text"
                                className={styles.formInput}
                                placeholder="Enter your first name"
                                value={firstName}
                                onChange={handleFirstNameChange}
                                maxLength={260}
                              />
                            </div>

                            <div className={styles.formGroup}>
                              <label htmlFor="lastName" className={styles.formLabel}>
                                Last Name
                              </label>
                              <input
                                id="lastName"
                                type="text"
                                className={styles.formInput}
                                placeholder="Enter your last name"
                                value={lastName}
                                onChange={handleLastNameChange}
                                maxLength={260}
                              />
                            </div>
                          </div>

                          <div className={styles.formRow}>
                            <div className={styles.formGroup}>
                              <label htmlFor="email" className={styles.formLabel}>
                                Email Address
                              </label>
                              <input
                                id="email"
                                type="email"
                                className={styles.formInput}
                                placeholder="<EMAIL>"
                                value={email}
                                readOnly
                              />
                            </div>

                            <div className={styles.formGroup}>
                              <label htmlFor="mobile" className={styles.formLabel}>
                                Mobile Number
                              </label>
                              <input
                                id="mobile"
                                type="text"
                                className={styles.formInput}
                                placeholder="Enter your mobile number"
                                value={mobile === 0 ? "" : mobile}
                                onChange={handleMobileChange}
                                maxLength={20}
                              />
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className={styles.formActions}>
                        <button
                          className={styles.primaryButton}
                          onClick={updateProfileFunc}
                          disabled={profileLoading}
                        >
                          <span className={styles.buttonIcon}>
                            {profileLoading ? "⏳" : "💾"}
                          </span>
                          {profileLoading ? "Updating..." : "Save Changes"}
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Delete Account Section */}
                  <div className={styles.settingsCard}>
                    <div className={styles.cardHeader}>
                      <div className={styles.cardIcon}>🗑️</div>
                      <div className={styles.cardHeaderContent}>
                        <h3 className={styles.cardTitle}>Delete Account</h3>
                        <p className={styles.cardDescription}>
                          Permanently delete your account and all associated data
                        </p>
                      </div>
                    </div>

                    <div className={styles.cardBody}>
                      <div className={styles.dangerZone}>
                        <div className={styles.dangerInfo}>
                          <div className={styles.warningIcon}>⚠️</div>
                          <div className={styles.warningContent}>
                            <h4 className={styles.warningTitle}>Warning</h4>
                            <p className={styles.warningText}>
                              This action cannot be undone. This will permanently delete your account,
                              remove all your data, and cancel any active subscriptions.
                            </p>
                          </div>
                        </div>

                        <button
                          className={styles.dangerButton}
                          onClick={deleteProfileFunc}
                          disabled={deleteLoading}
                        >
                          <span className={styles.buttonIcon}>
                            {deleteLoading ? "⏳" : "🗑️"}
                          </span>
                          {deleteLoading ? "Deleting..." : "Delete Account"}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Security Tab Content */}
              {activeTab === "security" && (
                <div className={styles.tabContent}>
                  {/* Password Change Section */}
              <div className={styles.settingsCard}>
                <div className={styles.cardHeader}>
                  <div className={styles.cardIcon}>🔒</div>
                  <div className={styles.cardHeaderContent}>
                    <h3 className={styles.cardTitle}>Change Password</h3>
                    <p className={styles.cardDescription}>
                      Update your account password to keep your account secure
                    </p>
                  </div>
                </div>

                <div className={styles.cardBody}>
                  <div className={styles.formGroup}>
                    <label htmlFor="oldPassword" className={styles.formLabel}>
                      Current Password
                    </label>
                    <input
                      id="oldPassword"
                      type="text"
                      className={styles.formInput}
                      placeholder="Enter your current password"
                      value={oldPass}
                      onChange={handleOldPasswordChange}
                      maxLength={260}
                    />
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="newPassword" className={styles.formLabel}>
                      New Password
                      <span
                        data-tooltip-id="password-tooltip"
                        data-tooltip-content="Password must be 8 to 25 characters long and contain at least one uppercase letter, one lowercase letter, one numeric digit, and one special character."
                        className={styles.tooltipIcon}
                        style={{
                          display: 'inline-flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          width: '16px',
                          height: '16px',
                          borderRadius: '50%',
                          backgroundColor: '#007bff',
                          color: 'white',
                          fontSize: '12px',
                          fontWeight: 'bold',
                          cursor: 'pointer',
                          marginLeft: '8px'
                        }}
                      >
                        ?
                      </span>
                    </label>
                    <div className={styles.passwordInputContainer}>
                      <input
                        id="newPassword"
                        type={showNewPassword ? "text" : "password"}
                        className={`${styles.formInput} ${isNewPasswordValid ? styles.inputSuccess : ""}`}
                        placeholder="Make sure it contains letters and numbers, at least 8 characters long."
                        value={newPass}
                        onChange={handleNewPasswordChange}
                        maxLength={260}
                        required
                      />
                      <span
                        className={styles.hidePass}
                        onClick={() => setShowNewPassword(!showNewPassword)}
                      >
                        {!showNewPassword ? (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 640 512"
                            width={20}
                            height={20}
                          >
                            <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                          </svg>
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 576 512"
                            width={20}
                            height={20}
                          >
                            <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                          </svg>
                        )}
                      </span>
                    </div>
                    <div className={`${styles.wrongPassDialogue} ${isNewPasswordValid ? styles.successMessage : ""}`}>
                      {newPassDialogue || "\u00A0"}
                    </div>
                  </div>

                  <div className={styles.formGroup}>
                    <label htmlFor="confirmPassword" className={styles.formLabel}>
                      Confirm New Password
                    </label>
                    <div className={styles.passwordInputContainer}>
                      <input
                        id="confirmPassword"
                        type={showConfirmPassword ? "text" : "password"}
                        className={`${styles.formInput} ${isPasswordsMatch ? styles.inputSuccess : ""}`}
                        placeholder="Passwords should match"
                        value={confirmPass}
                        onChange={handleConfirmPasswordChange}
                        maxLength={260}
                        required
                      />
                      <span
                        className={styles.hidePass}
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      >
                        {!showConfirmPassword ? (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 640 512"
                            width={20}
                            height={20}
                          >
                            <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                          </svg>
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 576 512"
                            width={20}
                            height={20}
                          >
                            <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                          </svg>
                        )}
                      </span>
                    </div>
                    <div className={`${styles.wrongPassDialogue} ${isPasswordsMatch ? styles.successMessage : ""}`}>
                      {confirmPassDialogue || "\u00A0"}
                    </div>
                  </div>

                  <button
                    className={`${styles.primaryButton} ${isPasswordsMatch && isNewPasswordValid ? styles.btnSuccess : ""}`}
                    onClick={updatePasswordFunc}
                    disabled={passwordLoading}
                  >
                    <span className={styles.buttonIcon}>
                      {passwordLoading ? "⏳" : "🔐"}
                    </span>
                    {passwordLoading ? "Updating..." : "Update Password"}
                  </button>
                </div>
              </div>

              {/* Two-Factor Authentication Section */}
              <div className={styles.settingsCard}>
                <div className={styles.cardHeader}>
                  <div className={styles.cardIcon}>🛡️</div>
                  <div className={styles.cardHeaderContent}>
                    <h3 className={styles.cardTitle}>Two-Factor Authentication</h3>
                    <p className={styles.cardDescription}>
                      Add an extra layer of security to your account with 2FA
                    </p>
                  </div>
                </div>

                <div className={styles.cardBody}>
                  {!secret ? (
                    <div className={styles.twoFASetup}>
                      <div className={styles.twoFAInfo}>
                        <div className={styles.infoIcon}>ℹ️</div>
                        <div className={styles.infoContent}>
                          <h4 className={styles.infoTitle}>Secure Your Account</h4>
                          <p className={styles.infoText}>
                            Two-factor authentication adds an extra layer of security to your account.
                            You'll need an authenticator app like Google Authenticator or Authy.
                          </p>
                        </div>
                      </div>

                      <button
                        className={twoFactorStatus ? styles.disable2FAButton : styles.primaryButton}
                        onClick={twoFactorStatus ? disable2fa : generateQRTokenFn}
                        disabled={twoFALoading}
                        aria-label={twoFactorStatus ? "Turn Off Two-Factor Authentication" : "Setup Two-Factor Authentication"}
                      >
                        <span className={styles.buttonIcon}>
                          {twoFALoading ? "⏳" : twoFactorStatus ? "🔒" : "🔑"}
                        </span>
                        {twoFactorStatus
                          ? "Disable Two-Factor Authentication"
                          : twoFALoading
                            ? "Generating..."
                            : "Setup Two-Factor Authentication"}
                      </button>
                    </div>
                  ) : (
                    <div className={styles.twoFAVerification}>
                      {/* Desktop: Side by side layout */}
                      <div className={styles.setupMethodsContainer}>
                        <div className={styles.qrSection}>
                          <h4 className={styles.qrTitle}>Scan QR Code</h4>
                          <p className={styles.qrDescription}>
                            Scan this QR code with your authenticator app to setup 2FA
                          </p>

                          <div className={styles.qrContainer}>
                            <QRCodeSVG value={otpAuthUrl} size={150} />
                          </div>
                        </div>

                        {/* Separator with OR text */}
                        <div className={styles.methodSeparator}>
                          <div className={styles.separatorLine}></div>
                          <span className={styles.separatorText}>OR</span>
                          <div className={styles.separatorLine}></div>
                        </div>

                        {/* Secret Key Section */}
                        <div className={styles.secretSection}>
                          <h4 className={styles.secretTitle}>Use this secret in your authenticator app:</h4>
                          <p className={styles.secretDescription}>
                            Copy this secret key and manually enter it in your authenticator app
                          </p>
                          <div className={styles.secretContainer}>
                            <div className={styles.secretDisplay}>
                              <code className={styles.secretCode}>{secret}</code>
                              <CopyToClipboard
                                text={secret}
                                onCopy={() => toast.success("Secret copied to clipboard!")}
                              >
                                <button className={styles.copyButton} type="button">
                                  <span className={styles.copyIcon}>📋</span>
                                  Copy
                                </button>
                              </CopyToClipboard>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Verification section below */}
                      <div className={styles.verificationSection}>
                        <div className={styles.formGroup}>
                          <label htmlFor="verificationCode" className={styles.formLabel}>
                            Verification Code
                          </label>
                          <input
                            id="verificationCode"
                            type="text"
                            className={styles.formInput}
                            placeholder="Enter 6-digit code from your authenticator app"
                            value={verifyOtp}
                            onChange={(e) => setVerifyOtp(e.target.value)}
                            maxLength={6}
                          />
                        </div>

                        <div className={styles.buttonGroup}>
                          <button
                            className={styles.secondaryButton}
                            onClick={cancel2fa}
                            disabled={twoFALoading}
                          >
                            Cancel
                          </button>
                          <button
                            className={styles.primaryButton}
                            onClick={verifyQRTokenFn}
                            disabled={twoFALoading || !verifyOtp.trim()}
                          >
                            <span className={styles.buttonIcon}>
                              {twoFALoading ? "⏳" : "✅"}
                            </span>
                            {twoFALoading ? "Verifying..." : "Verify Code"}
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
                </div>
              )}

              <Tooltip id="password-tooltip" />
            </div>
          </div>
        </Layout>
      </div>
      {/* ToastContainer removed - already provided globally in LayoutWrapper.js */}

      {/* Delete Account Confirmation Modal */}
      {showDeleteProfileModal && (
        <ConfirmationModal
          isOpen={showDeleteProfileModal}
          onClose={() => setShowDeleteProfileModal(false)}
          onConfirm={handleDeleteConfirm}
          title="Confirm Account Deletion"
          message="Are you sure you want to delete your account? This action cannot be undone and will permanently remove all your data."
          confirmButtonText="Delete Account"
          cancelButtonText="Cancel"
        />
      )}
    </>
  );
};

export default Page;