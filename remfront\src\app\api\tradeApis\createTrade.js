import axios from "axios";

const Base_url = process.env.NEXT_PUBLIC_Base_URL;
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const createTradeApi = async (
  listingID,
  amount,
  recipientId,
  duration
) => {
  const res = await axios({
    url: `${Base_url}/create-trade-request/?listing_id=${listingID}&amount=${amount}&recipient_id=${recipientId}&time_duration=${duration}`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
