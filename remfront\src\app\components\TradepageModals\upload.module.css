/* modal */
/* .modalWrapper {
    width: 700px;
} */

.modalHeaderCont {
    display: flex;
    justify-content: center;
    align-items: center;
}

.modalHeader {
    color: #4153ED;
    font-family: Poppins;
    font-size: 22px;
    font-style: normal;
    font-weight: 500;
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;

}

.modalHeader2 {
    color: #4153ED;
    font-family: Poppins;
    font-size: 22px;
    font-style: normal;
    font-weight: 500;
    display: flex;
    width: 100%;
    justify-content: center;

}

.issueSelect {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin-bottom: 10px;

    display: flex;

}

.issueSelect2 {
    color: #000;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    margin-bottom: 10px;
    justify-content: center;
    display: flex;

}

.optionsBox {
    display: flex;
    flex-direction: column;
}

.inputBoxes {
    margin: 5px 0px;
}

.options {
    color: #000;
    font-family: Poppins;
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    margin: 15px 0px;

    /* 50% */
}

.inputTextArea input {
    display: flex;
    width: 80%;
    height: 80px;
    padding: 15px 18px 10px 18px;

    gap: 10px;
    border-radius: 5px;
}

.submitBtnWrapper {
    width: 100%;
    border-radius: 5px;
    background: #4153ED;
    height: 40px;
    margin-top: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    cursor: pointer;
}

.submitBtnWrapper2 {
    width: 100%;
    border-radius: 5px;
    height: 40px;
    margin-top: 30px;
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.submitBtn {
    color: #FFF;
    text-align: center;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    background-color: transparent;
    outline: none;
    border: none;
    cursor: pointer;

}

.disputeClose {

    height: 40px;
    padding: 8px 20px;
    color: #9D9D9D;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    border-radius: 5px;
    background: #F2F2F2;
    border: none;
}

.disputeSubmit {
    height: 40px;
    padding: 8px 20px;
    border-radius: 5px;
    background: #4153ED;
    color: #FFF;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    border: none;
    cursor: pointer;
}

.finalSubmitBtn {
    width: 100%;
    height: 40px;
    border-radius: 5px;
    border: 1px solid #4153ED;
    background: #4153ED;
    color: #FFF;
    text-align: center;
    font-family: Poppins;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    cursor: pointer;

}

.inputUpload {
    width: 100%;
    height: 40px;
    border-radius: 5px;
    background: #F9F9F9;
    margin: 20px 0px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 15px;
}

.inputUpload input {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    background: #F9F9F9;
    font-size: 12px;
    font-family: poppins;
    border: none;
}

.passedTerms {
    margin-top: 3px;
    font-size: 12px;
    font-family: poppins;
    font-weight: 500;
}