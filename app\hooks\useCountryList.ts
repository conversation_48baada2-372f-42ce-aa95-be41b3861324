import { useState, useEffect } from "react";
import customFetchWithToken from "@/app/utils/axiosInterceptor";

export const useCurrencyAcceptedList = () => {
  const [loadCurrencyFrom, setLoadCurrencyFrom] = useState([]);
  const [loading1, setLoading1] = useState(true);

  const resAccCurrency = async () => {
    try {
      const resCurrency = await customFetchWithToken(
        "/currency-list/?currency_from=true"
      );
      setLoadCurrencyFrom(resCurrency.data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    } finally {
      setLoading1(false);
    }
  };

  useEffect(() => {
    resAccCurrency();
  }, []);

  return { loadCurrencyFrom, loading1 };
};
export const useCurrencyPayoutList = () => {
  const [loadCurrencyTo, setLoadCurrencyTo] = useState([]);
  const [loading2, setLoading2] = useState(true);

  const resAccCurrency = async () => {
    try {
      const resCurrency = await customFetchWithToken(
        "/currency-list/?currency_to=true"
      );
      setLoadCurrencyTo(resCurrency.data.data);
    } catch (error) {
      console.error("Error fetching currency data:", error);
    } finally {
      setLoading2(false);
    }
  };

  useEffect(() => {
    resAccCurrency();
  }, []);

  return { loadCurrencyTo, loading2 };
};
