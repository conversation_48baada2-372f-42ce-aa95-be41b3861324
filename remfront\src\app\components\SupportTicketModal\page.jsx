"use client";
import { useState } from "react";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import styles from "./supportTicket.module.css";

const SupportTicketModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    subject: "",
    body: ""
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.subject.trim()) {
      newErrors.subject = "Subject is required";
    }
    if (!formData.body.trim()) {
      newErrors.body = "Description is required";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await customFetchWithToken.post("/customer-support/", {
        subject: formData.subject.trim(),
        body: formData.body.trim()
      });

      if (response.status === 200 || response.status === 201) {
        // Success - clear form and close modal
        setFormData({ subject: "", body: "" });
        setErrors({});
        onClose();
        
        // You might want to show a success toast here
       toast.success("Support ticket created successfully!");
      }
    } catch (error) {
      console.error("Error creating support ticket:", error);
      
      if (error.response?.status === 401) {
        toast.error("Authentication failed. Please log in again.");
      } else if (error.response?.status === 400) {
        toast.error("Invalid request. Please check your input and try again.");
      } else {
        toast.error("Failed to create support ticket. Please try again later.");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      setFormData({ subject: "", body: "" });
      setErrors({});
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className={styles.modalOverlay} onClick={handleClose}>
      <div className={styles.modalContent} onClick={(e) => e.stopPropagation()}>
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>Create Support Ticket</h2>
          <button 
            className={styles.closeButton} 
            onClick={handleClose}
            disabled={isLoading}
            aria-label="Close modal"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div className={styles.modalBody}>
          <form onSubmit={handleSubmit} className={styles.form}>
            <div className={styles.formGroup}>
              <label htmlFor="subject" className={styles.label}>
                Subject <span className={styles.required}>*</span>
              </label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                className={`${styles.input} ${errors.subject ? styles.inputError : ''}`}
                placeholder="Brief description of your issue"
                disabled={isLoading}
                maxLength={200}
              />
              {errors.subject && (
                <span className={styles.errorMessage}>{errors.subject}</span>
              )}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="body" className={styles.label}>
                Description <span className={styles.required}>*</span>
              </label>
              <textarea
                id="body"
                name="body"
                value={formData.body}
                onChange={handleInputChange}
                className={`${styles.textarea} ${errors.body ? styles.inputError : ''}`}
                placeholder="Please describe your issue in detail..."
                disabled={isLoading}
                rows={6}
                maxLength={1000}
              />
              {errors.body && (
                <span className={styles.errorMessage}>{errors.body}</span>
              )}
              <div className={styles.characterCount}>
                {formData.body.length}/1000 characters
              </div>
            </div>

            <div className={styles.formActions}>
              <button
                type="button"
                onClick={handleClose}
                className={styles.cancelButton}
                disabled={isLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                className={styles.submitButton}
                disabled={isLoading || !formData.subject.trim() || !formData.body.trim()}
              >
                {isLoading ? (
                  <div className={styles.loadingContainer}>
                    <div className={styles.spinner}></div>
                    Creating...
                  </div>
                ) : (
                  <>
                    <span className={styles.buttonIcon}>🎫</span>
                    Create Ticket
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
    </div>
  );
};

export default SupportTicketModal; 