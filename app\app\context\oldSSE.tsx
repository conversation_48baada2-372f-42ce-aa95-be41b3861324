import React, { createContext, useContext, useEffect, useRef } from 'react';
import { useWebsocketContext } from './AuthContext';
import EventSource from 'react-native-sse';
import * as SecureStore from 'expo-secure-store';
import refreshTokenApi from '@/app/api/onboarding/refreshTokenEndpoint';
import { AxiosError } from 'axios';
import { usePathname } from 'expo-router';
import Toast from 'react-native-toast-message';
import { showToastSuccess } from '@/hooks/toast';


interface SSEContextType {
  // Empty interface since we're just handling connections
}

const SSEContext = createContext<SSEContextType | undefined>(undefined);

export const SSEProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { token } = useWebsocketContext();
    const eventSourceRef = useRef<EventSource | null>(null);
    const pathname = usePathname();
    let sseErrorCount = 0;
    
    let token1 = token;
  const refreshApiCall = async () => {
    try {
      const res = await refreshTokenApi();
      await SecureStore.setItemAsync("user", res.data.data.access_token);
      token1 = res.data.data.access_token;
      return res;
    } catch (error) {
      console.log("Error refreshing token:", error);
      throw error;
    }
  };

  const setupSSEConnection = () => {
    if (!token) return;
    
    // Don't connect on login/register pages
    if (pathname === '/' || pathname === '/register') {
      return;
    }

    const url = new URL("https://dev.remflow.net/remflow/notification-sse/");
    
    try {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      const es = new EventSource(url, {
        withCredentials: true,
        headers: {
          Authorization: {
            toString: function () {
              return "Bearer " + token1;
            },
            Accept: "text/event-stream",
            Connection: "keep-alive",
          },
        },
      });

      es.addEventListener("open", () => {
        console.log("SSE connection opened");
        sseErrorCount = 0;
      });

      es.addEventListener("message", (event: any) => {
        console.log("New SSE message:", event.data);
        if (event.data) {
          try {
            const data = JSON.parse(event.data);
            // Show toast notification with the message from SSE
            if (data.message) {
              showToastSuccess(data.message);
            } else {
              showToastSuccess("New notification received");
            }
          } catch (error) {
            console.error("Error handling SSE message:", error);
            showToastSuccess("New notification received");
          }
        }
      });

      es.addEventListener("error", async () => {
        if (sseErrorCount < 2) {
          console.error("SSE connection error");
          try {
            await refreshApiCall();
            setupSSEConnection(); // Retry connection with new token
          } catch (error) {
            console.error("Failed to refresh token:", error);
          }
          sseErrorCount++;
        }
      });

      eventSourceRef.current = es;

      return () => {
        es.close();
        eventSourceRef.current = null;
      };
    } catch (error) {
      if (error instanceof AxiosError && error.response?.status === 401) {
        refreshApiCall().catch(console.error);
      }
      console.error("Error setting up SSE:", error);
    }
  };

  useEffect(() => {
    if (token && pathname !== '/' && pathname !== '/register') {
      setupSSEConnection();
    }

    return () => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
        eventSourceRef.current = null;
      }
    };
  }, [token, pathname]);

  return (
    <SSEContext.Provider value={{}}>
      {children}
    </SSEContext.Provider>
  );
};

export const useSSE = () => {
  const context = useContext(SSEContext);
  if (context === undefined) {
    throw new Error('useSSE must be used within a SSEProvider');
  }
  return context;
}; 