/* Modern Dispute Page Styles */

/* Page Container */
.pageContainer {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Header Section */
.headerSection {
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  padding: 2rem 1rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  display: block;
}

.headerContent {
  display: none;  /* Hide header content by default on desktop */
}

/* Desktop Header Content */
.desktopHeaderContent {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

/* Mobile Header Content */
.mobileHeaderContent {
  display: none;  /* Hide by default on desktop */
  max-width: 1200px;
  margin: 0 auto;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

@media (max-width: 576px) {
  .headerSection {
    padding: 1rem;
    text-align: center;
  }
  
  .mobileHeaderContent {
    display: block;  /* Show only on mobile */
  }

  .desktopHeaderContent {
    display: none;  /* Hide desktop header on mobile */
  }
}

.pageTitle {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #1E293B 0%, #334155 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0 0 8px 0;
  letter-spacing: -0.5px;
}

.pageSubtitle {
  font-size: 14px;
  color: #64748B;
  margin: 0;
  font-weight: 400;
  line-height: 1.4;
}

.actionButtons {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: flex-start;
}

@media (max-width: 576px) {
  .actionButtons {
    justify-content: center;
  }
}

.actionBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: 1px solid transparent;
  text-decoration: none;
  min-height: 44px;
}

.btnIcon {
  width: 1.25rem;
  height: 1.25rem;
}

.primaryBtn {
  background: #3b82f6;
  color: #ffffff;
  border-color: #3b82f6;
}

.primaryBtn:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.secondaryBtn {
  background: #f8fafc;
  color: #475569;
  border-color: #e2e8f0;
}

.secondaryBtn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activeBtn {
  background: #1e40af;
  color: #ffffff;
  border-color: #1e40af;
  box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
}

.actionBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.actionBtn:active {
  transform: translateY(0);
}

/* Main Content */
.mainContent {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.contentWrapper {
  background: #ffffff;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Loading States */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.loadingSpinner {
  margin-bottom: 1rem;
}

.spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

/* Create Dispute Section */
.createDisputeSection {
  padding: 2rem;
}

.formCard {
  max-width: 800px;
  margin: 0 auto;
}

.formHeader {
  margin-bottom: 2rem;
  text-align: center;
}

.formTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.formDescription {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
  line-height: 1.5;
}

.formContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.fieldGroup {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fieldLabel {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  margin: 0;
}

.reasonGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.reasonBtn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-align: left;
  min-height: 60px;
}

.reasonBtn:hover {
  border-color: #3b82f6;
  background: #f8fafc;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

.reasonBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.selectedReason {
  border-color: #3b82f6;
  background: #eff6ff;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.reasonText {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.4;
}

.selectedReason .reasonText {
  color: #1e40af;
}

.checkIcon {
  width: 1.25rem;
  height: 1.25rem;
  color: #3b82f6;
  flex-shrink: 0;
}

.disputeForm {
  margin-top: 1.5rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  background: #f8fafc;
  @media (max-width: 720px) {
    padding: 0;
  }
}

.textArea {
  width: 100%;
  min-height: 120px;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: border-color 0.2s ease-in-out;
}

.textArea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textArea::placeholder {
  color: #9ca3af;
}

.charCount {
  font-size: 0.75rem;
  color: #6b7280;
  text-align: right;
  margin-top: 0.25rem;
}

.submitBtn {
  background: #10b981;
  color: #ffffff;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  margin-top: 1rem;
  min-height: 44px;
}

.submitBtn:hover {
  background: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.submitBtn:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
}

.submitBtn:active {
  transform: translateY(0);
}

/* Dispute List Section */
.disputeListSection {
  padding: 2rem;
}

.listHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.listTitle {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.disputeCount {
  background: #eff6ff;
  color: #1e40af;
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 600;
}

/* Table Styles */
.tableContainer {
  display: block;
  background: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tableWrapper {
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.tableWrapper::-webkit-scrollbar {
  height: 8px;
}

.tableWrapper::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.tableWrapper::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.tableWrapper::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.disputeTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
  min-width: 800px;
}

.tableHeader {
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
}

.tableHeaderCell {
  padding: 1rem 1.5rem;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e2e8f0;
  white-space: nowrap;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.headerIcon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
  flex-shrink: 0;
}

.tableBody {
  background: #ffffff;
}

/* Mobile Card Styles */
.mobileCardContainer {
  display: none;
  flex-direction: column;
  gap: 1rem;
}

.mobileCard {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.2s ease-in-out;
}

.mobileCard:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.mobileCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.mobileStatusBadge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #fef3c7;
  color: #92400e;
  padding: 0.375rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.mobileStatusIcon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.mobileOrderNumber {
  font-weight: 700;
  color: #1e293b;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.mobileCardContent {
  padding: 1rem;
}

.mobileDisputeTitle {
  font-size: 1rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.mobileEvidenceSection {
  display: flex;
  align-items: center;
  margin: 12px 0;
}

.mobileEvidenceLink {
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  color: #2563eb;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: rgba(37, 99, 235, 0.1);
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  width: 100%;
}

.mobileEvidenceLink:hover {
  background-color: rgba(37, 99, 235, 0.15);
}

.mobileEvidenceLabel {
  font-size: 14px;
  font-weight: 500;
}

.mobileEvidenceIcon {
  width: 18px;
  height: 18px;
  stroke: currentColor;
}

.mobileCommentSection {
  background: #f8fafc;
  border-radius: 0.375rem;
  padding: 0.75rem;
  border-left: 4px solid #10b981;
}

.mobileComment {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
}

.mobileCardFooter {
  padding: 1rem;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
}

.mobileCreatedDate {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.75rem;
  font-weight: 500;
}

.mobileDateIcon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  color: #9ca3af;
}

/* Empty State */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
}

.emptyIcon {
  width: 4rem;
  height: 4rem;
  color: #9ca3af;
  margin-bottom: 1.5rem;
}

.emptyIcon svg {
  width: 100%;
  height: 100%;
}

.emptyTitle {
  font-size: 1.25rem;
  font-weight: 600;
  color: #374151;
  margin: 0 0 0.5rem 0;
}

.emptyDescription {
  color: #6b7280;
  font-size: 1rem;
  margin: 0 0 2rem 0;
  max-width: 400px;
  line-height: 1.5;
}

.emptyActionBtn {
  background: #3b82f6;
  color: #ffffff;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 44px;
}

.emptyActionBtn:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.emptyActionBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.emptyActionBtn:active {
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .tableContainer {
    display: none;
  }

  .mobileCardContainer {
    display: flex;
  }
}

@media (max-width: 768px) {
  .headerSection {
    padding: 1.5rem 1rem;
  }

  .pageTitle {
    font-size: 24px;
  }

  .actionButtons {
    flex-direction: column;
  }

  .actionBtn {
    justify-content: center;
    width: 100%;
  }

  .mainContent {
    padding: 0 0.5rem;
  }

  .createDisputeSection,
  .disputeListSection {
    padding: 1rem;
  }

  .reasonGrid {
    grid-template-columns: 1fr;
  }

  .listHeader {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .headerSection {
    padding: 1rem 0.5rem;
  }

  .pageTitle {
    font-size: 24px;
  }

  .pageSubtitle {
    font-size: 13px;
  }

  .createDisputeSection,
  .disputeListSection {
    padding: 0.75rem;
  }

  .formTitle {
    font-size: 1.25rem;
  }

  .listTitle {
    font-size: 1.25rem;
  }

  .mobileCreatedDate {
    font-size: 0.6875rem;
  }
}

/* Focus and accessibility improvements */
.actionBtn:focus-visible,
.reasonBtn:focus-visible,
.submitBtn:focus-visible,
.emptyActionBtn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .actionBtn,
  .reasonBtn,
  .submitBtn,
  .emptyActionBtn {
    border-width: 2px;
  }

  .textArea {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .actionBtn,
  .reasonBtn,
  .submitBtn,
  .emptyActionBtn,
  .textArea,
  .spinner {
    transition: none;
    animation: none;
  }
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.modalHeader h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.closeButton {
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton svg {
  width: 24px;
  height: 24px;
}

.modalBody {
  padding: 16px;
  overflow-y: auto;
  max-height: calc(90vh - 70px);
}

.evidenceImage {
  width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
}

