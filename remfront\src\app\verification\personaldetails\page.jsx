"use client";
import React from "react";
import { Icon } from "@iconify/react";
import { useState, useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
// import { useSearchParams } from "next/navigation";
// Removed Image import - using modern design instead
import styles from "./personal.module.css";
import "./personalGlobal.css"; // Import global CSS for this page
// Removed image imports - using modern design instead
import {
  businesses,
  sources,
  genders,
} from "../../helpers/contants";

import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Tooltip } from "react-tooltip";
import { customFetchWithToken } from "@/app/utils/axiosInterpreter";

require("dotenv").config();

const PersonalDetails = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [personalBtnActive, setPersonalBtnActive] = useState(true);
  const [empBtnActive, setEmpBtnActive] = useState(false);
  const [email, setEmail] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [emailOTP, setEmailOTP] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [phoneOTP, setPhoneOTP] = useState("");
  const [dob, setDob] = useState("");
  const [citizenship, setCitizenship] = useState("");
  const [gender, setGender] = useState("");
  const [address, setAddress] = useState("");
  const [nameOfBusiness, setNameOfBusiness] = useState("");
  const [natureOfBusiness, setNatureOfBusiness] = useState("");
  const [sourceOfFunds, setSourceOfFunds] = useState("");
  const [countryOfBusiness, setCountryOfBusiness] = useState("");
  const [countriesFetched, setCountriesFetched] = useState([]);
  const [emailOtpVerified, setEmailOtpVerified] = useState(null);
  const [mobileOtpVerified, setMobileOtpVerified] = useState(null);
  const [phoneCountryArray, setPhoneCountryArray] = useState([]);
  const [countryPhone, setCountryPhone] = useState("");

  const phnCountryRef = useRef(true);

  function decodeData(encodedData) {
    return atob(encodedData); // Base64 decoding
  }

  const passedEmail = decodeData(searchParams.get("userEmail"));
  const passedfirstName = decodeData(searchParams.get("firstName"));
  const passedLastName = decodeData(searchParams.get("lastName"));

  let token;
  if (typeof window !== "undefined") {
    token = sessionStorage.getItem("user");
  }

  if (!token) {
    router.push("/sign/login");
  }

  const currentYear = new Date().getFullYear();

  useEffect(() => {
    setFirstName(passedfirstName);
    setLastName(passedLastName);
  }, []);

  useEffect(() => {
    setCountryOfBusiness(citizenship);
  }, [citizenship]);

  //email vaildation
  const validateEmail = (email) => {
    // Regular expression for email validation
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailPattern.test(email);
  };

  const handleFirstName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setFirstName(inputValue);
  };
  const handleLastName = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setLastName(inputValue);
  };

  const handleEmail = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9@. ]/g, "");
    setEmail(inputValue);
  };

  const handleSetEmailOtp = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setEmailOTP(inputValue.trim());
  };

  const handleCountryPhone = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9]/g, "");
    setCountryPhone(inputValue);
  };

  const handlePhoneOTP = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^0-9]/g, "");
    setPhoneOTP(inputValue);
  };

  const handleAddress = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setAddress(inputValue);
  };

  const handleNameOfBusiness = (e) => {
    const value = e.target.value;
    const inputValue = value.replace(/[^a-zA-Z0-9 ]/g, "");
    setNameOfBusiness(inputValue);
  };

  //email vaildation
  const HandleSubmitUserForm = async (e) => {
    e.preventDefault();

    if ((sourceOfFunds === "Salaried") & !nameOfBusiness) {
      toast.error("Name of business is required.");
      return;
    }
    if ((sourceOfFunds === "Salaried") & !natureOfBusiness) {
      toast.error("Nature of business is required.");
      return;
    }
    if ((sourceOfFunds === "Salaried") & !countryOfBusiness) {
      toast.error("Country of business is required.");
      return;
    }

    const requiredFields = {
      address: "Address",
      dob: "Date of birth",
      citizenship: "Citizenship",
      gender: "Gender",
      sourceOfFunds: "Source of Funds",
    };

    for (const [field, label] of Object.entries(requiredFields)) {
      if (!eval(field)) {
        toast.error(`${label} is required.`);
        return;
      }
    }

    const Data = {
      email: email,
      firstName: firstName,
      lastName: lastName,
      emailOTP: String(emailOTP),
      phoneNumber: `+${countryPhone}${phoneNumber}`,
      phoneOTP: String(phoneOTP),
      dob: dob,
      citizenship: citizenship,
      gender: gender,
      address: address,
      natureOfBusiness: natureOfBusiness,
      nameOfBusiness: nameOfBusiness,
      sourceOfFunds: sourceOfFunds,
      countryOfBusiness: countryOfBusiness,
    };

    try {
      const res = await customFetchWithToken.post("/user-details/", Data);

      if (res.status === 200) {
        toast.success(res.data.message);
        // setTimeout(() => router.push("/pages/searchads"), 1500);
        setTimeout(() => router.push("/verification/status"), 1500);
      } else {
        toast.error("Registration failed.");
      }
    } catch (error) {
      console.error("USERError:", error);

      toast.error(error.response.data.message);
    }
  };

  const sendEmailReq = async (e) => {
    e.preventDefault();
    try {
      if (!validateEmail(email)) {
        toast.error("Enter a valid email address");
        return;
      }
      const res = await customFetchWithToken.post("/email-otp/", {
        email: email,
      });

      if (res.status === 200) {
        toast.success(res.data.message);
      } else {
        toast.error("Registration failed.");
      }
    } catch (error) {
      console.error("Error:", error);
      console.log(error);
      toast.error(error.response.data.message);
    }
  };
  // email OTP

  //email otp verify
  const confirmEmailOtpPayload = {
    email: email,
    otp: Number(emailOTP),
  };
  const verifyEmail = async (e) => {
    e.preventDefault();
    try {
      const res = await customFetchWithToken.post(
        "/email-verify-otp/",
        confirmEmailOtpPayload
      );
      if (res.status === 200) {
        toast.success(res.data.message);
      } else {
        toast.error("Incorrect Email OTP");
      }
    } catch (error) {
      console.error("Error:", error);
      console.log(error);
      toast.error(error.response.data.message);
    }
  };

  //email otp verify

  const sendPhnOTPReq = async (e) => {
    e.preventDefault();
    try {
      const res = await customFetchWithToken.post("/mobile-otp/", {
        mobile: `+${countryPhone}${phoneNumber}`,
      });

      if (res.status === 200) {
        toast.success("OTP Sent to phone. Check Inbox");
      } else if (res.status === 429) {
        toast.success("exceeded the request limit");
      } else {
        toast.error("Failed to send OTP");
      }
    } catch (error) {
      console.log(error);
      toast.error(error.response.data.message);
    }
  };

  const mobileOtpVerifyPayload = {
    mobile: `+${countryPhone}${phoneNumber}`,
    otp: String(phoneOTP),
  };
  const confirmPhnOTP = async (e) => {
    e.preventDefault();
    try {
      const res = await customFetchWithToken.post(
        "/mobile-verify-otp/",
        mobileOtpVerifyPayload
      );

      if (res.status === 200) {
        toast.success(res.data.message);
      } else {
        toast.error("Failed to send OTP");
      }
    } catch (error) {
      console.error("Error:", error);
      console.log(error);
      toast.error(error.response.data.message);
    }
  };

  const handlePersonalBtn = () => {
    setEmpBtnActive(false);
    setPersonalBtnActive(true);
  };

  const handleEmpBtn = () => {
    setPersonalBtnActive(false);
    setEmpBtnActive(true);
  };

  const getPhoneCountryDropDown = async () => {
    try {
      if (phnCountryRef.current == false) return;
      const res = await customFetchWithToken.get("/country-code/");
      setPhoneCountryArray(res.data.data);
      phnCountryRef.current = false;
    } catch (error) {
      console.error(error);
    }
  };

  const handlePhoneNumber = (e) => {
    const value1 = e.target.value;
    const inputValue = value1.replace(/[^0-9]/g, "");

    setPhoneNumber(inputValue);
  };
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await customFetchWithToken.get("/country-list/");
        setCountriesFetched(res.data.data);
      } catch (error) {
        console.log(error);
      }
    };

    fetchData();
  }, []);

  const getUserRegistration = async () => {
    try {
      const res = await customFetchWithToken.get("/view-user-details/");

      setEmail(res.data.data.email);
      setFirstName(res.data.data.firstname);
      setLastName(res.data.data.lastname);
      setPhoneNumber(res.data.data.mobile);
      setDob(res.data.data.dob);
      setCitizenship(res.data.data.country);
      setGender(res.data.data.gender);
      setAddress(res.data.data.address);
      setSourceOfFunds(res.data.data.source_of_funds);
      setNatureOfBusiness(res.data.data.nature_of_business);
      setNameOfBusiness(res.data.data.name_of_business);
      setCountryOfBusiness(res.data.data.countryofbusiness);
      setEmailOtpVerified(res.data.data.email_verified);
      setMobileOtpVerified(res.data.data.mobile_verified);
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    getUserRegistration();
  }, []);

  useEffect(() => {
    getPhoneCountryDropDown();
  }, []);

  return (
    <>
      <div className={styles.mainContainer}>
        <div className={styles.topContainer}>
          <div className={styles.mobileHeader}>
            <div className={styles.verificationIcon}>
              <Icon icon="mdi:shield-check" width="48" height="48" />
            </div>
            <h2 className={styles.mobileTitle}>Personal Verification</h2>
          </div>
        </div>
        <div className={styles.leftContainer}>
          <div className={styles.leftWrapper}>
            <div className={styles.mainButtonsWrapper}>
              <div
                className={
                  personalBtnActive ? styles.personalBtn : styles.employBtn
                }
                onClick={handlePersonalBtn}
              >
                1. Personal
              </div>

              <div
                className={empBtnActive ? styles.personalBtn : styles.employBtn}
                onClick={handleEmpBtn}
              >
                2. Income
              </div>
            </div>
            <div className={styles.heading}>Personal Details</div>
            <div className={styles.formContainer}>
              <form action="" onSubmit={HandleSubmitUserForm}>
                {/* conditional rendering */}
                {personalBtnActive ? (
                  <div>
                    <div className={styles.formWrapper}>
                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>First Name*</div>
                        <div className={styles.firstNameInput}>
                          <input
                            type="text"
                            id="firstname"
                            maxLength={260}
                            onChange={handleFirstName}
                            // value={passedfirstName}
                            value={firstName ? firstName : passedfirstName}
                            required
                          />
                        </div>
                      </div>
                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>Last Name*</div>
                        <div className={styles.firstNameInput}>
                          <input
                            type="text"
                            id="lastname"
                            maxLength={260}
                            onChange={handleLastName}
                            value={lastName ? lastName : passedLastName}
                            required
                          />
                        </div>
                      </div>
                    </div>
                    {/* names */}
                    {/* email */}
                    <div className={styles.formWrapper}>
                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>
                          Email Address*
                        </div>
                        <div className={styles.firstNameInput}>
                          <input
                            type="text"
                            id="email"
                            disabled
                            maxLength={260}
                            autoComplete="email"
                            onChange={handleEmail}
                            value={email ? email : passedEmail}
                            required
                          />
                          {!emailOtpVerified ? (
                            <button
                              className={styles.emailBtn}
                              onClick={sendEmailReq}
                            >
                              Get OTP
                            </button>
                          ) : (
                            ""
                          )}
                        </div>
                      </div>
                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>
                          OTP (Email)*
                          {emailOtpVerified && (
                            <span className={styles.verifiedBadge}>
                              <Icon icon="mdi:check-circle" width="16" height="16" />
                              Verified
                            </span>
                          )}
                        </div>
                        <div className={`${styles.firstNameInput} ${emailOtpVerified ? styles.verifiedInput : ''}`}>
                          <input
                            type="text"
                            disabled={emailOtpVerified}
                            maxLength="4"
                            inputMode="numeric" // Set inputMode to numeric
                            id="emailOTP"
                            placeholder={
                              emailOtpVerified ? "✓ Verified" : "Enter OTP"
                            }
                            onChange={handleSetEmailOtp}
                            required
                            value={emailOtpVerified ? "✓ Verified" : emailOTP}
                          />
                          {!emailOtpVerified ? (
                            <button
                              className={styles.emailBtn}
                              onClick={verifyEmail}
                            >
                              Confirm
                            </button>
                          ) : (
                            <div className={styles.verifiedIcon}>
                              <Icon icon="mdi:shield-check" width="24" height="24" />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* email */}
                    {/* phn no */}

                    <div className={styles.formWrapper}>
                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>
                          Phone Number*{" "}
                          <span
                            style={{ fontStyle: "italic", fontSize: "14px" }}
                          >
                         
                          </span>
                        </div>

                        <div className={styles.firstNameInput}>
                          <span className={styles.courtyOptions}>
                            <select
                              name="country"
                              value={countryPhone}
                              onChange={(e) => setCountryPhone(e.target.value)}
                              id="country"
                              required
                            >
                              <option value="-1">Select Country</option>
                              {phoneCountryArray.map((el) => (
                                <option value={el.phone_code} key={el.phone_code}>
                                  {el.country_name} +{el.phone_code}
                                </option>
                              ))}
                            </select>
                          </span>
                          <input
                            type="text"
                            id="phone"
                            maxLength={260}
                            disabled={mobileOtpVerified}
                            onChange={handlePhoneNumber}
                            value={phoneNumber}
                            required
                          />
                          {!mobileOtpVerified ? (
                            <button
                              className={styles.emailBtn}
                              onClick={sendPhnOTPReq}
                            >
                              Get OTP
                            </button>
                          ) : (
                            ""
                          )}
                        </div>
                      </div>

                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>
                          OTP (Phone Number)*
                          {mobileOtpVerified && (
                            <span className={styles.verifiedBadge}>
                              <Icon icon="mdi:check-circle" width="16" height="16" />
                              Verified
                            </span>
                          )}
                        </div>
                        <div className={`${styles.firstNameInput} ${mobileOtpVerified ? styles.verifiedInput : ''}`}>
                          <input
                            type="text"
                            disabled={mobileOtpVerified}
                            maxLength="6"
                            inputMode="numeric" // Set inputMode to numeric
                            id="phoneOTP"
                            placeholder={
                              mobileOtpVerified ? "✓ Verified" : "Enter OTP"
                            }
                            onChange={handlePhoneOTP}
                            value={mobileOtpVerified ? "✓ Verified" : phoneOTP}
                            required
                          />
                          {!mobileOtpVerified ? (
                            <button
                              className={styles.emailBtn}
                              onClick={confirmPhnOTP}
                            >
                              Confirm
                            </button>
                          ) : (
                            <div className={styles.verifiedIcon}>
                              <Icon icon="mdi:shield-check" width="24" height="24" />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* phn no */}

                    {/* dob // citizenship // gender */}

                    <div className={styles.formWrapper}>
                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>
                          Date of birth*
                        </div>
                        <div className={styles.firstNameInput}>
                          <input
                            type="date"
                            id="birthdate"
                            value={dob ? dob : ""}
                            onChange={(e) => setDob(e.target.value)}
                            max={`${currentYear - 18}-01-01`} // Set the maximum allowed date 18 years ago
                            required
                          />
                          <div className={styles.calender}></div>
                        </div>
                      </div>
                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>
                          Citizenship*
                        </div>
                        <div className={styles.firstNameInput}>
                          <select
                            name="citizenship"
                            value={citizenship ? citizenship : ""}
                            onChange={(e) => setCitizenship(e.target.value)}
                            id="citizenship"
                            required
                          >
                            <option value="-1">Select a country</option>
                            {countriesFetched.map((country) => (
                              <option
                                key={country.id}
                                // value={country.country_name}
                              >
                                {country.country_name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                      <div className={styles.firstName}>
                        <div className={styles.firstNameLabel}>Gender*</div>
                        <div className={styles.firstNameInput}>
                          <select
                            name="gender"
                            id="gender"
                            value={gender ? gender : ""}
                            onChange={(e) => setGender(e.target.value)}
                            required
                          >
                            {genders.map((gender) => (
                              <option key={gender.value} value={gender.name}>
                                {gender.name}
                              </option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* dob // citizenship // gender */}
                    {/* Trade fee */}

                    <div className={styles.formWrapper}>
                      <div className={styles.addressName}>
                        <div className={styles.firstNameLabel}>
                          Address: (maximum input 100 characters)
                        </div>
                        <div className={styles.addressNameInput}>
                          <input
                            type="text"
                            id="phone"
                            onChange={handleAddress}
                            value={address ? address : ""}
                            maxLength={100}
                            required
                          />
                        </div>
                      </div>
                    </div>
                    <div className={styles.submitBtnCont}>
                      <button
                        className={styles.submitBtn}
                        onClick={handleEmpBtn}
                      >
                        Next
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className={styles.formContainer}>
                    <div className={styles.firstNameSOF}>
                      <div className={styles.firstNameLabel}>
                        Source of Funds*
                      </div>

                      <div className={styles.firstNameInputSOF}>
                        <select
                          name="source_of_funds"
                          id="source_of_funds"
                          value={sourceOfFunds ? sourceOfFunds : ""}
                          onChange={(e) => setSourceOfFunds(e.target.value)}
                          required
                        >
                          {sources.map((source) => (
                            <option key={source.value} value={source.name}>
                              {source.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    {/* names */}
                    {sourceOfFunds === "Salaried" ||
                    sourceOfFunds === "Investments" ||
                    sourceOfFunds === "Dividends or Profits from company" ? (
                      <div>
                        <div className={styles.formWrapper}>
                          <div className={styles.firstName}>
                            <div className={styles.firstNameLabel}>
                              Nature of Business*
                            </div>

                            <div className={styles.firstNameInput}>
                              <select
                                name="nature_of_business"
                                id="nature_of_business"
                                value={natureOfBusiness ? natureOfBusiness : ""}
                                onChange={(e) =>
                                  setNatureOfBusiness(e.target.value)
                                }
                                required
                              >
                                {businesses.map((business) => (
                                  <option
                                    key={business.value}
                                    value={business.name}
                                  >
                                    {business.name}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </div>
                          <div className={styles.formWrapper}>
                            <div className={styles.addressName}>
                              <div className={styles.firstNameLabel}>
                                Name of business* (max 50 character)
                              </div>
                              <div className={styles.addressNameInput}>
                                <input
                                  type="text"
                                  id="name_of_businesss"
                                  maxLength={50}
                                  value={nameOfBusiness ? nameOfBusiness : ""}
                                  onChange={handleNameOfBusiness}
                                  required
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className={styles.formWrapper}>
                          <div className={styles.addressName}>
                            <div className={styles.firstNameLabel}>
                              Country of Business*
                            </div>
                            <div className={styles.firstNameInputBusi}>
                              <select
                                type="text"
                                value={
                                  countryOfBusiness ? countryOfBusiness : ""
                                }
                                id="country_of_business"
                                onChange={(e) =>
                                  setCountryOfBusiness(e.target.value)
                                }
                                required
                              >
                                <option value="-1">Select a country</option>
                                {countriesFetched.map((country) => (
                                  <option
                                    key={country.id}
                                    // value={country.country_name}
                                  >
                                    {country.country_name}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div></div>
                    )}
                    {/* names */}
                    <div className={styles.submitBtnContEmp}>
                      <button
                        className={styles.submitBtn}
                        onClick={handlePersonalBtn}
                      >
                        Previous
                      </button>
                      <button type="submit" className={styles.submitBtn}>
                        Submit
                      </button>
                    </div>
                  </div>
                )}
              </form>

              {/* <div className={styles.submitBtnCont}>
                <button className={styles.submitBtn}>Next</button>
              </div> */}

              <ToastContainer />
              <Tooltip
                id="my-tooltip1"
                style={{
                  color: "#fff",
                  fontSize: "12px",
                }}
              />
            </div>
          </div>
        </div>
        <div className={styles.rightContainer}>
          <div className={styles.verificationGraphic}>
            <div className={styles.iconContainer}>
              <Icon icon="mdi:account-check" width="120" height="120" />
            </div>
            <h3 className={styles.graphicTitle}>Secure Verification</h3>
            <p className={styles.graphicSubtitle}>
              Complete your personal details to ensure account security and compliance
            </p>
            <div className={styles.featureList}>
              <div className={styles.featureItem}>
                <Icon icon="mdi:shield-check" width="24" height="24" />
                <span>Bank-level Security</span>
              </div>
              <div className={styles.featureItem}>
                <Icon icon="mdi:lock" width="24" height="24" />
                <span>Data Protection</span>
              </div>
              <div className={styles.featureItem}>
                <Icon icon="mdi:verified" width="24" height="24" />
                <span>Quick Verification</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PersonalDetails;
