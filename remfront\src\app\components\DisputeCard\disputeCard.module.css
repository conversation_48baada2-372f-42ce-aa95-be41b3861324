/* Modern Dispute Table Row Styles */

.tableRow {
  border-bottom: 1px solid #e2e8f0;
  transition: all 0.2s ease-in-out;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.tableRow:hover {
  background: #f8fafc;
}

.tableRow:last-child {
  border-bottom: none;
}

.expandedRow {
  background: #f0f9ff;
}

.tableCell {
  padding: 1rem 1.5rem;
  vertical-align: top;
  border-bottom: inherit;
}

/* Dispute Type Cell */
.disputeTypeCell {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.statusIndicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.statusIndicator.warning {
  background: #f59e0b;
}

.statusIndicator.success {
  background: #10b981;
}

.statusIndicator.error {
  background: #ef4444;
}

.statusIndicator.info {
  background: #3b82f6;
}

.disputeInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.disputeTitle {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
  line-height: 1.4;
}

.disputeStatus {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Order Number Cell */
.orderNumberCell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.orderNumber {
  font-size: 0.875rem;
  font-weight: 700;
  color: #1e293b;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.orderMeta {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.metaIcon {
  width: 0.875rem;
  height: 0.875rem;
  color: #9ca3af;
  flex-shrink: 0;
}

.metaText {
  font-size: 0.75rem;
  color: #6b7280;
}

/* Evidence Cell */
.evidenceCell {
  display: flex;
  align-items: center;
}

.evidenceBtn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: #eff6ff;
  border: 1px solid #bfdbfe;
  border-radius: 0.375rem;
  color: #1e40af;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  text-decoration: none;
}

.evidenceBtn:hover {
  background: #dbeafe;
  border-color: #93c5fd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.evidenceBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.evidenceIcon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.noEvidence {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #9ca3af;
  font-size: 0.75rem;
}

.noEvidenceIcon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* Comment Cell */
.commentCell {
  max-width: 300px;
}

.commentContent {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.commentText {
  color: #374151;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
  word-wrap: break-word;
}

.expandCommentBtn {
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  padding: 0;
  text-decoration: underline;
  transition: color 0.2s ease-in-out;
  align-self: flex-start;
}

.expandCommentBtn:hover {
  color: #2563eb;
}

.expandCommentBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 0.25rem;
}

.noComment {
  color: #9ca3af;
  font-size: 0.75rem;
  font-style: italic;
}

/* Date Cell */
.dateCell {
  display: flex;
  align-items: center;
}

.dateInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.dateText {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
}

.dateMeta {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.dateIcon {
  width: 0.875rem;
  height: 0.875rem;
  color: #9ca3af;
  flex-shrink: 0;
}

.timeDisplay {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* Modal Styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.modalContent {
  background: #ffffff;
  border-radius: 0.75rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modalTitle {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.modalCloseBtn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: #6b7280;
  transition: all 0.2s ease-in-out;
}

.modalCloseBtn:hover {
  background: #f3f4f6;
  color: #374151;
}

.modalCloseBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.modalCloseBtn svg {
  width: 1.25rem;
  height: 1.25rem;
}

.modalBody {
  padding: 1.5rem;
  overflow: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modalImage {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
}

.modalImageError {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: #6b7280;
  text-align: center;
}

.errorIcon {
  width: 3rem;
  height: 3rem;
  margin-bottom: 1rem;
  color: #ef4444;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tableCell {
    padding: 0.75rem 1rem;
  }

  .disputeTitle {
    font-size: 0.8125rem;
  }

  .orderNumber {
    font-size: 0.8125rem;
  }

  .commentCell {
    max-width: 200px;
  }

  .dateText {
    font-size: 0.8125rem;
  }

  .timeDisplay {
    font-size: 0.6875rem;
  }
}

@media (max-width: 640px) {
  .modalContent {
    margin: 0.5rem;
    max-width: calc(100vw - 1rem);
    max-height: calc(100vh - 1rem);
  }

  .modalHeader,
  .modalBody {
    padding: 1rem;
  }

  .modalTitle {
    font-size: 1rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .tableRow {
    border-bottom-width: 2px;
  }

  .actionBtn,
  .evidenceBtn,
  .expandCommentBtn {
    border-width: 2px;
  }

  .statusIndicator {
    border: 2px solid #ffffff;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .tableRow,
  .actionBtn,
  .evidenceBtn,
  .expandCommentBtn,
  .modalCloseBtn {
    transition: none;
  }

  .actionBtn:hover,
  .evidenceBtn:hover {
    transform: none;
  }
}

/* Focus and accessibility improvements */
.evidenceBtn:focus-visible,
.expandCommentBtn:focus-visible,
.modalCloseBtn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .modalOverlay {
    display: none;
  }

  .tableRow {
    break-inside: avoid;
  }
}