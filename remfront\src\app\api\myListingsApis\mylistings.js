import axios from "axios";

const BaseURL = process.env.NEXT_PUBLIC_Base_URL;

if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}

export const getListings = async () => {
  if (typeof window !== "undefined") {
    var token1 = sessionStorage.getItem("user");
  }

  const res = await axios({
    // url: `${BaseURL}/get-listings/?page_size=10&page=2`,
    url: `${BaseURL}/get-listings/?user_data=true`,
    headers: {
      Authorization: `Bearer ${token1}`,
    },
    method: "GET",
  });
  return res;
};

export const disableOneListing = async (listingId) => {
  const res = await axios({
    method: "DELETE",
    url: `${BaseURL}/delete-listings/${listingId}`,
    // url: BaseURL + "/delete-listings/" + listingId,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};

export const disableAllListings = async () => {
  const res = await axios({
    method: "DELETE",
    url: `${BaseURL}/disabled-enabled-listings/?flag=disable`,
    // url: `${BaseURL}/disabled-enabled-listings/?flag=disabled`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
export const enableAllListings = async () => {
  const res = await axios({
    method: "DELETE",
    url: `${BaseURL}/disabled-enabled-listings/?flag=enable`,
    // url: `${BaseURL}/disabled-enabled-listings/?flag=disabled`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
