import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>View,
  FlatList,
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import { Ionicons } from "@expo/vector-icons";
import Layout from "@/components/Layout";
import customFetchWithToken from "./utils/axiosInterceptor";
import { Link } from "expo-router";
import TradeReviewModal from "@/components/TradeReviewModal";
import { toastConfig, showToastSuccess, showToastError } from "@/hooks/toast";

interface User {
  email: string;
  firstname: string;
  id: number;
  lastname: string;
  username: string;
}

interface Listing {
  available_liquidity: number;
  buyer_trade_fee: number | null;
  country_from: string | null;
  country_to: string | null;
  created_date: string;
  currency_accepted: number;
  currency_payout: number;
  details: string;
  final_trade_fee: number;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  max_liquidity: number;
  min_liquidity: number;
  payin_option: number;
  payout_option: number;
  rf_commission: number | null;
  terms_and_conditions: string;
  time_limit: number;
  trade_fee: number;
  unique_id: string;
  updated_date: string;
  user: number;
  user_payment_option: number;
}

interface TradeRequest {
  available_liquidity: number;
  chat_id: string;
  created_date: string;
  expiry_date: string;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  listing: Listing;
  max_liquidity: number;
  min_liquidity: number;
  order_number: string;
  order_status:
    | "pending"
    | "expired"
    | "rejected"
    | "ongoing"
    | "completed"
    | "notified";
  payin_currency: string;
  payin_option: string;
  payment_proof: string | null;
  payout_currency: string;
  payout_option: string;
  peer_id: User;
  recipient_id: number | null;
  time_duration: number;
  trade_amount: number;
  updated_date: string;
  user: User;
}

type TradeRequestArray = TradeRequest[];

const getStatusColor = (status: string) => {
  switch (status) {
    case "pending":
      return "orange";
    case "expired":
      return "#1b1a1a";
    case "rejected":
      return "orangered";
    case "ongoing":
      return "#4153ed";
    case "completed":
      return "#4aa874";
    case "notified":
      return "#2d704b";
    default:
      return "gray";
  }
};

const TransactionCard = ({
  data,
  onOpenReviewModal,
}: {
  data: TradeRequest;
  onOpenReviewModal: (tradeId: number) => void;
}) => {
  const statusColor = getStatusColor(data?.order_status?.toLowerCase());
  const timestamp = data?.created_date;
  const datePart = timestamp
    ? new Date(timestamp).toISOString().split("T")[0]
    : "";
  const timePart = timestamp
    ? new Date(timestamp).toTimeString().split(" ")[0]
    : "";

  return (
    <View className="border-t border-b border-gray-200 py-4">
      {/* Status Tag */}
      <View
        style={{ backgroundColor: statusColor }}
        className="rounded-md py-2 mb-4"
      >
        <Text className="text-white text-center">{data?.order_status}</Text>
      </View>

      {/* Transaction Details */}
      {[
        { label: "Order Number", value: data?.order_number },
        { label: "Listing Id", value: data?.listing?.id },
        { label: "Date", value: datePart },
        { label: "Time", value: timePart },
        { label: "Trade Amount", value: data?.trade_amount },
        {
          label: "Indicative FX Rate",
          value: data?.indicative_fx_rate.toFixed(2),
        },
        { label: "Payin Currency", value: data?.payin_currency },
        { label: "Payin Payment Method", value: data?.payin_option },
        { label: "Payout Currency", value: data?.payout_currency },
        { label: "Payout Payment Method", value: data?.payout_option },
        { label: "Peer Id", value: data?.peer_id?.id },
      ].map((item, index) => (
        <View
          key={index}
          className="flex-row justify-between py-1 font-pmedium"
        >
          <Text className="text-gray-600 font-pmedium">{item.label}</Text>
          <Text className="text-black font-pmedium">{item.value}</Text>
        </View>
      ))}

      {/* Navigation Button */}
      {data?.order_status === "pending" && (
        <Link
          href={`/trade/${data?.order_number}`}
          className="mt-4 bg-gray-100 py-3 rounded-md"
        >
          <Text className="text-center text-gray-800 font-pmedium">
            Go To trade Page
          </Text>
        </Link>
      )}
      <TouchableOpacity
        onPress={() => {
          onOpenReviewModal(data.id);
        }}
      >
        <View className="mt-4 bg-gray-100 py-3 rounded-md">
          <Text className="text-center text-gray-800 font-pmedium">TEST</Text>
        </View>
      </TouchableOpacity>
    </View>
  );
};

export default function Component() {
  const [selectedFilter, setSelectedFilter] = useState("All");
  const [searchText, setSearchText] = useState("");
  const [tradeReqArr, setTradeReqArr] = useState<TradeRequestArray>([]);
  const [tradeStatus, setTradeStatus] = useState("");
  const [tabHeader, setTabHeader] = useState("");
  const [searchByOrderNo, setSearchByOrderNo] = useState("");
  const [loading, setLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const ITEMS_PER_PAGE = 10;
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedTradeId, setSelectedTradeId] = useState<number | null>(null);

  const handleAllTradeReq = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setLoading(true);
      setTradeReqArr([]);
      setPage(1);
    }
    setIsFetching(true);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?page=${reset ? 1 : page}&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr((prev) =>
        reset ? res.data.results : [...prev, ...res.data.results]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
      setTabHeader("All Results");
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  const handleSenderTradeReq = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setLoading(true);
      setTradeReqArr([]);
      setPage(1);
    }
    setIsFetching(true);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?type=sender&page=${
          reset ? 1 : page
        }&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr((prev) =>
        reset ? res.data.results : [...prev, ...res.data.results]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
      setTabHeader("Sender Results");
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  const handlePeerTradeReq = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setLoading(true);
      setTradeReqArr([]);
      setPage(1);
    }
    setIsFetching(true);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?type=peer&page=${
          reset ? 1 : page
        }&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr((prev) =>
        reset ? res.data.results : [...prev, ...res.data.results]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
      setTabHeader("Peer Results");
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      setIsFetching(false);
    }
  };

  const handlePress = (reset = false) => {
    if (selectedFilter === "All") {
      handleAllTradeReq(reset);
    } else if (selectedFilter === "Peer") {
      handlePeerTradeReq(reset);
    } else {
      handleSenderTradeReq(reset);
    }
  };

  const searchByOrderNoHandler = async (text: string) => {
    setLoading(true);
    setSearchByOrderNo(text);
    setHasMore(false);
    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?order_number=${text}&status=&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr(res.data.results);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleTradeReqByStatus = async (value: string) => {
    setLoading(true);
    setTradeStatus(value);
    setPage(1);
    setHasMore(true);

    try {
      const res = await customFetchWithToken.get(
        `/get-trade-request/?status=${value}&page=1&limit=${ITEMS_PER_PAGE}`
      );
      setTradeReqArr(res.data.results);
      setHasMore(res.data.next !== null);
      setPage(2);
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenReviewModal = (tradeId: number) => {
    setSelectedTradeId(tradeId);
    setIsModalVisible(true);
  };

  const handleSubmitReview = async (rating: number, comment: string) => {
    try {
      console.log(
        "Submitting review:",
        { rating, comment },
        "for trade:",
        selectedTradeId
      );
      // The actual submission is now handled in the TradeReviewModal component
      showToastSuccess("Review submitted successfully!");
    } catch (error) {
      console.error("Error submitting review:", error);
      showToastError("Failed to submit review. Please try again.");
    } finally {
      setIsModalVisible(false);
      setSelectedTradeId(null);
    }
  };

  useEffect(() => {
    handlePress(true);
  }, [selectedFilter]);

  const renderHeader = () => (
    <View>
      {/* Header */}
      <View className="flex-row justify-between items-center mb-6">
        <Text className="text-2xl font-pbold">History</Text>
      </View>

      {/* Search Bar */}
      <View className="bg-gray-100 rounded-md mb-4 font-pmedium">
        <TextInput
          placeholder="Search By Order Number"
          className="p-4"
          maxLength={10}
          value={searchByOrderNo}
          onChangeText={(text) => {
            searchByOrderNoHandler(text);
          }}
        />
      </View>

      {/* Status Dropdown */}
      <View className="bg-gray-100 rounded-md p-4 mb-4">
        <Picker
          selectedValue={tradeStatus}
          onValueChange={(value: string) => handleTradeReqByStatus(value)}
        >
          <Picker.Item label="All Status" value="" />
          <Picker.Item label="Pending" value="pending" />
          <Picker.Item label="Completed" value="completed" />
          <Picker.Item label="Reject" value="rejected" />
          <Picker.Item label="Expired" value="expired" />
          <Picker.Item label="Ongoing" value="ongoing" />
        </Picker>
      </View>

      {/* Filter Buttons */}
      <View className="flex-row justify-start space-x-4 mb-6">
        {["All", "Peer", "Sender"].map((filter) => (
          <TouchableOpacity
            key={filter}
            onPress={() => {
              setSelectedFilter(filter);
              setPage(1);
              setHasMore(true);
            }}
            className={`px-6 py-2 rounded-md border font-pmedium ${
              selectedFilter === filter ? "border-blue-500" : "border-gray-300"
            }`}
          >
            <Text
              className={`${
                selectedFilter === filter
                  ? "text-blue-500 font-pmedium"
                  : "text-gray-600 font-pmedium"
              }`}
            >
              {filter}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Results Header */}
      <TouchableOpacity>
        <Text className="text-xl font-pbold mb-4">
          {selectedFilter === "All"
            ? "All Results"
            : selectedFilter === "Peer"
            ? "Peer Results"
            : "Sender Results"}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <Layout>
      <SafeAreaView className="flex-1 bg-white">
        <View className="flex-1 p-4">
          <FlatList
            data={tradeReqArr}
            keyExtractor={(item, index) => String(index)}
            ListHeaderComponent={renderHeader()}
            renderItem={({ item }) => (
              <TransactionCard
                data={item}
                onOpenReviewModal={handleOpenReviewModal}
              />
            )}
            onEndReached={() => {
              if (!searchByOrderNo && !loading) {
                handlePress(false);
              }
            }}
            onEndReachedThreshold={0.5}
            ListEmptyComponent={
              loading ? (
                <Text className="text-center font-pbold font-xl">
                  Loading...
                </Text>
              ) : (
                <Text>No data available</Text>
              )
            }
            ListFooterComponent={
              isFetching && !loading ? (
                <View className="py-4">
                  <ActivityIndicator size="small" color="#0000ff" />
                </View>
              ) : null
            }
            initialNumToRender={ITEMS_PER_PAGE}
            maxToRenderPerBatch={5}
            windowSize={7}
            updateCellsBatchingPeriod={50}
            removeClippedSubviews={true}
          />
        </View>
        {selectedTradeId !== null && (
          <TradeReviewModal
            isVisible={isModalVisible}
            onClose={() => {
              setIsModalVisible(false);
              setSelectedTradeId(null);
            }}
            onSubmit={handleSubmitReview}
            tradeOrderId={String(selectedTradeId)}
          />
        )}
      </SafeAreaView>
    </Layout>
  );
}
