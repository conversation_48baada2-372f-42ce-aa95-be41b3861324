/* Listing Card */
.listingCard {
    background-color: #ffffff;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
    overflow: hidden;
    height: fit-content;
}

.listingCard:hover {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

/* Card Header */
.cardHeader {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid #f1f5f9;
}

.listingId {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.idLabel {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.idValue {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
}

/* Card Actions */
.cardActions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.editButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    color: #64748b;
    cursor: pointer;
    transition: all 0.2s ease;
}

.editButton:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    color: #334155;
    border-color: #cbd5e1;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.1);
}

.editButton:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #3b82f6;
}

.toggleContainer {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toggleLabel {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.toggleText {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    min-width: 3.5rem;
    text-align: right;
}

.toggleButton {
    position: relative;
    width: 3rem;
    height: 1.5rem;
    border: none;
    border-radius: 0.75rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #e2e8f0;
}

.toggleButton:focus {
    outline: 2px solid transparent;
    outline-offset: 2px;
    box-shadow: 0 0 0 2px #ffffff, 0 0 0 4px #3b82f6;
}

.toggleActive {
    background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
    box-shadow: 0 2px 8px 0 rgba(16, 185, 129, 0.3);
}

.toggleDisabled {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    box-shadow: 0 2px 8px 0 rgba(239, 68, 68, 0.3);
}

.toggleSlider {
    position: absolute;
    top: 0.125rem;
    left: 0.125rem;
    width: 1.25rem;
    height: 1.25rem;
    background-color: #ffffff;
    border-radius: 50%;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.toggleActive .toggleSlider {
    transform: translateX(1.5rem);
}

.toggleDisabled .toggleSlider {
    transform: translateX(1.5rem);
}

/* Card Content */
.cardContent {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* Currency Section */
.currencySection {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-radius: 0.75rem;
    padding: 1rem;
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.currencySection::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #10b981, #f59e0b, #ef4444);
    opacity: 0.6;
}

.currencyPair {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
}

.currencyItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.currencyLabel {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.currencyValue {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    min-width: 4rem;
    text-align: center;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.currencyValue:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.15);
}

.currencyArrow {
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: #ffffff;
    border-radius: 50%;
    border: 1px solid #e2e8f0;
}

/* Details Grid */
.detailsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
}

.detailItem {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    padding: 0.75rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.detailLabel {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.detailValue {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1e293b;
    word-break: break-word;
}

/* Payment Methods */
.paymentMethods {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.paymentMethod {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.paymentLabel {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.paymentValue {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1e293b;
    text-align: right;
}

/* Terms Section */
.termsSection {
    border-top: 1px solid #e2e8f0;
    padding-top: 1rem;
}

.termsLabel {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.5rem;
    display: block;
}

.termsText {
    font-size: 0.875rem;
    color: #374151;
    line-height: 1.5;
    margin: 0;
    padding: 0.75rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    max-height: 4rem;
    overflow-y: auto;
}

/* Responsive Design */
@media (max-width: 640px) {
    .cardHeader {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .cardActions {
        justify-content: space-between;
    }

    .currencyPair {
        flex-direction: column;
        gap: 0.75rem;
    }

    .currencyArrow {
        transform: rotate(90deg);
    }

    .detailsGrid {
        grid-template-columns: 1fr;
    }

    .paymentMethod {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .paymentValue {
        text-align: left;
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    .detailsGrid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Focus and Accessibility */
.listingCard:focus-within {
    box-shadow: 0 0 0 2px #3b82f6;
}

/* Animation for loading states */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.listingCard[aria-busy="true"] {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .listingCard {
        border-width: 2px;
    }

    .detailItem,
    .currencyValue,
    .paymentMethod,
    .termsText {
        border-width: 2px;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .listingCard,
    .editButton,
    .toggleButton,
    .toggleSlider {
        transition: none;
    }

    .listingCard:hover {
        transform: none;
    }
}