/* Modern HistoryCard Component Styles */

/* Main Card Container */
.card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin-bottom: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  position: relative;
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: #d1d5db;
}

.card:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Card Header */
.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px 24px 16px;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  border-bottom: 1px solid #f1f5f9;
}

.orderInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.orderNumber {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
  letter-spacing: -0.025em;
}

.relativeTime {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
  background: #f1f5f9;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  margin-top: 4px;
}

.statusContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.statusIcon {
  font-size: 14px;
}

.statusText {
  white-space: nowrap;
}

/* Status Badge Variants */
.statusPending {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border-color: #f59e0b;
}

.statusExpired {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
  border-color: #9ca3af;
}

.statusOngoing {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  color: #1e40af;
  border-color: #3b82f6;
}

.statusRejected {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  color: #b91c1c;
  border-color: #ef4444;
}

.statusCompleted {
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  color: #065f46;
  border-color: #10b981;
}

.statusNotified {
  background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
  color: #3730a3;
  border-color: #6366f1;
}

.statusCancelled {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #374151;
  border-color: #9ca3af;
}

.dateTimeContainer {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
}

.date,
.time {
  font-size: 11px;
  color: #64748b;
  font-weight: 500;
}

.date {
  font-weight: 600;
}

/* Card Content */
.cardContent {
  padding: 24px;
  flex: 1;
  background: #ffffff;
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.infoSection {
  background: #f8fafc;
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.infoSection:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.sectionTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.sectionIcon {
  font-size: 16px;
}

.infoList {
  margin: 0;
  padding: 0;
}

.infoRow {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 4px 0;
}

.infoRow:last-child {
  margin-bottom: 0;
}

.infoLabel {
  font-size: 13px;
  color: #64748b;
  font-weight: 500;
  margin: 0;
  flex-shrink: 0;
}

.infoValue {
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
  text-align: right;
  margin: 0 0 0 16px;
  word-break: break-word;
}

.currencyCode {
  background: #3b82f6;
  color: #ffffff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 700;
  letter-spacing: 0.025em;
}



/* Card Actions */
.cardActions {
  padding: 20px 24px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.primaryButton,
.secondaryButton {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  white-space: nowrap;
  min-width: 140px;
  justify-content: center;
}

.primaryButton {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: #ffffff;
  box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.3);
}

.primaryButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px -1px rgba(59, 130, 246, 0.4);
}

.primaryButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondaryButton {
  background: #ffffff;
  color: #374151;
  border: 2px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.secondaryButton:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #d1d5db;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.buttonIcon {
  font-size: 16px;
}

/* Pending Actions */
.pendingActions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.pendingMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  text-align: center;
}

.messageIcon {
  font-size: 16px;
}

.buttonGroup {
  display: flex;
  gap: 12px;
  width: 100%;
  max-width: 300px;
}

.acceptButton,
.rejectButton {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
  border: 2px solid transparent;
}

.acceptButton {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: #ffffff;
  box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
}

.acceptButton:hover:not(:disabled) {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px -1px rgba(16, 185, 129, 0.4);
}

.acceptButton:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.rejectButton {
  background: #ffffff;
  color: #ef4444;
  border-color: #ef4444;
  box-shadow: 0 1px 3px 0 rgba(239, 68, 68, 0.1);
}

.rejectButton:hover:not(:disabled) {
  background: #fef2f2;
  border-color: #dc2626;
  color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2);
}

.rejectButton:disabled {
  background: #f9fafb;
  color: #9ca3af;
  border-color: #e5e7eb;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Completed and Inactive Actions */
.completedActions,
.inactiveActions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.completedMessage,
.inactiveMessage {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

.completedMessage {
  color: #065f46;
}

.inactiveMessage {
  color: #6b7280;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .infoGrid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .card {
    margin-bottom: 12px;
    border-radius: 12px;
  }

  .cardHeader {
    padding: 16px 20px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .statusContainer {
    align-self: stretch;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .dateTimeContainer {
    align-items: flex-end;
  }

  .cardContent {
    padding: 20px;
  }

  .infoSection {
    padding: 12px;
  }

  .sectionTitle {
    font-size: 13px;
  }

  .infoRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
  }

  .infoValue {
    text-align: left;
    margin: 0;
  }

  .cardActions {
    padding: 16px 20px;
  }

  .buttonGroup {
    flex-direction: column;
    max-width: none;
  }

  .primaryButton,
  .secondaryButton {
    width: 100%;
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .cardHeader {
    padding: 12px 16px;
  }

  .cardContent {
    padding: 16px;
  }

  .cardActions {
    padding: 12px 16px;
  }

  .orderNumber {
    font-size: 16px;
  }

  .statusBadge {
    padding: 4px 8px;
    font-size: 11px;
  }

  .statusIcon {
    font-size: 12px;
  }

  .infoSection {
    padding: 10px;
  }

  .sectionTitle {
    font-size: 12px;
    gap: 6px;
  }

  .sectionIcon {
    font-size: 14px;
  }

  .infoLabel,
  .infoValue {
    font-size: 12px;
  }

  .primaryButton,
  .secondaryButton,
  .acceptButton,
  .rejectButton {
    padding: 10px 14px;
    font-size: 13px;
  }

  .buttonIcon {
    font-size: 14px;
  }
}

/* Focus and accessibility improvements */
.primaryButton:focus-visible,
.secondaryButton:focus-visible,
.acceptButton:focus-visible,
.rejectButton:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }

  .statusBadge {
    border-width: 2px;
  }

  .infoSection {
    border-width: 2px;
  }

  .primaryButton,
  .secondaryButton,
  .acceptButton,
  .rejectButton {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .card,
  .infoSection,
  .primaryButton,
  .secondaryButton,
  .acceptButton,
  .rejectButton,
  .statusBadge {
    transition: none;
  }

  .card:hover,
  .primaryButton:hover:not(:disabled),
  .secondaryButton:hover:not(:disabled),
  .acceptButton:hover:not(:disabled),
  .rejectButton:hover:not(:disabled) {
    transform: none;
  }
}

/* Print styles */
@media print {
  .card {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
    margin-bottom: 20px;
  }

  .cardActions {
    display: none;
  }

  .statusBadge {
    background: transparent !important;
    color: #000 !important;
    border: 1px solid #000 !important;
  }
}