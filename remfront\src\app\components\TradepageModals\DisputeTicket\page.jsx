"use client";
import React from "react";
import styles from "../upload.module.css";

const page = ({ setIsOpen2 }) => {
  return (
    <>
      <h2
        ref={(subtitle) => (subtitle = subtitle)}
        className={styles.modalHeader2}
      >
        Dispute Ticket : 2536736
        {/* <button>close</button> */}
      </h2>
      {/* <div>I am a modal</div> */}
      <div className={styles.issueSelect2}>
        Both parties are notified by email about the dispute
      </div>
      <div className={styles.optionsBox}></div>
      <div className={styles.submitBtnWrapper2}>
        <button
          className={styles.disputeSubmit}
          onClick={() => setIsOpen2(false)}
        >
          Close
        </button>
        {/* <button
      className={styles.disputeSubmit}
      onClick={openModal2}
    >
      Submit
    </button> */}
      </div>
    </>
  );
};

export default page;
