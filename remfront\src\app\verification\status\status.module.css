@font-face {
  font-family: 'Poppins';
  font-weight: 300;
  src: url('../../../../public/fonts/Poppins-Light.ttf') format('truetype');
}

.mainContainer {
  width: 100%;
  height: 100vh;
  display: flex;
  max-width: 100vw;
  overflow: hidden;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    max-width: 100%;
    height: auto;
    min-height: 100vh;
    overflow-x: hidden;
    overflow-y: auto;
  }

  @media screen and (min-width: 1500px) {
    height: 100vh;
  }
}

.topContainer {
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    display: block;
    position: relative;
  }

  @media screen and (min-width: 577px) {
    display: none;
  }
}



.leftContainer {
  width: 65%;
  height: 100%;
  display: flex;
  max-width: 100%;
  box-sizing: border-box;
  overflow-y: auto;

  @media screen and (max-width: 576px) {
    width: 100%;
    max-width: 100%;
    height: auto;
    overflow-y: visible;
  }
}

.leftWrapper {
  padding: 80px;
  width: 100%;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    padding: 30px 20px;
    max-width: 100%;
    overflow-x: hidden;
  }
}

.silverWrapper {
  padding: 30px;
  width: 80%;
  margin-left: auto;
  margin-right: auto;
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  border: 1px solid black;
  position: relative;
  border-radius: 5px;
  margin-bottom: 20px;

  @media screen and (max-width: 577px) {
    width: 80%;
  }
}

.goldWrapper {
  padding: 30px;
  width: 80%;
  margin: auto;
  display: flex;
  flex-direction: column;
  border: 1px solid gold;
  position: relative;
  border-radius: 5px;
}

.silverTag {
  width: 200px;
  height: 30px;
  background-color: silver;
  position: absolute;
  top: -15px;
  text-align: center;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}

.goldTag {
  width: 200px;
  height: 30px;
  background-color: gold;
  position: absolute;
  top: -15px;
  text-align: center;
  font-weight: 600;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
}

.backBtn {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 50px;
  margin-bottom: 20px;
  font-size: 14px;

  @media screen and (max-width: 576px) {
    position: absolute;
    top: 10px;
    left: 14px;
  }
}

.backimg {
  margin-right: 10px;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    margin-right: 0px;
  }
}

.mainButtonsWrapper {
  display: flex;
  width: 100%;
  margin-bottom: 25px;
  font-family: Poppins;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
}

.personalBtn {
  color: #4153ed;
  border: 1px solid #4153ed;
  border-radius: 5px;
  padding: 8px 10px 8px 10px;
  margin-right: 8px;
  font-size: 14px;
}

.employBtn {
  color: #000;
  border: 1px solid black;
  border-radius: 5px;
  padding: 8px 10px 8px 10px;
}

.heading {
  color: #4153ed;
  font-family: Poppins;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  margin-bottom: 30px;
}

.formWrapper {
  width: 100%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  column-gap: 24px;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    flex-direction: column;
    margin-bottom: 20px;
    column-gap: 0;
    row-gap: 20px;
  }
}

.firstName {
  width: 100%;
  box-sizing: border-box;

  @media screen and (max-width: 576px) {
    width: 100%;
    margin: 0;
  }
}

.firstNameOther {
  width: 96%;


  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.firstNameLabel {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 10px;
  word-wrap: break-word;

  @media screen and (max-width: 576px) {
    margin-bottom: 8px;
    font-size: 15px;
  }
}

.addressName {
  width: 100%;
}

.lastName {
  width: 92%;
}

.firstNameInput input {
  border: none;
  background-color: transparent;
  flex: 1;
  height: 100%;
  min-width: 0;
  padding: 0 16px;
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: #2d3748;
  box-sizing: border-box;
  outline: none;

  @media screen and (max-width: 576px) {
    font-size: 16px;
    padding: 0 14px;
  }
}

.firstNameInput input::placeholder {
  color: #a0aec0;
  font-weight: 300;
}

.firstNameInputKYC input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width : 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInput select {
  border: none;
  background-color: transparent;
  width: 100%;
  padding: 0 16px;
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: #2d3748;
  box-sizing: border-box;
  outline: none;
  cursor: pointer;

  @media screen and (max-width: 576px) {
    font-size: 16px;
    padding: 0 14px;
  }
}

.lastNameInput input {
  border: none;
}

.firstNameInputKYC select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.lastNameInput input {
  border: none;
}

.firstNameInput {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: #ffffff;
  border: 2px solid #e8eaed;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  min-width: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  @media screen and (max-width: 576px) {
    width: 100%;
    height: 52px;
    border-radius: 10px;
  }
}

.firstNameInput:hover {
  border-color: #4153ed;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.1);
}

.firstNameInput:focus-within {
  border-color: #4153ed;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.firstNameInputOther {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  background: #ffffff;
  border: 2px solid #e8eaed;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);

  @media screen and (max-width: 576px) {
    width: 100%;
    height: 52px;
    border-radius: 10px;
  }
}

.firstNameInputOther:hover {
  border-color: #4153ed;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.1);
}

.firstNameInputOther:focus-within {
  border-color: #4153ed;
  box-shadow: 0 0 0 3px rgba(65, 83, 237, 0.1);
}

.firstNameInputOther input {
  border: none;
  background-color: transparent;
  width: 100%;
  height: 100%;
  padding: 0 16px;
  font-family: 'Poppins', sans-serif;
  font-size: 15px;
  font-weight: 400;
  color: #2d3748;
  box-sizing: border-box;
  outline: none;

  @media screen and (max-width: 576px) {
    font-size: 16px;
    padding: 0 14px;
  }
}

.firstNameInputOther input::placeholder {
  color: #a0aec0;
  font-weight: 300;
}

.firstNameInputKYC {
  width: 100%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.kycSubmitBtn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.kycSubmitBtn button {
  color: #fff;
  background-color: #4153ed;
  height: 40px;
  padding: 10px 50px;
  border-radius: 5px;
  outline: none;
  border: 1px solid #4153ed;
  cursor: pointer;

}

.lastNameInput {
  width: 92%;
  border: 1px solid black;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
}

.emailBtn {
  border-radius: 0px 2px 2px 0px;
  background: #f5f5f5;
  border: 1px solid #ebebeb;
  border-left: 2px solid #c4c3c3;
  font-size: 8px;
}

.calender {
  display: flex;
  justify-content: center;
  align-items: center;
}

.addressNameInput input {
  cursor: pointer;
  background-color: #f9f9f9;
  width: 96%;
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  position: relative;
}

.addressNameInput {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #c4c3c3;

}





.fileInput[type='file'] {
  opacity: 1
}



.upload {
  position: absolute;
  right: 30px;
  top: 12px;
}

/* submitBtn */

.submitBtnCont {
  display: flex;
  justify-content: center;
  align-items: center;
}

.submitBtnCont1 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.submitBtn {
  background-color: #fff;
  padding: 15px 50px;
  color: #4153ed;
  gap: 10px;
  font-weight: 600;
  border-color: #4153ed;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
}

.nextBtnCont {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}

.nextsubmitBtn {
  background-color: #4153ed;
  padding: 15px 120px;
  color: white;
  gap: 10px;
  font-weight: 600;
  border-color: #4153ed;
  outline: none;
  border-radius: 3px;
  margin-bottom: 30px;
  cursor: pointer;
}

/* submitBtn */


.rightContainer {
  width: 35%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4153ed 100%);
  overflow: hidden;

  @media screen and (max-width: 576px) {
    display: none;
  }
}

.firstNameInputKYC input {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;

  @media screen and (max-width : 576px) {
    margin-bottom: 5px;
  }
}

.firstNameInputKYC select {
  border: none;
  background-color: #f9f9f9;
  width: 100%;
  padding-left: 10px;
  padding-right: 10px;
}

.lastNameInput input {
  border: none;
}

.firstNameInputKYC {
  width: 100%;
  height: 40px;
  border-radius: 2px;
  background: #f9f9f9;
  display: flex;

  @media screen and (max-width : 576px) {
    width: 100%;
  }
}

.kycSubmitBtn {
  display: flex;
  align-items: center;
  justify-content: center;
}

.kycSubmitBtn button {
  color: #fff;
  background-color: #4153ed;
  height: 40px;
  padding: 10px 50px;
  border-radius: 5px;
  outline: none;
  border: 1px solid #4153ed;


}

.submitBtnCont1 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10px;
}

.submitBtn1 {
  background: linear-gradient(135deg, #4153ed 0%, #2196f3 100%);
  border: none;
  padding: 0 32px;
  min-width: 200px;
  color: white;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 500;
  border-radius: 12px;
  height: 52px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.2);
  outline: none;

  @media screen and (max-width: 576px) {
    padding: 0 24px;
    min-width: 180px;
    height: 48px;
    font-size: 15px;
    border-radius: 10px;
  }
}

.submitBtn1:hover {
  background: linear-gradient(135deg, #3142dc 0%, #1976d2 100%);
  box-shadow: 0 6px 16px rgba(65, 83, 237, 0.3);
}

.submitBtn1:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(65, 83, 237, 0.2);
}

.manualKycContainer {
  width: 70%;
  margin: 20px auto 10px auto;
}

.kycUpload {
  margin-top: 20px;
}

.others {
  width: 100%;
}

/* Modern Mobile Header Styles */
.mobileHeader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  background: linear-gradient(135deg, #4153ed 0%, #2196f3 50%, #03a9f4 100%);
  color: white;
  text-align: center;
}

.verificationIcon {
  margin-bottom: 15px;
  color: white;
  opacity: 0.9;
}

.mobileTitle {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 500;
  margin: 0;
  color: white;
}

/* Modern Right Container Styles */
.verificationGraphic {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 60px 40px;
  width: 100%;
  height: 100%;
  color: white;
}

.iconContainer {
  margin-bottom: 30px;
  opacity: 0.9;
}

.graphicTitle {
  font-family: 'Poppins', sans-serif;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 15px;
  color: white;
}

.graphicSubtitle {
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
  font-weight: 300;
  margin-bottom: 40px;
  line-height: 1.5;
  opacity: 0.9;
  max-width: 300px;
}

.featureList {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
}

.featureItem {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: white;
  opacity: 0.9;
}