"use client";
import { useState } from "react";
import { useParams } from "next/navigation";
import Image from "next/image";
import styles from "./forgotpass.module.css";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { setNewPassApi } from "@/app/api/onboarding/forgotPassword";
import RightSideAnimation from "../../../components/RightSideAnimation/RightSideAnimation";

require("dotenv").config();

const Reset_Pass = () => {
  const router = useRouter();
  const { id } = useParams();

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passDialogue, setPassDialogue] = useState("");
  const [confirmPassDialogue, setConfirmPassDialogue] = useState("");
  const [btnIsActive, setBtnIsActive] = useState(false);
  const [isPasswordValid, setIsPasswordValid] = useState(false);
  const [isPasswordsMatch, setIsPasswordsMatch] = useState(false);

  const handlePassword = (e) => {
    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;

    const newPassword = e.target.value;
    const isValid = passwordRegex.test(newPassword);

    if (!isValid) {
      setPassDialogue(
        "Password must be 8-25 characters, with at least one uppercase, one lowercase, one digit, and one special character."
      );
      setIsPasswordValid(false);
    } else {
      setPassDialogue("");
      setIsPasswordValid(true);
    }

    setPassword(newPassword);

    // Check if passwords match when password changes
    if (confirmPassword && newPassword && newPassword === confirmPassword && isValid) {
      setConfirmPassDialogue("Passwords match");
      setIsPasswordsMatch(true);
    } else if (confirmPassword && newPassword && newPassword !== confirmPassword) {
      setConfirmPassDialogue("Passwords do not match.");
      setIsPasswordsMatch(false);
    }
  };

  const handleConfirmPassword = (e) => {
    const confirmPass = e.target.value;
    setConfirmPassword(confirmPass);

    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;
    const isPasswordValid = passwordRegex.test(password);

    if (password && confirmPass && password !== confirmPass) {
      setConfirmPassDialogue("Passwords do not match.");
      setIsPasswordsMatch(false);
    } else if (password && confirmPass && password === confirmPass && isPasswordValid) {
      setConfirmPassDialogue("Passwords match");
      setIsPasswordsMatch(true);
    } else {
      setConfirmPassDialogue("");
      setIsPasswordsMatch(false);
    }
  };

  const BaseURL = process.env.NEXT_PUBLIC_RESETPASS_URL;

  const URL = `${BaseURL}/change-password/${id}`;

  const Data = {
    new_password: password,
    confirm_password: confirmPassword,
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    setPassDialogue("");
    setConfirmPassDialogue("");
    setBtnIsActive(true);

    const passwordRegex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,25}$/;

    if (!passwordRegex.test(password)) {
      setPassDialogue(
        "Password must be 8-25 characters, with at least one uppercase, one lowercase, one digit, and one special character."
      );
      setBtnIsActive(false);
      return;
    }

    if (password !== confirmPassword) {
      setConfirmPassDialogue("Passwords do not match.");
      setBtnIsActive(false);
      return;
    }

    try {
      const res = await setNewPassApi(URL, Data);

      if (res.status === 200) {
        toast.success(res.data.message);
        setTimeout(() => router.push("/sign/login"), 1500);
      } else {
        toast.error("Password reset failed.");
      }
    } catch (error) {
      console.log(error);
      toast.error(error.response?.data?.message || "An error occurred. Please try again.");
    } finally {
      setBtnIsActive(false);
    }
  };

  return (
    <main className={styles.main}>
      <div className={styles.leftContainer}>
        <div className={styles.leftBody}>
          <div className={styles.logo}>Remflow</div>
          {/* <div className={styles.logo}>Remflow new pass : </div> */}
          <h1 className={styles.heading}>Set New Password</h1>
          <div className={styles.subHeading}>
            Please enter your new Password and both should match.
          </div>

          <form action="" onSubmit={onSubmit}>
            <div className={styles.passwordContainer}>
              <div className={styles.password}>
                <label className={styles.nameLabel} htmlFor="password">
                  New password
                </label>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  maxLength={260}
                  onChange={handlePassword}
                  placeholder="Make sure it contains letters and numbers, at least 8 characters long."
                  className={isPasswordValid ? styles.inputSuccess : ""}
                  required
                />
                <span
                  className={styles.hidePass}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {!showPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      width={20}
                      height={20}
                    >
                      <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 576 512"
                      width={20}
                      height={20}
                    >
                      <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                    </svg>
                  )}
                </span>
              </div>
              <div className={`${styles.wrongPassDialogue} ${isPasswordValid ? styles.successMessage : ""}`}>
                {passDialogue || "\u00A0"}
              </div>
              <div className={styles.password}>
                <label className={styles.nameLabel} htmlFor="confirm_password">
                  Confirm password
                </label>
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  maxLength={260}
                  id="confirm_password"
                  value={confirmPassword}
                  onChange={handleConfirmPassword}
                  placeholder="Passwords should match"
                  className={isPasswordsMatch ? styles.inputSuccess : ""}
                  required
                />
                <span
                  className={styles.hidePass}
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {!showConfirmPassword ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 640 512"
                      width={20}
                      height={20}
                    >
                      <path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2S-1.2 34.7 9.2 42.9l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L525.6 386.7c39.6-40.6 66.4-86.1 79.9-118.4c3.3-7.9 3.3-16.7 0-24.6c-14.9-35.7-46.2-87.7-93-131.1C465.5 68.8 400.8 32 320 32c-68.2 0-125 26.3-169.3 60.8L38.8 5.1zM223.1 149.5C248.6 126.2 282.7 112 320 112c79.5 0 144 64.5 144 144c0 24.9-6.3 48.3-17.4 68.7L408 294.5c8.4-19.3 10.6-41.4 4.8-63.3c-11.1-41.5-47.8-69.4-88.6-71.1c-5.8-.2-9.2 6.1-7.4 11.7c2.1 6.4 3.3 13.2 3.3 20.3c0 10.2-2.4 19.8-6.6 28.3l-90.3-70.8zM373 389.9c-16.4 6.5-34.3 10.1-53 10.1c-79.5 0-144-64.5-144-144c0-6.9 .5-13.6 1.4-20.2L83.1 161.5C60.3 191.2 44 220.8 34.5 243.7c-3.3 7.9-3.3 16.7 0 24.6c14.9 35.7 46.2 87.7 93 131.1C174.5 443.2 239.2 480 320 480c47.8 0 89.9-12.9 126.2-32.5L373 389.9z" />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 576 512"
                      width={20}
                      height={20}
                    >
                      <path d="M288 80c-65.2 0-118.8 29.6-159.9 67.7C89.6 183.5 63 226 49.4 256c13.6 30 40.2 72.5 78.6 108.3C169.2 402.4 222.8 432 288 432s118.8-29.6 159.9-67.7C486.4 328.5 513 286 526.6 256c-13.6-30-40.2-72.5-78.6-108.3C406.8 109.6 353.2 80 288 80zM95.4 112.6C142.5 68.8 207.2 32 288 32s145.5 36.8 192.6 80.6c46.8 43.5 78.1 95.4 93 131.1c3.3 7.9 3.3 16.7 0 24.6c-14.9 35.7-46.2 87.7-93 131.1C433.5 443.2 368.8 480 288 480s-145.5-36.8-192.6-80.6C48.6 356 17.3 304 2.5 268.3c-3.3-7.9-3.3-16.7 0-24.6C17.3 208 48.6 156 95.4 112.6zM288 336c44.2 0 80-35.8 80-80s-35.8-80-80-80c-.7 0-1.3 0-2 0c1.3 5.1 2 10.5 2 16c0 35.3-28.7 64-64 64c-5.5 0-10.9-.7-16-2c0 .7 0 1.3 0 2c0 44.2 35.8 80 80 80zm0-208a128 128 0 1 1 0 256 128 128 0 1 1 0-256z" />
                    </svg>
                  )}
                </span>
              </div>
              <div className={`${styles.wrongPassDialogue} ${isPasswordsMatch ? styles.successMessage : ""}`}>
                {confirmPassDialogue || "\u00A0"}
              </div>
            </div>
            <div className={styles.loginBtnContainer}>
              <button
                type="submit"
                className={`${styles.loginBtn} ${isPasswordsMatch && isPasswordValid ? styles.btnSuccess : ""}`}
                disabled={btnIsActive}
              >
                Reset Password
              </button>
            </div>
          </form>
          <ToastContainer />
        </div>
      </div>
      <RightSideAnimation />

    </main>
  );
};

export default Reset_Pass;
