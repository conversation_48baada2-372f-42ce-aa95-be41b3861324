import axios from "axios";
require("dotenv").config();
const BaseURL = process.env.NEXT_PUBLIC_Base_URL;
if (typeof window !== "undefined") {
  var token = sessionStorage.getItem("user");
}
export const getHelpListApi = async () => {
  const res = await axios({
    url: `${BaseURL}/get-helpdesk-list/`,
    method: "GET",
  });
  return res;
};

export const helpDeskPostApi = async (Data) => {
  const res = await axios({
    url: `${BaseURL}/add-helpdesk-query/`,
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
    },
    data: Data,
  });
  return res;
};

export const getHelpQuery = async () => {
  const res = await axios({
    url: `${BaseURL}/get-helpdesk-query/?active=true`,
    method: "GET",
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
  return res;
};
