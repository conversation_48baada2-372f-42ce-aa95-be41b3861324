"use client";
import React from "react";
import styles from "./table.module.css";
import SearchResultsComp from "./page";

const SearchResultsTable = ({ data, availableLiquidity }) => {
  return (
    <div className={styles.tableWrapper}>
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead className={styles.tableHead}>
            <tr>
              <th className={styles.tableHeader}>
                <div className={styles.headerContent}>
                  <span className={styles.headerLabel}>Trader</span>
                  <span className={styles.headerSubLabel}>Advertiser</span>
                </div>
              </th>
              <th className={styles.tableHeader}>
                <div className={styles.headerContent}>
                  <span className={styles.headerLabel}>Rate</span>
                  <span className={styles.headerSubLabel}>Price</span>
                </div>
              </th>
              <th className={styles.tableHeader}>
                <div className={styles.headerContent}>
                  <span className={styles.headerLabel}>Range</span>
                  <span className={styles.headerSubLabel}>Limits</span>
                </div>
              </th>
              <th className={styles.tableHeader}>
                <div className={styles.headerContent}>
                  <span className={styles.headerLabel}>Liquidity</span>
                  <span className={styles.headerSubLabel}>Available</span>
                </div>
              </th>
              <th className={styles.tableHeader}>
                <div className={styles.headerContent}>
                  <span className={styles.headerLabel}>Method</span>
                  <span className={styles.headerSubLabel}>Payment</span>
                </div>
              </th>
              <th className={styles.tableHeader}>
                <div className={styles.headerContent}>
                  <span className={styles.headerLabel}>Action</span>
                  <span className={styles.headerSubLabel}>Trade</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody className={styles.tableBody}>
            {data && data.length ? (
              data.map((el, index) => (
                <SearchResultsComp
                  key={index}
                  listingId={el.lsiting_id}
                  name={el.user_username}
                  // name={`User Id${el.user?.id || ""}`}
                  available_liquidity={el.available_liquidity}
                  min_liquidity={el.min_liquidity}
                  max_liquidity={el.max_liquidity}
                  payIn_option={el.payin_option?.payment_method}
                  payOut_option={el.payout_option?.payment_method}
                  rate={el.final_trade_fee?.toFixed(2)}
                  terms={el.terms_and_conditions}
                  time={el.updated_date}
                  payIn_currency={el.currency_accepted?.currency_code}
                  payOut_currency={el.currency_payout?.currency_code}
                  time_limit={el.time_limit}
                  amount={availableLiquidity}
                />
              ))
            ) : (
              <tr>
                <td colSpan="6" className={styles.emptyState}>
                  <div className={styles.emptyContent}>
                    <div className={styles.emptyIcon}>
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
                        <path
                          d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z"
                          stroke="#6b7280"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                    <h3 className={styles.emptyTitle}>No trades found</h3>
                    <p className={styles.emptyDescription}>
                      Try adjusting your search criteria or check back later for new listings.
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SearchResultsTable; 