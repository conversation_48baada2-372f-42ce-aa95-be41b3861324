import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  FlatList,
  ActivityIndicator,
  Switch,
} from "react-native";
import { Picker } from "@react-native-picker/picker";
import Layout from "../components/Layout";
import customFetchWithToken from "./utils/axiosInterceptor";
import Toast, { BaseToast } from "react-native-toast-message";
import {
  useCurrencyAcceptedList,
  useCurrencyPayoutList,
} from "@/hooks/useCountryList";
import ListingCardComponent from "../components/ListingCard";

interface TradeDetails {
  available_liquidity: number;
  country_from_id: number | null;
  country_to_id: number | null;
  created_date: string;
  currency_accepted: {
    currency_code: string;
    currency_name: string;
    id: number;
  };
  currency_payout: {
    currency_code: string;
    currency_name: string;
    id: number;
  };
  details: string | null;
  final_trade_fee: number;
  id: number;
  indicative_fx_rate: number;
  is_deleted: boolean;
  max_liquidity: number;
  min_liquidity: number;
  payin_option: {
    id: number;
    payment_method: string;
  };
  payout_option: {
    id: number;
    payment_method: string;
  };
  terms_and_conditions: string;
  time_limit: number;
  total_orders: number;
  total_reviews_percentage: number;
  trade_fee: number;
  unique_id: string;
  updated_date: string;
  user: {
    email: string;
    firstname: string;
    id: number;
    lastname: string;
  };
  user_payment_option_id: number | null;
}

interface Currency {
  currency_code: string;
  currency_name: string;
  currency_type: string;
  id: number;
}

const toastConfig = {
  success: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "green", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{ fontSize: 14, fontWeight: "bold", marginLeft: 20 }}
    />
  ),
  error: (props: any) => (
    <BaseToast
      {...props}
      style={{ borderLeftColor: "red", width: "90%" }}
      contentContainerStyle={{ paddingHorizontal: 5 }}
      text1Style={{ fontSize: 14, fontWeight: "bold", marginLeft: 20 }}
    />
  ),
};

export default function MyListings() {
  const { loadCurrencyFrom } = useCurrencyAcceptedList();
  const { loadCurrencyTo } = useCurrencyPayoutList();
  const [activeTab, setActiveTab] = useState("all");
  const [selectedCurrency1, setSelectedCurrency1] = useState("");
  const [selectedCurrency2, setSelectedCurrency2] = useState("");
  const [listingId, setListingId] = useState("");
  const [fetchListResults, setFetchListingResults] = useState<TradeDetails[]>(
    []
  );
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const [isDisabledListings, setIsDisabledListings] = useState(false);
  const [isTabLoading, setIsTabLoading] = useState(false);

  const getActiveListings = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setIsTabLoading(true);
      setFetchListingResults([]);
    }
    setIsFetching(true);

    try {
      const url = `/get-listings/?page=${
        reset ? 1 : page
      }&user_data=true&active=true`;
      const res = await customFetchWithToken.get(url);

      const filteredListings = res.data.results.filter(
        (listing: TradeDetails) => listing.is_deleted === false
      );

      setFetchListingResults((prev) =>
        reset ? filteredListings : [...prev, ...filteredListings]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setIsTabLoading(false);
      setIsFetching(false);
    }
  };

  const getDisabledListingsOnly = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setIsTabLoading(true);
      setFetchListingResults([]);
    }
    setIsFetching(true);

    try {
      const url = `/get-listings/?page=${
        reset ? 1 : page
      }&user_data=true&active=false`;
      const res = await customFetchWithToken.get(url);

      const filteredListings = res.data.results.filter(
        (listing: TradeDetails) => listing.is_deleted === true
      );

      setFetchListingResults((prev) =>
        reset ? filteredListings : [...prev, ...filteredListings]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setIsTabLoading(false);
      setIsFetching(false);
    }
  };

  const getAllListings = async (reset = false) => {
    if (isFetching || (!hasMore && !reset)) return;
    if (reset) {
      setIsTabLoading(true);
      setFetchListingResults([]);
    }
    setIsFetching(true);

    try {
      const url = `/get-listings/?page=${reset ? 1 : page}&user_data=true`;
      const res = await customFetchWithToken.get(url);

      setFetchListingResults((prev) =>
        reset ? res.data.results : [...prev, ...res.data.results]
      );
      setHasMore(res.data.next !== null);
      setPage((prev) => (reset ? 2 : prev + 1));
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setIsTabLoading(false);
      setIsFetching(false);
    }
  };

  const toggleListing = async (id: number) => {
    setFetchListingResults((prev) =>
      prev.map((listing) =>
        listing.id === id
          ? { ...listing, is_deleted: !listing.is_deleted }
          : listing
      )
    );

    try {
      const res = await customFetchWithToken.delete(`/delete-listings/${id}`);
      Toast.show({ type: "success", text1: res.data.message, position: "top" });
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error.response?.data?.message || "Error updating listing",
        position: "top",
      });
    }
  };

  const disableAllListingsFunc = async () => {
    try {
      const res = await customFetchWithToken.delete(
        "/disabled-enabled-listings/?flag=disable"
      );
      Toast.show({ type: "success", text1: res.data.message, position: "top" });
      setActiveTab("active");
      setPage(1);
      setHasMore(true);
      getActiveListings(true);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error.response?.data?.message || "Error disabling listings",
        position: "top",
      });
    }
  };

  const enableAllListingsFunc = async () => {
    try {
      const res = await customFetchWithToken.delete(
        "/disabled-enabled-listings/?flag=enable"
      );
      Toast.show({ type: "success", text1: res.data.message, position: "top" });
      setActiveTab("active");
      setPage(1);
      setHasMore(true);
      getActiveListings(true);
    } catch (error: any) {
      Toast.show({
        type: "error",
        text1: error.response?.data?.message || "Error enabling listings",
        position: "top",
      });
    }
  };

  const fetchListingsByCurrency = async () => {
    setLoading(true);
    try {
      const res = await customFetchWithToken.get(
        `/get-listings/?currency_accepted=${selectedCurrency1}&currency_payout=${selectedCurrency2}&id=${listingId}`
      );
      setFetchListingResults(res.data.results);
      setHasMore(false);
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === "active") {
      getActiveListings(true);
    } else if (activeTab === "disabled") {
      getDisabledListingsOnly(true);
    } else {
      getAllListings(true);
    }
  }, [activeTab]);

  const renderHeader = () => (
    <>
      <View className="flex-row justify-between items-center mb-4">
        <Text className="text-2xl font-bold">My Listings</Text>
      </View>

      <View className="flex-row bg-gray-200 rounded-lg p-1 mb-4">
        {["all", "active", "disabled"].map((tab) => (
          <TouchableOpacity
            key={tab}
            className={`flex-1 py-2 rounded-md ${
              activeTab === tab ? "bg-white" : ""
            }`}
            onPress={() => {
              setActiveTab(tab);
              setPage(1);
              setHasMore(true);
            }}
          >
            <Text className="text-center capitalize">{tab} Listings</Text>
          </TouchableOpacity>
        ))}
      </View>
      <View className="flex-row justify-between mb-4">
        <TouchableOpacity
          className="bg-red-500 px-6 py-3 rounded-lg"
          onPress={disableAllListingsFunc}
        >
          <Text className="text-white font-semibold">Disable All Listings</Text>
        </TouchableOpacity>

        <TouchableOpacity
          className="bg-green-600 px-6 py-3 rounded-lg"
          onPress={enableAllListingsFunc}
        >
          <Text className="text-white font-semibold">Enable All Listings</Text>
        </TouchableOpacity>
      </View>
      <View className="bg-white p-4 rounded-lg mb-4">
        <Text className="text-md font-semibold mb-4">Filter Listings</Text>

        <View className="mb-4 border border-gray-300 rounded-md">
          <Picker
            selectedValue={selectedCurrency1}
            onValueChange={setSelectedCurrency1}
          >
            <Picker.Item label="Select currency accepted" value="" />
            {loadCurrencyFrom.map((currency: Currency) => (
              <Picker.Item
                key={currency.id}
                label={currency.currency_code}
                value={currency.currency_code}
              />
            ))}
          </Picker>
        </View>

        <View className="mb-4 border border-gray-300 rounded-md">
          <Picker
            selectedValue={selectedCurrency2}
            onValueChange={setSelectedCurrency2}
          >
            <Picker.Item label="Select payout currency" value="" />
            {loadCurrencyTo.map((currency: Currency) => (
              <Picker.Item
                key={currency.id}
                label={currency.currency_code}
                value={currency.currency_code}
              />
            ))}
          </Picker>
        </View>

        <TextInput
          className="border border-gray-300 rounded-md p-2 mb-4"
          placeholder="Listing ID (optional)"
          maxLength={10}
          value={listingId}
          onChangeText={setListingId}
        />

        <TouchableOpacity
          className="bg-blue-500 py-3 rounded-lg"
          onPress={fetchListingsByCurrency}
        >
          <Text className="text-white text-center font-semibold">Search</Text>
        </TouchableOpacity>
      </View>
    </>
  );

  return (
    <Layout>
      <View className="flex-1 bg-gray-100 p-4">
        <FlatList
          data={fetchListResults}
          keyExtractor={(item, index) => String(index)}
          ListHeaderComponent={renderHeader()}
          ListEmptyComponent={
            isTabLoading ? (
              <Text className="text-md text-center font-pmedium mt-4">
                Loading...
              </Text>
            ) : (
              <Text className="text-md text-center font-pmedium mt-4">
                No listings available
              </Text>
            )
          }
          renderItem={({ item }) => (
            <ListingCardComponent
              listing={item}
              toggleListing={toggleListing}
            />
          )}
          ListFooterComponent={
            isFetching && !isTabLoading ? (
              <View className="py-4">
                <ActivityIndicator size="small" color="#0000ff" />
              </View>
            ) : null
          }
          onEndReached={() => {
            if (activeTab === "active") {
              hasMore && getActiveListings();
            } else if (activeTab === "disabled") {
              hasMore && getDisabledListingsOnly();
            } else {
              hasMore && getAllListings();
            }
          }}
          onEndReachedThreshold={0.5}
          initialNumToRender={10}
          maxToRenderPerBatch={5}
          windowSize={7}
          updateCellsBatchingPeriod={50}
          removeClippedSubviews={true}
        />
        <Toast config={toastConfig} />
      </View>
    </Layout>
  );
}
