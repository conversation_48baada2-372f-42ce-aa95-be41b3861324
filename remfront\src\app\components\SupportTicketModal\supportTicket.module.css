.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modalContent {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modalHeader {
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modalTitle {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.closeButton {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  border-radius: 8px;
  color: #6b7280;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover:not(:disabled) {
  background: #f3f4f6;
  color: #374151;
}

.closeButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.modalBody {
  padding: 24px;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.required {
  color: #ef4444;
}

.input,
.textarea {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  transition: all 0.2s ease;
  background: #ffffff;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:disabled,
.textarea:disabled {
  background: #f9fafb;
  color: #6b7280;
  cursor: not-allowed;
}

.inputError {
  border-color: #ef4444;
}

.inputError:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 120px;
  font-family: 'Poppins', sans-serif;
}

.errorMessage {
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  color: #ef4444;
  margin-top: 4px;
}

.characterCount {
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  color: #6b7280;
  text-align: right;
  margin-top: 4px;
}

.formActions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.cancelButton,
.submitButton {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  font-weight: 500;
  padding: 12px 24px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 44px;
}

.cancelButton {
  background: #f3f4f6;
  color: #374151;
  flex: 1;
}

.cancelButton:hover:not(:disabled) {
  background: #e5e7eb;
}

.submitButton {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: #ffffff;
  flex: 2;
}

.submitButton:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.submitButton:disabled,
.cancelButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.buttonIcon {
  font-size: 16px;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsiveness */
@media (max-width: 576px) {
  .modalOverlay {
    padding: 16px;
  }

  .modalContent {
    max-height: 95vh;
  }

  .modalHeader {
    padding: 20px 20px 16px 20px;
  }

  .modalTitle {
    font-size: 18px;
  }

  .modalBody {
    padding: 20px;
  }

  .formActions {
    flex-direction: column;
  }

  .cancelButton,
  .submitButton {
    flex: 1;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modalContent {
    border: 2px solid #000000;
  }

  .input,
  .textarea {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .modalOverlay,
  .modalContent,
  .closeButton,
  .input,
  .textarea,
  .cancelButton,
  .submitButton {
    animation: none;
    transition: none;
  }

  .spinner {
    animation: none;
  }
} 