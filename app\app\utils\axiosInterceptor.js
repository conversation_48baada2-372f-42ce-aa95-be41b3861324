import axios from "axios";
import refreshTokenApi from "../api/onboarding/refreshTokenEndpoint";
import * as SecureStore from "expo-secure-store";
import { Alert } from "react-native";

const Base_url = process.env.EXPO_PUBLIC_Base_URL;
let authToken;

const sessionLogoutHandler = async () => {
  try {
    console.log("Logged out successfully!");
    setTimeout(async () => {
      await SecureStore.deleteItemAsync("user");
      Alert.alert("Session expired", "Please log in again.");
    }, 1500);
  } catch (error) {
    console.error(error);
    console.log("Please try again.");
  }
};

const getAuthToken = async () => {
  const token = await SecureStore.getItemAsync("user");
  return token || "";
};

// Initialize authToken from SecureStore
(async () => {
  authToken = await getAuthToken();
})();

const customFetchWithToken = axios.create({
  baseURL: Base_url,
  headers: {
    Authorization: `Bearer ${authToken}`,
  },
  withCredentials: true,
});

let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};


 const refreshApiCall = async () => {
  try {
    const res = await refreshTokenApi();
    await SecureStore.setItemAsync("user", res.data.data.access_token);
    return res;
  } catch (error) {
    if (error) {
      sessionLogoutHandler();
    }
    console.log(error);
    throw error;
  }
};

customFetchWithToken.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    if (
      error.response &&
      error.response.status === 401 &&
      !originalRequest._retry
    ) {
      if (!isRefreshing) {
        isRefreshing = true;
        originalRequest._retry = true;
        try {
          const res = await refreshApiCall();
          authToken = res.data.data.access_token;
          customFetchWithToken.defaults.headers.Authorization = `Bearer ${authToken}`;
          processQueue(null, authToken);
          originalRequest.headers.Authorization = `Bearer ${authToken}`;
          return customFetchWithToken(originalRequest);
        } catch (err) {
          processQueue(err, null);
          return Promise.reject(err);
        } finally {
          isRefreshing = false;
        }
      } else {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers.Authorization = `Bearer ${token}`;
            return customFetchWithToken(originalRequest);
          })
          .catch((err) => {
            return Promise.reject(err);
          });
      }
    }
    return Promise.reject(error);
  }
);

export default customFetchWithToken;

