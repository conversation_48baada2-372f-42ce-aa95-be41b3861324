"use client";
import { useState, useEffect } from "react";
import styles from "./twoFacor.module.css";
import { submitQRToken } from "@/app/api/2fa/generateToken";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useRouter } from "next/navigation";
import { useTimer } from "@/app/context/TimerContext";

const page = () => {
  const router = useRouter();
  const { startTimer } = useTimer();

  const [secOtp, setSecOtp] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Get user email on component mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const email = localStorage.getItem("userEmail");
      if (!email) {
        toast.error("User email not found. Please login again.");
        router.push("/sign/login");
        return;
      }
      setUserEmail(email);
    }
  }, [router]);
  const verifyQRTokenFn = async (e) => {
    e.preventDefault();

    // Validation
    if (!secOtp.trim()) {
      toast.error("Please enter the authentication code");
      return;
    }

    if (!userEmail) {
      toast.error("User email not found. Please login again.");
      router.push("/sign/login");
      return;
    }

    setIsLoading(true);

    try {
      const res = await submitQRToken(secOtp, userEmail);

      if (res.status === 200) {
        toast.success("2FA authentication successful");

        // Store user data
        localStorage.setItem("userID", res.data.data.user_id);
        localStorage.setItem("userName", res.data.data.firstname);
        localStorage.setItem("user", res.data.data.tokens.access);
        localStorage.setItem("userEmail", res.data.data.user_email);
        localStorage.setItem("chatToken", res.data.data.chat_token);
        localStorage.setItem("refreshToken", res.data.data.tokens.refresh);
        localStorage.setItem("verificationStatus", res.data.data.flag);

        // Store session data
        sessionStorage.setItem("user", res.data.data.tokens.access);
        // Set 2FA status to true since user successfully completed 2FA login
        sessionStorage.setItem("two_factor_enabled", "true");

        setTimeout(function () {
          router.push("/pages/searchads");
        }, 1500);
        startTimer();
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || "Authentication failed. Please try again.";
      toast.error(errorMessage);
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={styles.bodyWrapper}>
      <div className={styles.container}>
        <div className={styles.title}>Remflow</div>
        <div className={styles.subTitle}>2FA authentication</div>

        <div className={styles.email}>
          <label className={styles.nameLabel} htmlFor="otp">
            Please enter the authentication code to sign In
          </label>
          <input
            type="text"
            id="otp"
            maxLength={6}
            value={secOtp}
            onChange={(e) => setSecOtp(e.target.value)}
            placeholder="Enter your authentication OTP from the App"
            required
            disabled={isLoading}
          />
        </div>
        <div className={styles.loginBtnContainer}>
          <button
            type="submit"
            className={styles.loginBtn}
            onClick={verifyQRTokenFn}
            disabled={isLoading || !secOtp.trim()}
          >
            {isLoading ? "Verifying..." : "Submit"}
          </button>
        </div>
        <ToastContainer />
      </div>
    </div>
  );
};

export default page;
