"use client";
import React from "react";
import styles from "../upload.module.css";

const page = ({ openModal2, closeModal }) => {
  return (
    <>
      <div className={styles.modalHeaderCont}>
        <h2
          ref={(subtitle) => (subtitle = subtitle)}
          className={styles.modalHeader}
        >
          Trade Details
          {/* <button>close</button> */}
        </h2>
        <svg
          style={{ marginLeft: "30px", cursor: "pointer" }}
          onClick={closeModal}
          xmlns="http://www.w3.org/2000/svg"
          width="15"
          height="16"
          viewBox="0 0 15 16"
          fill="none"
        >
          <path
            d="M8.60216 7.983L14.8197 1.78213C14.9425 1.63904 15.0067 1.45497 14.9994 1.26672C14.9921 1.07847 14.9139 0.899895 14.7803 0.766681C14.6468 0.633468 14.4677 0.555427 14.2789 0.548156C14.0902 0.540885 13.9056 0.604918 13.7622 0.727459L7.54466 6.92833L1.32716 0.719979C1.18593 0.579129 0.994384 0.5 0.794658 0.5C0.594931 0.5 0.403385 0.579129 0.262158 0.719979C0.12093 0.860829 0.0415888 1.05186 0.0415888 1.25105C0.0415888 1.45025 0.12093 1.64128 0.262158 1.78213L6.48716 7.983L0.262158 14.1839C0.183646 14.2509 0.119881 14.3334 0.0748636 14.4262C0.0298461 14.519 0.00454851 14.6201 0.000558893 14.7231C-0.00343073 14.8261 0.0139734 14.9289 0.0516793 15.0249C0.0893852 15.1208 0.146579 15.208 0.21967 15.2809C0.292761 15.3538 0.380171 15.4109 0.476415 15.4485C0.572659 15.4861 0.675658 15.5034 0.778948 15.4994C0.882237 15.4955 0.983586 15.4702 1.07664 15.4253C1.16968 15.3804 1.25242 15.3168 1.31966 15.2385L7.54466 9.03767L13.7622 15.2385C13.9056 15.3611 14.0902 15.4251 14.2789 15.4178C14.4677 15.4106 14.6468 15.3325 14.7803 15.1993C14.9139 15.0661 14.9921 14.8875 14.9994 14.6993C15.0067 14.511 14.9425 14.327 14.8197 14.1839L8.60216 7.983Z"
            fill="#858585"
          />
        </svg>
      </div>
      {/* <div>I am a modal</div> */}
      <div className={styles.issueSelect}>Select the type of issue</div>
      <div className={styles.optionsBox}>
        <div className={styles.inputBoxes}>
          <input type="radio" />
          <span className={styles.options}>Funds not received in time</span>
        </div>
        <div className={styles.inputBoxes}>
          <input type="radio" />
          <span className={styles.options}>3rd party payment</span>
        </div>
        <div className={styles.inputBoxes}>
          <input type="radio" />
          <span className={styles.options}>Adjust amount (underpaid)</span>
        </div>
        <div className={styles.inputBoxes}>
          <input type="radio" />
          <span className={styles.options}>Suspicious behaviour</span>
        </div>
        <div className={styles.inputBoxes}>
          <input type="radio" />
          <span className={styles.options}>Other</span>
        </div>
        <div className={styles.inputTextArea}>
          <input
            maxLength={260}
            type="textarea"
            placeholder="Write about the suspicious behaviour"
          />
        </div>
      </div>

      <div className={styles.submitBtnWrapper} onClick={openModal2}>
        <button className={styles.submitBtn}>Submit</button>
      </div>
    </>
  );
};

export default page;
