/* Modern Dispute Dropdown Form Styles */

.disputeFormContainer {
  background: #ffffff;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  margin: 1.5rem 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  border: 1px solid #e2e8f0;
  width: 100%;
  max-width: 100%;
}

/* Form Header */
.formHeader {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  padding: 1.5rem;
  color: #ffffff;
}

.headerContent {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.headerIcon {
  width: 2.5rem;
  height: 2.5rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.headerIcon svg {
  width: 1.5rem;
  height: 1.5rem;
  color: #ffffff;
}

.headerText {
  flex: 1;
}

.formTitle {
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0 0 0.25rem 0;
  color: #ffffff;
  line-height: 1.4;
}

.formDescription {
  font-size: 0.875rem;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}

/* Form Content */
.formContent {
  padding: 2rem;
}

.disputeForm {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Field Groups */
.fieldGroup {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.fieldLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.labelIcon {
  width: 1rem;
  height: 1rem;
  color: #6b7280;
  flex-shrink: 0;
}

.required {
  color: #ef4444;
  font-weight: 700;
}

/* Select Field */
.selectWrapper {
  position: relative;
}

.selectField {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: #ffffff;
  font-size: 0.875rem;
  color: #374151;
  transition: all 0.2s ease-in-out;
  appearance: none;
  cursor: pointer;
  min-height: 44px;
}

.selectField:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.selectField:hover {
  border-color: #9ca3af;
}

.selectIcon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.25rem;
  height: 1.25rem;
  color: #6b7280;
  pointer-events: none;
}

.selectIcon svg {
  width: 100%;
  height: 100%;
}

/* Upload Area */
.uploadArea {
  position: relative;
  border: 2px dashed #d1d5db;
  border-radius: 0.75rem;
  padding: 2rem;
  text-align: center;
  background: #f9fafb;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.uploadArea:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.uploadArea.dragActive {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.02);
}

.fileInput {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.fileInput.disabled {
  pointer-events: none;
}

.uploadContent {
  width: 100%;
}

.uploadPrompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.uploadIcon {
  width: 3rem;
  height: 3rem;
  color: #9ca3af;
}

.uploadIcon svg {
  width: 100%;
  height: 100%;
}

.uploadText {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.uploadPrimary {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

.uploadSecondary {
  font-size: 0.875rem;
  color: #6b7280;
}

.uploadHint {
  font-size: 0.75rem;
  color: #9ca3af;
}

.uploadSuccess {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 0.5rem;
}

.successIcon {
  width: 2rem;
  height: 2rem;
  background: #10b981;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.successIcon svg {
  width: 1.25rem;
  height: 1.25rem;
  color: #ffffff;
}

.uploadedFileInfo {
  flex: 1;
  text-align: left;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.fileName {
  font-size: 0.875rem;
  font-weight: 600;
  color: #374151;
  word-break: break-all;
}

.fileSize {
  font-size: 0.75rem;
  color: #6b7280;
}

.removeFileBtn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  color: #6b7280;
  transition: all 0.2s ease-in-out;
  flex-shrink: 0;
}

.removeFileBtn:hover {
  background: #f3f4f6;
  color: #ef4444;
}

.removeFileBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.removeFileBtn svg {
  width: 1.25rem;
  height: 1.25rem;
}

/* Textarea Field */
.textareaWrapper {
  position: relative;
}

.textareaField {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: #ffffff;
  font-size: 0.875rem;
  color: #374151;
  font-family: inherit;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease-in-out;
  min-height: 120px;
}

.textareaField:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textareaField:hover {
  border-color: #9ca3af;
}

.textareaField::placeholder {
  color: #9ca3af;
  font-style: normal;
}

.characterCount {
  position: absolute;
  bottom: 0.75rem;
  right: 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  background: rgba(255, 255, 255, 0.9);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  backdrop-filter: blur(4px);
}

/* Error States */
.fieldError {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.errorMessage {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #ef4444;
  font-size: 0.75rem;
  font-weight: 500;
}

.errorIcon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.helpText {
  color: #6b7280;
  font-size: 0.75rem;
  line-height: 1.4;
}


/* Submit Section */
.submitSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.submitBtn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  width: 100%;
  padding: 0.875rem 1.5rem;
  background: #3b82f6;
  color: #ffffff;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 48px;
}

.submitBtn:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.submitBtn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.submitBtn:active:not(:disabled) {
  transform: translateY(0);
}

.submitBtn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submitBtn.submitting {
  background: #6b7280;
}

.submitIcon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

.spinner {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  flex-shrink: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.submitNote {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  color: #6b7280;
  font-size: 0.75rem;
  text-align: center;
}

.noteIcon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .disputeFormContainer {
    margin: 0.75rem 0;
    border-radius: 0.75rem;
  }

  .formHeader {
    padding: 1rem 1.25rem;
  }

  .headerContent {
    gap: 0.75rem;
  }

  .headerIcon {
    width: 2rem;
    height: 2rem;
  }

  .headerIcon svg {
    width: 1.25rem;
    height: 1.25rem;
  }

  .formTitle {
    font-size: 1.125rem;
  }

  .formDescription {
    font-size: 0.8125rem;
  }

  .formContent {
    padding: 1.25rem;
  }

  .disputeForm {
    gap: 1.25rem;
  }

  .uploadArea {
    padding: 1.25rem;
    min-height: 100px;
  }

  .uploadIcon {
    width: 2.5rem;
    height: 2.5rem;
  }

  .uploadPrimary {
    font-size: 0.875rem;
  }

  .uploadSecondary {
    font-size: 0.8125rem;
  }

  .uploadSuccess {
    flex-direction: row;
    text-align: left;
    gap: 0.75rem;
  }

  .uploadedFileInfo {
    text-align: left;
  }
}

@media (max-width: 480px) {
  .disputeFormContainer {
    margin: 0.5rem 0;
    border-radius: 0.5rem;
    width: 100%;
    max-width: none;
  }

  .formHeader {
    padding: 0.875rem 1rem;
  }

  .headerContent {
    gap: 0.5rem;
  }

  .formContent {
    padding: 0.875rem;
  }

  .disputeForm {
    gap: 1rem;
  }

  .fieldGroup {
    gap: 0.5rem;
  }

  .fieldLabel {
    font-size: 0.8125rem;
  }

  .selectField,
  .textareaField {
    font-size: 0.8125rem;
    padding: 0.625rem;
  }

  .selectField {
    padding-right: 2.25rem;
  }

  .uploadArea {
    padding: 0.875rem;
    min-height: 80px;
  }

  .uploadIcon {
    width: 2rem;
    height: 2rem;
  }

  .uploadPrimary {
    font-size: 0.8125rem;
  }

  .uploadSecondary {
    font-size: 0.75rem;
  }

  .uploadHint {
    font-size: 0.6875rem;
  }

  .uploadSuccess {
    padding: 0.75rem;
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .uploadedFileInfo {
    text-align: center;
  }

  .submitBtn {
    padding: 0.75rem 1rem;
    font-size: 0.8125rem;
    min-height: 44px;
  }

  .submitNote {
    font-size: 0.6875rem;
  }

  .errorMessage,
  .helpText {
    font-size: 0.6875rem;
  }

  .characterCount {
    font-size: 0.6875rem;
    bottom: 0.5rem;
    right: 0.5rem;
  }
}

/* Extra small mobile screens */
@media (max-width: 360px) {
  .disputeFormContainer {
    margin: 0.25rem 0;
    border-radius: 0.375rem;
  }

  .formHeader {
    padding: 0.75rem;
  }

  .formContent {
    padding: 0.75rem;
  }

  .disputeForm {
    gap: 0.875rem;
  }

  .fieldGroup {
    gap: 0.375rem;
  }

  .selectField,
  .textareaField {
    padding: 0.5rem;
    font-size: 0.8125rem;
  }

  .selectField {
    padding-right: 2rem;
  }

  .uploadArea {
    padding: 0.75rem;
    min-height: 70px;
  }

  .uploadIcon {
    width: 1.75rem;
    height: 1.75rem;
  }

  .submitBtn {
    padding: 0.625rem 0.875rem;
    font-size: 0.8125rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .selectField,
  .textareaField,
  .uploadArea {
    border-width: 2px;
  }

  .submitBtn {
    border: 2px solid #3b82f6;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .disputeFormContainer,
  .selectField,
  .textareaField,
  .uploadArea,
  .submitBtn,
  .removeFileBtn,
  .spinner {
    transition: none;
    animation: none;
  }

  .submitBtn:hover:not(:disabled) {
    transform: none;
  }

  .uploadArea.dragActive {
    transform: none;
  }
}

/* Focus and accessibility improvements */
.selectField:focus-visible,
.textareaField:focus-visible,
.submitBtn:focus-visible,
.removeFileBtn:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .disputeFormContainer {
    box-shadow: none;
    border: 1px solid #000000;
  }

  .formHeader {
    background: #000000 !important;
    color: #ffffff !important;
  }

  .submitBtn {
    display: none;
  }
}