"use client";
import React from "react";
import styles from "./spinner.module.css";

const LoadingSpinner = ({
  text = "Processing...",
  size = "medium"
}) => {
  return (
    <div className={styles.spinnerContainer}>
      <div className={`${styles.spinnerWrapper} ${styles[size]}`}>
        {/* Outer rotating ring */}
        <div className={styles.outerRing}></div>

        {/* Middle pulsing ring */}
        <div className={styles.middleRing}></div>

        {/* Inner spinning dots */}
        <div className={styles.innerDots}>
          <div className={styles.dot}></div>
          <div className={styles.dot}></div>
          <div className={styles.dot}></div>
        </div>

        {/* Central financial icon */}
        <div className={styles.centerIcon}>
          <svg viewBox="0 0 24 24" className={styles.iconSvg}>
            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" />
          </svg>
        </div>
      </div>

      {text && (
        <div className={styles.textContainer}>
          <p className={styles.spinnerText}>{text}</p>
          <div className={styles.progressDots}>
            <span className={styles.progressDot}></span>
            <span className={styles.progressDot}></span>
            <span className={styles.progressDot}></span>
          </div>
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
