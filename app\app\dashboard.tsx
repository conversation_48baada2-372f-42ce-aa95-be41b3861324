import { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  TextInput,
  Button,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import FontAwesome6 from "@expo/vector-icons/FontAwesome6";
import { toastConfig, showToastError, showToastSuccess } from "@/hooks/toast";
import Toast, { BaseToast } from "react-native-toast-message";
import Layout from "../components/Layout";
import TransactionTable from "../components/TransactionTable";
import customFetchWithToken from "./utils/axiosInterceptor";
import { useRouter } from "expo-router";
import * as SecureStore from "expo-secure-store";
import * as Clipboard from "expo-clipboard";
import AntDesign from "@expo/vector-icons/AntDesign";

interface dashboardData {
  total_transaction_volumes: number;
  registered_users_count: number;
  ad_listing_count: number;
}

const dashboard = () => {
  const router = useRouter();
  const [transactionData, setTransactionData] = useState([]);
  const [dashboardData, setDashboardData] = useState<dashboardData>({
    total_transaction_volumes: 0,
    registered_users_count: 0,
    ad_listing_count: 0,
  });
  const [timeRange, setTimeRange] = useState("all");
  const [refCode, setRefCode] = useState("");
  const [refEmail, setRefEmail] = useState("");
  const [copied, setCopied] = useState(false);

  const userEmail = SecureStore.getItem("userEmail");
  const userName = SecureStore.getItem("userName");

  const ReferalBodyData = {
    email: userEmail,
    name: userName,
  };

  const copyToClipboard = async () => {
    await Clipboard.setStringAsync(refCode);
    showToastSuccess("Copied to clipboard");
  };

  const handleRefCodeGeneration = async () => {
    try {
      const res = await customFetchWithToken.post(
        "/referral-code/",
        ReferalBodyData
      );
      showToastSuccess(res.data.message);
      setRefCode(res.data.referral_link);
    } catch (error) {
      console.log(error);
    }
  };
  const getTransactions = async () => {
    try {
      const res = await customFetchWithToken.get("/get-transaction/");

      setTransactionData(res.data.results);
    } catch (error) {
      console.log(error);
    }
  };
  const fetchDashboardData = async () => {
    try {
      const res = await customFetchWithToken.get("/dashboard/");

      setDashboardData(res.data);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getTransactions();
  }, []);
  useEffect(() => {
    fetchDashboardData();
  }, []);

  return (
    <Layout>
      <Text className="text-left ml-8 my-3 text-lg font-psemitBold">
        DashBoard
      </Text>
      <View className="flex justify-center items-center">
        <View className="flex flex-row justify-center items-center bg-indigo-300/30 w-[90%] h-32 rounded-md">
          <View className="w-[30%] justify-center items-center flex flex-1">
            <View className="bg-white p-4 rounded-full">
              <FontAwesome6
                name="money-bill-transfer"
                size={24}
                color="black"
              />
            </View>
          </View>
          <View className="w-[70%]  justify-center items-start flex ">
            <View>
              <Text className="text-md font-pmedium">
                Total Transaction Volume
              </Text>
            </View>
            <View>
              <Text className="text-lg font-pmedium text-blue-800">
                $ {dashboardData.total_transaction_volumes}
              </Text>
            </View>
          </View>
        </View>
        <View className="flex flex-row justify-between items-center w-[90%]  rounded-md my-3">
          <View className="w-[48%] flex justify-around h-40 rounded-md bg-[#ffefdb80] p-2">
            <View className="flex justify-center items-start rounded-full">
              <View className="flex justify-center items-start bg-white p-4 rounded-full">
                <FontAwesome6 name="users" size={24} color="black" />
              </View>
            </View>
            <Text className="text-sm font-pmedium text-gray-800">
              Total Users Registered
            </Text>
            <Text className="text-lg font-pmedium text-gray-800">
              {dashboardData.registered_users_count}
            </Text>
          </View>
          {/* here */}
          <View className="w-[48%] flex justify-around h-40 rounded-md bg-green-300/50 p-2">
            <View className="flex justify-center items-start rounded-full">
              <View className="flex justify-center items-start bg-white p-4 rounded-full">
                {/* <Ionicons name="filter" size={24} color="#000" /> */}
                <FontAwesome6 name="sheet-plastic" size={24} color="black" />
              </View>
            </View>
            <Text className="text-sm font-pmedium text-gray-800">
              Total Ads Listed
            </Text>
            <Text className="text-lg font-pmedium text-gray-800">
              {dashboardData?.ad_listing_count}
            </Text>
          </View>
          {/* here */}
        </View>
        <View className="flex flex-row justify-center items-center bg-indigo-300/30 w-[90%] py-5 rounded-md">
          <View className="w-[30%] justify-center items-center flex flex-1">
            <View className="bg-white p-4 rounded-full">
              <Ionicons className="" name="filter" size={24} color="#000" />
            </View>
          </View>
          {refCode ? (
            <View className="mt-4 w-[70%]  justify-center items-start flex ">
              <Text className="text-sm font-pmedium text-gray-800">
                Your referal Code is
              </Text>
              <Text className="text-sm font-pmedium text-blue-800">
                {refCode}{" "}
                <AntDesign
                  name="copy1"
                  size={20}
                  color="black"
                  onPress={copyToClipboard}
                />
              </Text>
            </View>
          ) : (
            <View className="w-[70%]  justify-center items-start flex ">
              <TouchableOpacity
                className="border p-2 rounded-md my-2"
                onPress={handleRefCodeGeneration}
              >
                <Text className="text-sm font-pmedium">
                  Generare referral code
                </Text>
              </TouchableOpacity>
              <View>
                <Text className="text-sm font-pmedium text-blue-800">
                  Click the Button to generate a referal Link
                </Text>
              </View>
            </View>
          )}
        </View>
        {/* the chatGpT genetared code goes here */}
      </View>
      <View className="w-[95%] mx-auto">
        <TransactionTable />
      </View>
      <Toast config={toastConfig} />
    </Layout>
  );
};

export default dashboard;
